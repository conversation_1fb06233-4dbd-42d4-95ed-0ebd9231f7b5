#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试墙体分组算法的具体问题
"""

import sys
import os
import math
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test_grouping_comprehensive import create_test_entities
from cad_data_processor import CADDataProcessor

def debug_precise_connection_algorithm():
    """调试精确连接算法"""
    print("🔍 调试精确连接算法")
    print("="*60)
    
    # 创建处理器
    processor = CADDataProcessor()
    
    # 创建简单的测试数据 - 应该连接的4条墙体线条
    wall_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (6000, 0)], 'color': 7},      # 底边
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(6000, 0), (6000, 4000)], 'color': 7}, # 右边
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(6000, 4000), (0, 4000)], 'color': 7}, # 顶边
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 4000), (0, 0)], 'color': 7},       # 左边
    ]
    
    print(f"测试数据: {len(wall_entities)} 个墙体实体 (应该形成1个组)")
    for i, entity in enumerate(wall_entities):
        points = entity['points']
        print(f"  墙体 {i+1}: {points[0]} -> {points[1]}")
    
    # 手动测试连接检测
    print(f"\n🔧 手动测试连接检测:")
    
    for i in range(len(wall_entities)):
        for j in range(i + 1, len(wall_entities)):
            entity1 = wall_entities[i]
            entity2 = wall_entities[j]
            
            # 测试连接检测
            is_connected = processor._are_entities_connected_by_endpoints(entity1, entity2, 10)
            
            # 手动计算距离
            points1 = entity1['points']
            points2 = entity2['points']
            start1, end1 = points1[0], points1[1]
            start2, end2 = points2[0], points2[1]
            
            distances = [
                math.sqrt((start1[0] - start2[0])**2 + (start1[1] - start2[1])**2),
                math.sqrt((start1[0] - end2[0])**2 + (start1[1] - end2[1])**2),
                math.sqrt((end1[0] - start2[0])**2 + (end1[1] - start2[1])**2),
                math.sqrt((end1[0] - end2[0])**2 + (end1[1] - end2[1])**2)
            ]
            min_distance = min(distances)
            
            print(f"  墙体{i+1} <-> 墙体{j+1}: 连接检测={is_connected}, 最小距离={min_distance}")
    
    # 测试分组算法
    print(f"\n🔧 测试分组算法:")
    
    groups = processor._group_entities_by_precise_connection(wall_entities, threshold=10)
    print(f"分组结果: {len(groups)} 个组 (期望: 1个)")
    
    for i, group in enumerate(groups):
        print(f"  组 {i+1}: {len(group)} 个实体")
        for j, entity in enumerate(group):
            points = entity['points']
            print(f"    实体 {j+1}: {points[0]} -> {points[1]}")
    
    return groups

def debug_graph_building():
    """调试图构建过程"""
    print(f"\n🔍 调试图构建过程")
    print("="*60)
    
    # 创建处理器
    processor = CADDataProcessor()
    
    # 创建简单的测试数据
    wall_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'color': 7},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'color': 7},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 100), (0, 100)], 'color': 7},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 100), (0, 0)], 'color': 7},
    ]
    
    print(f"测试数据: {len(wall_entities)} 个实体")
    
    # 手动构建连接图
    print(f"\n🔗 手动构建连接图:")
    
    connections = {}
    for i in range(len(wall_entities)):
        connections[i] = []
    
    for i in range(len(wall_entities)):
        for j in range(i + 1, len(wall_entities)):
            entity1 = wall_entities[i]
            entity2 = wall_entities[j]
            
            # 检查连接
            is_connected = processor._are_entities_connected_by_endpoints(entity1, entity2, 10)
            
            if is_connected:
                connections[i].append(j)
                connections[j].append(i)
                print(f"  实体 {i} 连接到 实体 {j}")
    
    print(f"\n连接图:")
    for node, neighbors in connections.items():
        print(f"  节点 {node}: 连接到 {neighbors}")
    
    # 手动查找连通分量
    print(f"\n🔍 查找连通分量:")
    
    visited = set()
    components = []
    
    def dfs(node, component):
        if node in visited:
            return
        visited.add(node)
        component.append(node)
        for neighbor in connections[node]:
            dfs(neighbor, component)
    
    for i in range(len(wall_entities)):
        if i not in visited:
            component = []
            dfs(i, component)
            components.append(component)
            print(f"  连通分量 {len(components)}: {component}")
    
    print(f"\n总连通分量数: {len(components)} (期望: 1)")
    
    return components

def test_connection_method_directly():
    """直接测试连接方法"""
    print(f"\n🔧 直接测试连接方法")
    print("="*60)
    
    # 创建处理器
    processor = CADDataProcessor()
    
    # 创建两个应该连接的实体
    entity1 = {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'color': 7}
    entity2 = {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'color': 7}
    
    print(f"实体1: {entity1['points'][0]} -> {entity1['points'][1]}")
    print(f"实体2: {entity2['points'][0]} -> {entity2['points'][1]}")
    
    # 测试不同的连接检测方法
    print(f"\n测试连接检测方法:")
    
    try:
        # 方法1: 端点连接检测
        result1 = processor._are_entities_connected_by_endpoints(entity1, entity2, 10)
        print(f"  _are_entities_connected_by_endpoints: {result1}")
    except Exception as e:
        print(f"  _are_entities_connected_by_endpoints: 错误 - {e}")
    
    try:
        # 方法2: 一般连接检测
        result2 = processor._are_entities_connected(entity1, entity2, 10)
        print(f"  _are_entities_connected: {result2}")
    except Exception as e:
        print(f"  _are_entities_connected: 错误 - {e}")
    
    # 手动计算端点距离
    points1 = entity1['points']
    points2 = entity2['points']
    start1, end1 = points1[0], points1[1]
    start2, end2 = points2[0], points2[1]
    
    distances = [
        math.sqrt((start1[0] - start2[0])**2 + (start1[1] - start2[1])**2),
        math.sqrt((start1[0] - end2[0])**2 + (start1[1] - end2[1])**2),
        math.sqrt((end1[0] - start2[0])**2 + (end1[1] - start2[1])**2),
        math.sqrt((end1[0] - end2[0])**2 + (end1[1] - end2[1])**2)
    ]
    
    print(f"\n手动计算端点距离:")
    print(f"  start1 <-> start2: {distances[0]}")
    print(f"  start1 <-> end2: {distances[1]}")
    print(f"  end1 <-> start2: {distances[2]}")
    print(f"  end1 <-> end2: {distances[3]}")
    print(f"  最小距离: {min(distances)}")
    print(f"  应该连接: {min(distances) <= 10}")

def main():
    """主函数"""
    print("🚀 开始调试墙体分组算法")
    
    # 1. 直接测试连接方法
    test_connection_method_directly()
    
    # 2. 调试图构建过程
    components = debug_graph_building()
    
    # 3. 调试精确连接算法
    groups = debug_precise_connection_algorithm()
    
    print(f"\n📊 调试总结:")
    print(f"  连通分量数: {len(components) if 'components' in locals() else 'N/A'}")
    print(f"  分组数: {len(groups) if 'groups' in locals() else 'N/A'}")
    
    if len(components) == 1 and len(groups) > 1:
        print(f"  ⚠️ 问题: 连通分量正确但分组错误")
        print(f"  可能原因: 分组算法实现有问题")
    elif len(components) > 1:
        print(f"  ⚠️ 问题: 连接检测有问题")
        print(f"  可能原因: 连接检测方法实现有问题")

if __name__ == "__main__":
    main()
