#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整的三阶段处理测试代码
包含多个多图层线条的测试数据，测试整个处理流程
"""

import os
import sys
import json
import time
import threading
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from tkinter import messagebox, ttk
from main_enhanced_with_v2_fill import EnhancedCADAppV2

class ComprehensiveTestSuite:
    """完整测试套件"""
    
    def __init__(self):
        self.test_folder = "test_data"
        self.test_results = []
        self.current_test = None
        self.app = None
        self.root = None
        
    def create_test_data(self):
        """创建测试数据文件夹和DXF文件"""
        print("🔧 创建测试数据...")
        
        # 创建测试文件夹
        if not os.path.exists(self.test_folder):
            os.makedirs(self.test_folder)
        
        # 创建多个测试DXF文件的模拟数据
        test_files = [
            "building_plan_01.dxf",
            "building_plan_02.dxf", 
            "building_plan_03.dxf"
        ]
        
        for file_name in test_files:
            self.create_mock_dxf_data(file_name)
        
        print(f"✅ 测试数据创建完成，共 {len(test_files)} 个文件")
        return self.test_folder
    
    def create_mock_dxf_data(self, file_name):
        """创建模拟DXF数据文件"""
        file_path = os.path.join(self.test_folder, file_name)
        
        # 创建模拟的实体数据（JSON格式，模拟DXF解析结果）
        mock_entities = {
            "entities": [
                # 墙体图层 - 外墙
                {
                    "type": "LINE",
                    "layer": "WALL_OUTER",
                    "points": [(0, 0), (100, 0)],
                    "color": 1,
                    "linetype": "CONTINUOUS"
                },
                {
                    "type": "LINE", 
                    "layer": "WALL_OUTER",
                    "points": [(100, 0), (100, 80)],
                    "color": 1,
                    "linetype": "CONTINUOUS"
                },
                {
                    "type": "LINE",
                    "layer": "WALL_OUTER", 
                    "points": [(100, 80), (0, 80)],
                    "color": 1,
                    "linetype": "CONTINUOUS"
                },
                {
                    "type": "LINE",
                    "layer": "WALL_OUTER",
                    "points": [(0, 80), (0, 0)],
                    "color": 1,
                    "linetype": "CONTINUOUS"
                },
                
                # 墙体图层 - 内墙
                {
                    "type": "LINE",
                    "layer": "WALL_INNER",
                    "points": [(30, 0), (30, 50)],
                    "color": 2,
                    "linetype": "CONTINUOUS"
                },
                {
                    "type": "LINE",
                    "layer": "WALL_INNER", 
                    "points": [(70, 0), (70, 50)],
                    "color": 2,
                    "linetype": "CONTINUOUS"
                },
                
                # 门窗图层
                {
                    "type": "ARC",
                    "layer": "DOOR_WINDOW",
                    "center": (15, 0),
                    "radius": 15,
                    "start_angle": 0,
                    "end_angle": 90,
                    "color": 3,
                    "linetype": "CONTINUOUS"
                },
                {
                    "type": "RECTANGLE",
                    "layer": "DOOR_WINDOW",
                    "points": [(45, 78), (55, 82)],
                    "color": 3,
                    "linetype": "CONTINUOUS"
                },
                
                # 栏杆图层
                {
                    "type": "LWPOLYLINE",
                    "layer": "RAILING",
                    "points": [(20, 60), (25, 60), (25, 65), (20, 65), (20, 60)],
                    "closed": True,
                    "color": 4,
                    "linetype": "DASHED"
                },
                
                # 家具图层
                {
                    "type": "CIRCLE",
                    "layer": "FURNITURE",
                    "center": (40, 40),
                    "radius": 8,
                    "color": 5,
                    "linetype": "CONTINUOUS"
                },
                {
                    "type": "RECTANGLE",
                    "layer": "FURNITURE", 
                    "points": [(60, 30), (80, 50)],
                    "color": 5,
                    "linetype": "CONTINUOUS"
                },
                
                # 尺寸标注图层
                {
                    "type": "TEXT",
                    "layer": "DIMENSION",
                    "position": (50, 90),
                    "text": "10000",
                    "height": 2.5,
                    "color": 6
                },
                {
                    "type": "LINE",
                    "layer": "DIMENSION",
                    "points": [(0, 85), (100, 85)],
                    "color": 6,
                    "linetype": "CONTINUOUS"
                },
                
                # 文字标注图层
                {
                    "type": "TEXT",
                    "layer": "TEXT_LABEL",
                    "position": (15, 25),
                    "text": "客厅",
                    "height": 3,
                    "color": 7
                },
                {
                    "type": "TEXT",
                    "layer": "TEXT_LABEL",
                    "position": (50, 25),
                    "text": "卧室",
                    "height": 3,
                    "color": 7
                },
                
                # 其他图层 - 装饰线条
                {
                    "type": "SPLINE",
                    "layer": "DECORATION",
                    "control_points": [(10, 10), (20, 15), (30, 10), (40, 15)],
                    "color": 8,
                    "linetype": "DOT"
                }
            ],
            "layers": [
                {"name": "WALL_OUTER", "color": 1, "linetype": "CONTINUOUS"},
                {"name": "WALL_INNER", "color": 2, "linetype": "CONTINUOUS"},
                {"name": "DOOR_WINDOW", "color": 3, "linetype": "CONTINUOUS"},
                {"name": "RAILING", "color": 4, "linetype": "DASHED"},
                {"name": "FURNITURE", "color": 5, "linetype": "CONTINUOUS"},
                {"name": "DIMENSION", "color": 6, "linetype": "CONTINUOUS"},
                {"name": "TEXT_LABEL", "color": 7, "linetype": "CONTINUOUS"},
                {"name": "DECORATION", "color": 8, "linetype": "DOT"}
            ]
        }
        
        # 保存为JSON文件（模拟DXF数据）
        with open(file_path + ".json", 'w', encoding='utf-8') as f:
            json.dump(mock_entities, f, ensure_ascii=False, indent=2)
        
        # 创建空的DXF文件（占位符）
        with open(file_path, 'w') as f:
            f.write("# Mock DXF file for testing\n")
    
    def run_comprehensive_test(self):
        """运行完整测试"""
        print("🚀 开始完整测试流程")
        print("=" * 60)
        
        # 1. 创建测试数据
        test_folder = self.create_test_data()
        
        # 2. 启动应用
        self.start_application()
        
        # 3. 等待应用启动
        time.sleep(2)
        
        # 4. 执行测试序列
        self.execute_test_sequence(test_folder)
    
    def start_application(self):
        """启动应用"""
        print("🖥️ 启动应用...")
        
        def run_app():
            self.root = tk.Tk()
            self.app = EnhancedCADAppV2(self.root)
            
            # 设置测试模式标志
            self.app._test_mode = True
            
            # 启动主循环
            self.root.mainloop()
        
        # 在单独线程中运行应用
        app_thread = threading.Thread(target=run_app, daemon=True)
        app_thread.start()
    
    def execute_test_sequence(self, test_folder):
        """执行测试序列"""
        print("📋 执行测试序列...")
        
        test_steps = [
            ("选择测试文件夹", lambda: self.test_select_folder(test_folder)),
            ("测试阶段1：开始处理", self.test_stage1_basic_processing),
            ("验证基础数据显示", self.verify_basic_data_display),
            ("测试阶段2：线条处理", self.test_stage2_line_processing),
            ("验证线条处理结果", self.verify_line_processing_result),
            ("测试阶段3：识别分组", self.test_stage3_group_processing),
            ("验证分组结果", self.verify_group_processing_result),
            ("测试类别选择", self.test_category_selection),
            ("验证最终结果", self.verify_final_result)
        ]
        
        for step_name, step_func in test_steps:
            print(f"\n🔍 {step_name}...")
            try:
                result = step_func()
                self.test_results.append({
                    "step": step_name,
                    "status": "PASS" if result else "FAIL",
                    "timestamp": datetime.now().isoformat()
                })
                print(f"✅ {step_name} - {'通过' if result else '失败'}")
            except Exception as e:
                print(f"❌ {step_name} - 异常: {e}")
                self.test_results.append({
                    "step": step_name,
                    "status": "ERROR",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
        
        # 生成测试报告
        self.generate_test_report()
    
    def test_select_folder(self, test_folder):
        """测试选择文件夹"""
        if not self.app:
            return False
        
        # 模拟选择文件夹
        self.app.folder_var.set(test_folder)
        return os.path.exists(test_folder)
    
    def test_stage1_basic_processing(self):
        """测试阶段1：基础处理"""
        if not self.app:
            return False

        # 模拟点击开始处理按钮
        try:
            self.app.start_processing()
            time.sleep(3)  # 等待处理完成
            return hasattr(self.app, 'processing_stage') and self.app.processing_stage == "basic"
        except Exception as e:
            print(f"阶段1处理异常: {e}")
            return False

    def verify_basic_data_display(self):
        """验证基础数据显示"""
        if not self.app or not self.app.processor:
            return False

        try:
            # 检查是否有原始实体数据
            has_raw_entities = hasattr(self.app.processor, 'raw_entities') and self.app.processor.raw_entities

            # 检查可视化器是否更新
            visualizer_updated = self.app.visualizer is not None

            # 检查按钮状态
            line_btn_enabled = hasattr(self.app, 'line_process_btn') and str(self.app.line_process_btn['state']) == 'normal'

            print(f"  - 原始实体数据: {'✓' if has_raw_entities else '✗'}")
            print(f"  - 可视化器更新: {'✓' if visualizer_updated else '✗'}")
            print(f"  - 线条处理按钮启用: {'✓' if line_btn_enabled else '✗'}")

            return has_raw_entities and visualizer_updated and line_btn_enabled

        except Exception as e:
            print(f"验证基础数据显示异常: {e}")
            return False

    def test_stage2_line_processing(self):
        """测试阶段2：线条处理"""
        if not self.app:
            return False

        try:
            # 模拟点击线条处理按钮
            self.app.start_line_processing()
            time.sleep(3)  # 等待处理完成
            return hasattr(self.app, 'processing_stage') and self.app.processing_stage == "line"
        except Exception as e:
            print(f"阶段2处理异常: {e}")
            return False

    def verify_line_processing_result(self):
        """验证线条处理结果"""
        if not self.app or not self.app.processor:
            return False

        try:
            # 检查是否有合并后的实体数据
            has_merged_entities = hasattr(self.app.processor, 'merged_entities') and self.app.processor.merged_entities

            # 检查分组处理按钮是否启用
            group_btn_enabled = hasattr(self.app, 'group_process_btn') and str(self.app.group_process_btn['state']) == 'normal'

            print(f"  - 合并实体数据: {'✓' if has_merged_entities else '✗'}")
            print(f"  - 分组处理按钮启用: {'✓' if group_btn_enabled else '✗'}")

            return has_merged_entities and group_btn_enabled

        except Exception as e:
            print(f"验证线条处理结果异常: {e}")
            return False

    def test_stage3_group_processing(self):
        """测试阶段3：识别分组"""
        if not self.app:
            return False

        try:
            # 模拟点击识别分组按钮
            self.app.start_group_processing()
            time.sleep(3)  # 等待处理完成
            return hasattr(self.app, 'processing_stage') and self.app.processing_stage == "group"
        except Exception as e:
            print(f"阶段3处理异常: {e}")
            return False

    def verify_group_processing_result(self):
        """验证分组处理结果"""
        if not self.app or not self.app.processor:
            return False

        try:
            # 检查是否有分组数据
            has_groups = hasattr(self.app.processor, 'all_groups') and self.app.processor.all_groups

            # 检查组状态信息
            has_groups_info = hasattr(self.app.processor, 'groups_info') and self.app.processor.groups_info

            # 检查实体组列表是否更新
            group_list_updated = hasattr(self.app, 'group_tree') and self.app.group_tree

            # 检查处理阶段状态
            stage_complete = hasattr(self.app, 'processing_stage') and self.app.processing_stage in ["group", "complete"]

            print(f"  - 分组数据: {'✓' if has_groups else '✗'}")
            print(f"  - 组状态信息: {'✓' if has_groups_info else '✗'}")
            print(f"  - 实体组列表更新: {'✓' if group_list_updated else '✗'}")
            print(f"  - 处理阶段完成: {'✓' if stage_complete else '✗'}")

            if has_groups:
                print(f"  - 分组数量: {len(self.app.processor.all_groups)}")

            if has_groups_info:
                print(f"  - 组信息数量: {len(self.app.processor.groups_info)}")

            return has_groups and has_groups_info and group_list_updated and stage_complete

        except Exception as e:
            print(f"验证分组处理结果异常: {e}")
            return False

    def test_category_selection(self):
        """测试类别选择"""
        if not self.app or not self.app.processor or not hasattr(self.app.processor, 'all_groups'):
            return False

        try:
            # 模拟对不同组进行类别选择
            test_categories = ['wall', 'door_window', 'furniture', 'other']
            successful_selections = 0

            for i, category in enumerate(test_categories):
                if i < len(self.app.processor.all_groups):
                    try:
                        # 模拟选择组
                        if hasattr(self.app, 'select_group'):
                            self.app.select_group(i)

                        # 模拟选择类别
                        if hasattr(self.app, 'label_current_group'):
                            self.app.label_current_group(category)
                            successful_selections += 1
                            print(f"  - 组{i+1}标注为{category}: ✓")

                    except Exception as e:
                        print(f"  - 组{i+1}标注失败: {e}")

            return successful_selections > 0

        except Exception as e:
            print(f"测试类别选择异常: {e}")
            return False

    def verify_final_result(self):
        """验证最终结果"""
        if not self.app:
            return False

        try:
            # 检查各项功能是否正常
            checks = {
                "处理器存在": self.app.processor is not None,
                "可视化器存在": self.app.visualizer is not None,
                "有分组数据": hasattr(self.app.processor, 'all_groups') and self.app.processor.all_groups,
                "有组状态信息": hasattr(self.app.processor, 'groups_info') and self.app.processor.groups_info,
                "实体组列表存在": hasattr(self.app, 'group_tree'),
                "处理阶段完成": hasattr(self.app, 'processing_stage') and self.app.processing_stage in ["group", "complete"]
            }

            passed_checks = 0
            for check_name, result in checks.items():
                print(f"  - {check_name}: {'✓' if result else '✗'}")
                if result:
                    passed_checks += 1

            success_rate = passed_checks / len(checks)
            print(f"  - 总体成功率: {success_rate:.1%}")

            return success_rate >= 0.8  # 80%以上通过率认为成功

        except Exception as e:
            print(f"验证最终结果异常: {e}")
            return False

    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 测试报告")
        print("=" * 60)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['status'] == 'PASS')
        failed_tests = sum(1 for r in self.test_results if r['status'] == 'FAIL')
        error_tests = sum(1 for r in self.test_results if r['status'] == 'ERROR')

        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"错误: {error_tests}")
        print(f"成功率: {passed_tests/total_tests:.1%}")

        print("\n详细结果:")
        for result in self.test_results:
            status_icon = {"PASS": "✅", "FAIL": "❌", "ERROR": "💥"}[result['status']]
            print(f"{status_icon} {result['step']}: {result['status']}")
            if 'error' in result:
                print(f"   错误: {result['error']}")

        # 保存测试报告到文件
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": failed_tests,
                    "errors": error_tests,
                    "success_rate": passed_tests/total_tests
                },
                "details": self.test_results,
                "timestamp": datetime.now().isoformat()
            }, f, ensure_ascii=False, indent=2)

        print(f"\n📄 详细报告已保存到: {report_file}")


def main():
    """主函数"""
    print("🧪 CAD分类标注工具 - 完整测试套件")
    print("=" * 60)

    # 创建测试套件
    test_suite = ComprehensiveTestSuite()

    try:
        # 运行完整测试
        test_suite.run_comprehensive_test()

        # 等待用户查看结果
        input("\n按回车键退出测试...")

    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n🏁 测试结束")


if __name__ == "__main__":
    main()
