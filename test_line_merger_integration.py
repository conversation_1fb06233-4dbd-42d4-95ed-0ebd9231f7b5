#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试线段合并功能集成
验证DXF文件导入后的墙体图层线条简化处理
"""

import sys
import os
import time
import matplotlib.pyplot as plt
import numpy as np

def test_line_merger_standalone():
    """测试独立的线段合并功能"""
    print("🧪 测试独立线段合并功能...")
    
    try:
        from line_merger import SimpleLineMerger, DXFLineMerger
        
        # 创建测试线段数据
        test_lines = [
            # 可以合并的连续线段（水平）
            [(0, 0), (10, 0)],
            [(10, 0), (20, 0)],
            [(20, 0), (30, 0)],
            
            # 可以合并的连续线段（垂直）
            [(50, 0), (50, 10)],
            [(50, 10), (50, 20)],
            
            # 独立线段
            [(100, 100), (110, 110)],
            
            # 平行但不连接的线段
            [(0, 50), (10, 50)],
            [(20, 50), (30, 50)],
        ]
        
        print(f"  原始线段数量: {len(test_lines)}")
        
        # 测试简单合并器
        merger = SimpleLineMerger(distance_threshold=5, angle_threshold=2)
        merged_lines = merger.merge_lines(test_lines)
        
        print(f"  合并后线段数量: {len(merged_lines)}")
        merger.print_stats()
        
        # 测试DXF合并器
        dxf_merger = DXFLineMerger(distance_threshold=5, angle_threshold=2)
        
        # 创建模拟DXF实体
        test_entities = []
        for i, line in enumerate(test_lines):
            entity = {
                'type': 'LINE',
                'layer': 'WALL' if i < 5 else 'OTHER',  # 前5个是墙体线条
                'start_x': line[0][0],
                'start_y': line[0][1],
                'end_x': line[1][0],
                'end_y': line[1][1],
                'color': 7,
                'linetype': 'CONTINUOUS'
            }
            test_entities.append(entity)
        
        print(f"\n  测试DXF实体处理:")
        print(f"  原始实体数量: {len(test_entities)}")
        
        processed_entities = dxf_merger.process_entities(test_entities)
        print(f"  处理后实体数量: {len(processed_entities)}")
        
        dxf_merger.get_stats()
        
        return True
        
    except Exception as e:
        print(f"❌ 独立测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cad_processor_integration():
    """测试CAD数据处理器集成"""
    print("\n🧪 测试CAD数据处理器集成...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        # 创建处理器
        processor = CADDataProcessor()
        
        # 检查线段合并器是否已初始化
        if hasattr(processor, 'line_merger') and processor.line_merger:
            print("  ✅ 线段合并器已成功集成到CAD数据处理器")
            
            # 检查合并器配置
            merger = processor.line_merger
            print(f"  📋 配置参数:")
            print(f"    距离阈值: {merger.merger.dist_thresh}")
            print(f"    角度阈值: {merger.merger.angle_thresh}")
            print(f"    墙体图层: {merger.wall_layers}")
            
            return True
        else:
            print("  ❌ 线段合并器未正确集成")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_mock_dxf_data():
    """使用模拟DXF数据测试完整流程"""
    print("\n🧪 测试完整DXF处理流程...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        # 创建处理器
        processor = CADDataProcessor()
        
        if not (hasattr(processor, 'line_merger') and processor.line_merger):
            print("  ⚠️ 线段合并器不可用，跳过测试")
            return True
        
        # 创建模拟DXF实体数据
        mock_entities = []
        
        # 模拟墙体线条（可以合并）
        wall_lines = [
            # 水平墙体线段
            {'type': 'LINE', 'layer': 'WALL', 'start_x': 0, 'start_y': 0, 'end_x': 100, 'end_y': 0},
            {'type': 'LINE', 'layer': 'WALL', 'start_x': 100, 'start_y': 0, 'end_x': 200, 'end_y': 0},
            {'type': 'LINE', 'layer': 'WALL', 'start_x': 200, 'start_y': 0, 'end_x': 300, 'end_y': 0},
            
            # 垂直墙体线段
            {'type': 'LINE', 'layer': 'WALL', 'start_x': 0, 'start_y': 0, 'end_x': 0, 'end_y': 100},
            {'type': 'LINE', 'layer': 'WALL', 'start_x': 0, 'start_y': 100, 'end_x': 0, 'end_y': 200},
        ]
        
        # 模拟其他实体（不会被合并）
        other_entities = [
            {'type': 'CIRCLE', 'layer': 'FURNITURE', 'center_x': 150, 'center_y': 150, 'radius': 20},
            {'type': 'TEXT', 'layer': 'TEXT', 'text': 'Room 1', 'x': 100, 'y': 100},
            {'type': 'LINE', 'layer': 'DIMENSION', 'start_x': 50, 'start_y': 50, 'end_x': 150, 'end_y': 50},
        ]
        
        mock_entities = wall_lines + other_entities
        
        print(f"  模拟实体数量: {len(mock_entities)}")
        print(f"    墙体线条: {len(wall_lines)}")
        print(f"    其他实体: {len(other_entities)}")
        
        # 使用线段合并器处理
        processed_entities = processor.line_merger.process_entities(mock_entities)
        
        print(f"  处理后实体数量: {len(processed_entities)}")
        
        # 统计处理结果
        wall_count = sum(1 for e in processed_entities if e.get('layer') == 'WALL')
        other_count = len(processed_entities) - wall_count
        
        print(f"    处理后墙体实体: {wall_count}")
        print(f"    其他实体: {other_count}")
        
        # 检查是否有合并标记
        merged_count = sum(1 for e in processed_entities if e.get('merged', False))
        print(f"    标记为合并的实体: {merged_count}")
        
        # 打印统计信息
        stats = processor.line_merger.get_stats()
        if stats['merged_lines'] > 0:
            print(f"  📊 合并效果: 简化了 {stats['merged_lines']} 条线段")
            print(f"  ⏱️ 处理时间: {stats['processing_time']:.3f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_visualization_test():
    """创建可视化测试"""
    print("\n🎨 创建线段合并可视化测试...")
    
    try:
        from line_merger import SimpleLineMerger
        
        # 创建更复杂的测试数据
        test_lines = []
        
        # 创建一个简单的房间轮廓（可以合并的线段）
        room_outline = [
            [(0, 0), (50, 0)],      # 底边1
            [(50, 0), (100, 0)],    # 底边2
            [(100, 0), (100, 50)],  # 右边1
            [(100, 50), (100, 100)], # 右边2
            [(100, 100), (50, 100)], # 顶边1
            [(50, 100), (0, 100)],   # 顶边2
            [(0, 100), (0, 50)],     # 左边1
            [(0, 50), (0, 0)],       # 左边2
        ]
        
        # 添加一些内部线条
        internal_lines = [
            [(25, 0), (25, 100)],    # 垂直分隔线
            [(75, 0), (75, 100)],    # 垂直分隔线
            [(0, 25), (100, 25)],    # 水平分隔线
            [(0, 75), (100, 75)],    # 水平分隔线
        ]
        
        # 添加一些独立的线段
        isolated_lines = [
            [(120, 20), (140, 40)],  # 对角线
            [(120, 80), (140, 60)],  # 对角线
        ]
        
        test_lines = room_outline + internal_lines + isolated_lines
        
        print(f"  创建测试线段: {len(test_lines)} 条")
        print(f"    房间轮廓: {len(room_outline)} 条")
        print(f"    内部线条: {len(internal_lines)} 条")
        print(f"    独立线段: {len(isolated_lines)} 条")
        
        # 执行合并
        merger = SimpleLineMerger(distance_threshold=2, angle_threshold=1)
        merged_lines = merger.merge_lines(test_lines)
        
        print(f"  合并结果: {len(merged_lines)} 条线段")
        merger.print_stats()
        
        # 创建简单的文本可视化
        print("\n📊 合并效果预览:")
        print("  原始线段分布:")
        for i, line in enumerate(test_lines[:10]):  # 只显示前10条
            print(f"    线段{i+1}: {line[0]} -> {line[1]}")
        if len(test_lines) > 10:
            print(f"    ... 还有 {len(test_lines) - 10} 条线段")
        
        print("\n  合并后线段:")
        for i, line in enumerate(merged_lines[:10]):  # 只显示前10条
            if hasattr(line, 'coords'):
                coords = list(line.coords)
                print(f"    合并线段{i+1}: {coords[0]} -> {coords[-1]}")
            else:
                print(f"    合并线段{i+1}: {line[0]} -> {line[-1]}")
        if len(merged_lines) > 10:
            print(f"    ... 还有 {len(merged_lines) - 10} 条线段")
        
        return True
        
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始线段合并功能集成测试\n")
    
    test_results = []
    
    # 测试1: 独立功能测试
    result1 = test_line_merger_standalone()
    test_results.append(("独立功能测试", result1))
    
    # 测试2: CAD处理器集成测试
    result2 = test_cad_processor_integration()
    test_results.append(("CAD处理器集成", result2))
    
    # 测试3: 完整流程测试
    result3 = test_with_mock_dxf_data()
    test_results.append(("完整流程测试", result3))
    
    # 测试4: 可视化测试
    result4 = create_visualization_test()
    test_results.append(("可视化测试", result4))
    
    # 输出测试总结
    print("\n" + "="*60)
    print("📋 测试结果总结:")
    print("="*60)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(test_results)} 项测试通过")
    
    if passed == len(test_results):
        print("🎉 所有测试通过！线段合并功能集成成功")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    return passed == len(test_results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
