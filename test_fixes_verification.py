#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证修复后的功能测试脚本
"""

import os
import sys
import json
import time
import threading

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from tkinter import messagebox

def test_color_system():
    """测试配色系统"""
    print("🎨 测试配色系统...")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        root = tk.Tk()
        app = EnhancedCADAppV2(root)
        
        # 检查新增的颜色定义
        required_colors = ['other_lines', 'processing_lines']
        missing_colors = []
        
        for color in required_colors:
            if color not in app.current_color_scheme:
                missing_colors.append(color)
        
        if missing_colors:
            print(f"❌ 缺少颜色定义: {missing_colors}")
            return False
        else:
            print("✅ 所有必需的颜色都已定义")
            print(f"  - other_lines: {app.current_color_scheme['other_lines']}")
            print(f"  - processing_lines: {app.current_color_scheme['processing_lines']}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 配色系统测试失败: {e}")
        return False

def test_visualizer_methods():
    """测试可视化器方法"""
    print("🎨 测试可视化器方法...")
    
    try:
        from cad_visualizer import CADVisualizer
        
        visualizer = CADVisualizer()
        
        # 测试新增的方法
        test_entity = {
            'type': 'LINE',
            'layer': 'TEST_LAYER',
            'points': [(0, 0), (100, 100)],
            'processing': True
        }
        
        # 测试 _get_entity_color 方法
        color = visualizer._get_entity_color(test_entity)
        print(f"✅ _get_entity_color 方法正常，返回颜色: {color}")
        
        # 测试处理中的实体颜色
        test_entity_processing = test_entity.copy()
        test_entity_processing['processing'] = True
        color_processing = visualizer._get_entity_color(test_entity_processing)
        print(f"✅ 处理中实体颜色: {color_processing}")
        
        # 测试 draw_entities 方法
        test_entities = [test_entity]
        visualizer.draw_entities(test_entities)
        print("✅ draw_entities 方法正常")
        
        # 测试 draw_groups 方法
        test_groups = [[test_entity]]
        visualizer.draw_groups(test_groups)
        print("✅ draw_groups 方法正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 可视化器方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_processor_methods():
    """测试数据处理器方法"""
    print("🔧 测试数据处理器方法...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 测试 merge_lines 方法
        test_entities = [
            {'type': 'LINE', 'layer': 'TEST', 'points': [(0, 0), (100, 0)]},
            {'type': 'LINE', 'layer': 'TEST', 'points': [(100, 0), (200, 0)]}
        ]
        
        merged_entities = processor.merge_lines(test_entities)
        print(f"✅ merge_lines 方法正常，输入: {len(test_entities)}, 输出: {len(merged_entities)}")
        
        # 测试 group_entities 方法
        groups = processor.group_entities(merged_entities)
        print(f"✅ group_entities 方法正常，生成 {len(groups)} 个组")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据处理器方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_three_stage_processing():
    """测试三阶段处理流程"""
    print("🔄 测试三阶段处理流程...")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        root = tk.Tk()
        app = EnhancedCADAppV2(root)
        
        # 创建测试数据文件夹
        test_folder = "test_stage_processing"
        if not os.path.exists(test_folder):
            os.makedirs(test_folder)
        
        # 创建测试DXF文件
        test_file = os.path.join(test_folder, "test.dxf")
        with open(test_file, 'w') as f:
            f.write("# Test DXF file\n")
        
        # 设置文件夹
        app.folder_var.set(test_folder)
        
        # 测试阶段1：基础处理
        print("  测试阶段1：基础处理...")
        initial_stage = app.processing_stage
        if initial_stage != "none":
            print(f"❌ 初始阶段错误: {initial_stage}")
            return False
        
        # 测试按钮状态
        if str(app.start_btn['state']) != 'normal':
            print(f"❌ 开始按钮状态错误: {app.start_btn['state']}")
            return False
        
        if str(app.line_process_btn['state']) != 'disabled':
            print(f"❌ 线条处理按钮状态错误: {app.line_process_btn['state']}")
            return False
        
        if str(app.group_process_btn['state']) != 'disabled':
            print(f"❌ 分组处理按钮状态错误: {app.group_process_btn['state']}")
            return False
        
        print("✅ 三阶段处理流程初始状态正确")
        
        root.destroy()
        
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
        if os.path.exists(test_folder):
            os.rmdir(test_folder)
        
        return True
        
    except Exception as e:
        print(f"❌ 三阶段处理流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_line_processing_continuation():
    """测试线条处理后的继续流程"""
    print("🔄 测试线条处理继续流程...")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        # 创建模拟的可视化器和画布
        class MockVisualizer:
            def clear_all(self): pass
            def draw_entities(self, entities): pass
            def draw_groups(self, groups): pass
            def visualize_overview(self, **kwargs): pass
        
        class MockCanvas:
            def draw(self): pass
        
        processor = EnhancedCADProcessor(MockVisualizer(), MockCanvas())
        
        # 模拟原始实体数据
        processor.raw_entities = [
            {'type': 'LINE', 'layer': 'TEST', 'points': [(0, 0), (100, 0)]}
        ]
        
        # 测试线条处理方法
        success = processor.process_line_merging()
        
        # 无论成功与否，都应该有合并实体数据
        if not hasattr(processor, 'merged_entities'):
            print("❌ 线条处理后缺少合并实体数据")
            return False
        
        if not processor.merged_entities:
            print("❌ 合并实体数据为空")
            return False
        
        print(f"✅ 线条处理继续流程正常，合并实体数: {len(processor.merged_entities)}")
        
        # 测试分组处理
        success = processor.process_grouping()
        
        if not hasattr(processor, 'all_groups'):
            print("❌ 分组处理后缺少分组数据")
            return False
        
        print(f"✅ 分组处理正常，生成组数: {len(processor.all_groups)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 线条处理继续流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_all_tests():
    """运行所有测试"""
    print("🧪 开始验证修复后的功能")
    print("=" * 50)
    
    tests = [
        ("配色系统测试", test_color_system),
        ("可视化器方法测试", test_visualizer_methods),
        ("数据处理器方法测试", test_data_processor_methods),
        ("三阶段处理流程测试", test_three_stage_processing),
        ("线条处理继续流程测试", test_line_processing_continuation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"成功率: {passed/total:.1%}")
    
    if passed == total:
        print("\n🎉 所有测试通过！修复成功！")
        return True
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，需要进一步修复")
        return False

def main():
    """主函数"""
    print("🔧 CAD三阶段处理功能修复验证")
    print("=" * 50)
    
    try:
        success = run_all_tests()
        
        if success:
            print("\n✅ 所有修复验证通过")
        else:
            print("\n❌ 部分修复验证失败")
            
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n🏁 修复验证结束")

if __name__ == "__main__":
    main()
