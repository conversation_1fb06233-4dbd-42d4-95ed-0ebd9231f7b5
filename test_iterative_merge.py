#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试迭代合并功能
验证迭代合并对墙体线条合并效果的改进
"""

import sys
import os
import time
import matplotlib.pyplot as plt
import numpy as np

def test_iterative_vs_single_merge():
    """测试迭代合并与单次合并的效果对比"""
    print("🧪 测试迭代合并 vs 单次合并效果对比...")
    
    try:
        from line_merger import SimpleLineMerger
        
        # 创建复杂的测试线段数据（需要多次迭代才能完全合并）
        test_lines = [
            # 复杂的连续线段组合（需要多次迭代）
            [(0, 0), (10, 0)],      # 第1段
            [(10, 0), (20, 0)],     # 第2段 - 可与第1段合并
            [(20, 0), (30, 0)],     # 第3段 - 第2次迭代时可与前面合并
            [(30, 0), (40, 0)],     # 第4段 - 第3次迭代时可与前面合并
            
            # 垂直线段组合
            [(50, 0), (50, 10)],    # 垂直第1段
            [(50, 10), (50, 20)],   # 垂直第2段
            [(50, 20), (50, 30)],   # 垂直第3段
            
            # L形线段组合（需要多次迭代）
            [(100, 0), (110, 0)],   # L形底边1
            [(110, 0), (120, 0)],   # L形底边2
            [(120, 0), (120, 10)],  # L形右边1
            [(120, 10), (120, 20)], # L形右边2
            
            # 独立线段
            [(200, 200), (210, 210)],
            
            # 平行但不连接的线段
            [(0, 50), (10, 50)],
            [(20, 50), (30, 50)],
        ]
        
        print(f"  原始线段数量: {len(test_lines)}")
        
        # 测试单次合并
        print("\n📍 单次合并测试:")
        single_merger = SimpleLineMerger(
            distance_threshold=5, 
            angle_threshold=2, 
            enable_iterative=False
        )
        single_result = single_merger.merge_lines(test_lines.copy())
        single_merger.print_stats()
        
        # 测试迭代合并
        print("\n🔄 迭代合并测试:")
        iterative_merger = SimpleLineMerger(
            distance_threshold=5, 
            angle_threshold=2, 
            enable_iterative=True,
            max_iterations=3
        )
        iterative_result = iterative_merger.merge_lines(test_lines.copy())
        iterative_merger.print_stats()
        
        # 对比结果
        print("\n📊 效果对比:")
        print(f"  单次合并结果: {len(single_result)} 条线段")
        print(f"  迭代合并结果: {len(iterative_result)} 条线段")
        
        improvement = len(single_result) - len(iterative_result)
        if improvement > 0:
            print(f"  ✅ 迭代合并额外简化了 {improvement} 条线段")
            improvement_rate = (improvement / len(test_lines)) * 100
            print(f"  📈 额外简化率: {improvement_rate:.1f}%")
        elif improvement == 0:
            print(f"  📋 两种方法效果相同")
        else:
            print(f"  ⚠️ 异常：迭代合并结果更多")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_max_iterations_effect():
    """测试不同最大迭代次数的效果"""
    print("\n🧪 测试不同最大迭代次数的效果...")
    
    try:
        from line_merger import SimpleLineMerger
        
        # 创建需要多次迭代的复杂线段
        complex_lines = []
        
        # 创建一个需要多次迭代的复杂图形
        # 长链条线段（每次迭代只能合并相邻的）
        chain_length = 8
        for i in range(chain_length):
            complex_lines.append([(i * 10, 0), ((i + 1) * 10, 0)])
        
        # 垂直链条
        for i in range(6):
            complex_lines.append([(100, i * 10), (100, (i + 1) * 10)])
        
        print(f"  复杂线段数量: {len(complex_lines)}")
        
        # 测试不同的最大迭代次数
        max_iter_values = [1, 2, 3, 5]
        results = {}
        
        for max_iter in max_iter_values:
            print(f"\n📍 测试最大迭代次数: {max_iter}")
            
            merger = SimpleLineMerger(
                distance_threshold=5,
                angle_threshold=2,
                enable_iterative=True,
                max_iterations=max_iter
            )
            
            result = merger.merge_lines(complex_lines.copy())
            stats = merger.get_stats()
            
            results[max_iter] = {
                'final_count': len(result),
                'iterations_performed': stats['iterations_performed'],
                'processing_time': stats['processing_time']
            }
            
            print(f"  结果: {len(result)} 条线段")
            print(f"  实际迭代: {stats['iterations_performed']} 次")
            print(f"  处理时间: {stats['processing_time']:.3f}秒")
        
        # 分析结果
        print("\n📊 迭代次数效果分析:")
        print("  最大迭代次数 | 最终线段数 | 实际迭代次数 | 处理时间(秒)")
        print("  " + "-" * 55)
        
        for max_iter in max_iter_values:
            result = results[max_iter]
            print(f"  {max_iter:^12} | {result['final_count']:^10} | {result['iterations_performed']:^12} | {result['processing_time']:^11.3f}")
        
        # 找出最优配置
        best_result = min(results.values(), key=lambda x: x['final_count'])
        best_max_iter = [k for k, v in results.items() if v['final_count'] == best_result['final_count']][0]
        
        print(f"\n🎯 最优配置: 最大迭代次数 {best_max_iter}")
        print(f"  最终线段数: {best_result['final_count']}")
        print(f"  实际迭代次数: {best_result['iterations_performed']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 迭代次数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dxf_merger_iterative():
    """测试DXF合并器的迭代功能"""
    print("\n🧪 测试DXF合并器迭代功能...")
    
    try:
        from line_merger import DXFLineMerger
        
        # 创建模拟DXF实体（墙体线条）
        wall_entities = []
        
        # 创建复杂的墙体线条组合
        wall_segments = [
            # 水平墙体链条
            [(0, 0), (50, 0)],
            [(50, 0), (100, 0)],
            [(100, 0), (150, 0)],
            [(150, 0), (200, 0)],
            
            # 垂直墙体链条
            [(0, 0), (0, 50)],
            [(0, 50), (0, 100)],
            [(0, 100), (0, 150)],
            
            # 连接线段
            [(200, 0), (200, 50)],
            [(200, 50), (200, 100)],
        ]
        
        # 转换为DXF实体格式
        for i, (start, end) in enumerate(wall_segments):
            entity = {
                'type': 'LINE',
                'layer': 'WALL',
                'start_x': start[0],
                'start_y': start[1],
                'end_x': end[0],
                'end_y': end[1],
                'color': 7,
                'linetype': 'CONTINUOUS'
            }
            wall_entities.append(entity)
        
        # 添加一些非墙体实体
        other_entities = [
            {'type': 'CIRCLE', 'layer': 'FURNITURE', 'center_x': 100, 'center_y': 100, 'radius': 20},
            {'type': 'TEXT', 'layer': 'TEXT', 'text': 'Room', 'x': 50, 'y': 50},
        ]
        
        all_entities = wall_entities + other_entities
        
        print(f"  输入实体数量: {len(all_entities)}")
        print(f"    墙体线条: {len(wall_entities)}")
        print(f"    其他实体: {len(other_entities)}")
        
        # 测试非迭代模式
        print("\n📍 非迭代模式:")
        non_iter_merger = DXFLineMerger(
            distance_threshold=5,
            angle_threshold=2,
            enable_iterative=False
        )
        non_iter_result = non_iter_merger.process_entities(all_entities.copy())
        non_iter_stats = non_iter_merger.get_stats()
        
        # 测试迭代模式
        print("\n🔄 迭代模式:")
        iter_merger = DXFLineMerger(
            distance_threshold=5,
            angle_threshold=2,
            enable_iterative=True,
            max_iterations=3
        )
        iter_result = iter_merger.process_entities(all_entities.copy())
        iter_stats = iter_merger.get_stats()
        
        # 对比结果
        print("\n📊 DXF处理效果对比:")
        print(f"  非迭代模式: {len(non_iter_result)} 个实体")
        print(f"  迭代模式: {len(iter_result)} 个实体")
        
        improvement = len(non_iter_result) - len(iter_result)
        if improvement > 0:
            print(f"  ✅ 迭代模式额外简化了 {improvement} 个实体")
        elif improvement == 0:
            print(f"  📋 两种模式效果相同")
        else:
            print(f"  ⚠️ 异常：迭代模式结果更多")
        
        return True
        
    except Exception as e:
        print(f"❌ DXF合并器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cad_processor_integration():
    """测试CAD数据处理器的迭代合并集成"""
    print("\n🧪 测试CAD数据处理器迭代合并集成...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        # 创建处理器
        processor = CADDataProcessor()
        
        # 检查线段合并器配置
        if hasattr(processor, 'line_merger') and processor.line_merger:
            merger = processor.line_merger.merger
            
            print("  ✅ 线段合并器配置:")
            print(f"    距离阈值: {merger.dist_thresh}")
            print(f"    角度阈值: {merger.angle_thresh}")
            print(f"    迭代模式: {'启用' if merger.enable_iterative else '禁用'}")
            print(f"    最大迭代次数: {merger.max_iterations}")
            
            if merger.enable_iterative:
                print("  🎉 迭代合并功能已成功集成到CAD数据处理器")
                return True
            else:
                print("  ⚠️ 迭代合并功能未启用")
                return False
        else:
            print("  ❌ 线段合并器未正确集成")
            return False
            
    except Exception as e:
        print(f"❌ CAD处理器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始迭代合并功能测试\n")
    
    test_results = []
    
    # 测试1: 迭代 vs 单次合并对比
    result1 = test_iterative_vs_single_merge()
    test_results.append(("迭代vs单次合并对比", result1))
    
    # 测试2: 不同迭代次数效果
    result2 = test_max_iterations_effect()
    test_results.append(("不同迭代次数效果", result2))
    
    # 测试3: DXF合并器迭代功能
    result3 = test_dxf_merger_iterative()
    test_results.append(("DXF合并器迭代功能", result3))
    
    # 测试4: CAD处理器集成
    result4 = test_cad_processor_integration()
    test_results.append(("CAD处理器集成", result4))
    
    # 输出测试总结
    print("\n" + "="*60)
    print("📋 迭代合并功能测试结果总结:")
    print("="*60)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(test_results)} 项测试通过")
    
    if passed == len(test_results):
        print("🎉 所有测试通过！迭代合并功能集成成功")
        print("\n💡 迭代合并优势:")
        print("  - 能够处理复杂的多段连续线条")
        print("  - 自动优化合并次数，避免过度处理")
        print("  - 提供详细的迭代过程统计信息")
        print("  - 显著提高墙体线条简化效果")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    return passed == len(test_results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
