#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
问题检测测试脚本
专门用于检测三阶段处理过程中可能出现的问题
"""

import os
import sys
import json
import time
import traceback
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from tkinter import messagebox

class ProblemDetector:
    """问题检测器"""
    
    def __init__(self):
        self.problems = []
        self.warnings = []
        self.test_results = {}
    
    def detect_ui_problems(self, app):
        """检测UI相关问题"""
        print("🔍 检测UI问题...")
        
        ui_checks = [
            ("主窗口存在", lambda: app.root is not None),
            ("处理控制按钮存在", lambda: hasattr(app, 'start_btn')),
            ("线条处理按钮存在", lambda: hasattr(app, 'line_process_btn')),
            ("分组处理按钮存在", lambda: hasattr(app, 'group_process_btn')),
            ("停止按钮存在", lambda: hasattr(app, 'stop_btn')),
            ("状态显示存在", lambda: hasattr(app, 'status_var')),
            ("文件夹选择存在", lambda: hasattr(app, 'folder_var')),
            ("可视化器存在", lambda: app.visualizer is not None),
            ("画布存在", lambda: app.canvas is not None),
            ("实体组列表存在", lambda: hasattr(app, 'group_tree'))
        ]
        
        ui_problems = []
        for check_name, check_func in ui_checks:
            try:
                result = check_func()
                if not result:
                    ui_problems.append(f"UI组件缺失: {check_name}")
                    print(f"  ❌ {check_name}")
                else:
                    print(f"  ✅ {check_name}")
            except Exception as e:
                ui_problems.append(f"UI检查异常 {check_name}: {e}")
                print(f"  💥 {check_name}: {e}")
        
        self.test_results['ui_problems'] = ui_problems
        return len(ui_problems) == 0
    
    def detect_button_state_problems(self, app):
        """检测按钮状态问题"""
        print("🔍 检测按钮状态问题...")
        
        button_problems = []
        
        try:
            # 检查初始状态
            if hasattr(app, 'start_btn'):
                start_state = str(app.start_btn['state'])
                if start_state != 'normal':
                    button_problems.append(f"开始按钮初始状态错误: {start_state}")
                    print(f"  ❌ 开始按钮状态: {start_state}")
                else:
                    print(f"  ✅ 开始按钮状态: {start_state}")
            
            if hasattr(app, 'line_process_btn'):
                line_state = str(app.line_process_btn['state'])
                if line_state != 'disabled':
                    button_problems.append(f"线条处理按钮初始状态错误: {line_state}")
                    print(f"  ❌ 线条处理按钮状态: {line_state}")
                else:
                    print(f"  ✅ 线条处理按钮状态: {line_state}")
            
            if hasattr(app, 'group_process_btn'):
                group_state = str(app.group_process_btn['state'])
                if group_state != 'disabled':
                    button_problems.append(f"分组处理按钮初始状态错误: {group_state}")
                    print(f"  ❌ 分组处理按钮状态: {group_state}")
                else:
                    print(f"  ✅ 分组处理按钮状态: {group_state}")
            
            if hasattr(app, 'stop_btn'):
                stop_state = str(app.stop_btn['state'])
                if stop_state != 'disabled':
                    button_problems.append(f"停止按钮初始状态错误: {stop_state}")
                    print(f"  ❌ 停止按钮状态: {stop_state}")
                else:
                    print(f"  ✅ 停止按钮状态: {stop_state}")
                    
        except Exception as e:
            button_problems.append(f"按钮状态检查异常: {e}")
            print(f"  💥 按钮状态检查异常: {e}")
        
        self.test_results['button_problems'] = button_problems
        return len(button_problems) == 0
    
    def detect_processing_stage_problems(self, app):
        """检测处理阶段问题"""
        print("🔍 检测处理阶段问题...")
        
        stage_problems = []
        
        try:
            # 检查处理阶段状态
            if hasattr(app, 'processing_stage'):
                stage = app.processing_stage
                if stage != "none":
                    stage_problems.append(f"处理阶段初始状态错误: {stage}")
                    print(f"  ❌ 处理阶段状态: {stage}")
                else:
                    print(f"  ✅ 处理阶段状态: {stage}")
            else:
                stage_problems.append("处理阶段状态属性不存在")
                print(f"  ❌ 处理阶段状态属性不存在")
            
            # 检查阶段数据存储
            if hasattr(app, 'stage_data'):
                if not isinstance(app.stage_data, dict):
                    stage_problems.append(f"阶段数据类型错误: {type(app.stage_data)}")
                    print(f"  ❌ 阶段数据类型: {type(app.stage_data)}")
                else:
                    print(f"  ✅ 阶段数据类型: dict")
            else:
                stage_problems.append("阶段数据存储属性不存在")
                print(f"  ❌ 阶段数据存储属性不存在")
                
        except Exception as e:
            stage_problems.append(f"处理阶段检查异常: {e}")
            print(f"  💥 处理阶段检查异常: {e}")
        
        self.test_results['stage_problems'] = stage_problems
        return len(stage_problems) == 0
    
    def detect_processor_problems(self, app):
        """检测处理器问题"""
        print("🔍 检测处理器问题...")
        
        processor_problems = []
        
        try:
            # 检查处理器存在
            if app.processor is None:
                processor_problems.append("处理器未初始化")
                print(f"  ❌ 处理器未初始化")
                return False
            else:
                print(f"  ✅ 处理器已初始化")
            
            # 检查处理器方法
            required_methods = [
                'process_folder_basic',
                'process_line_merging', 
                'process_grouping',
                'set_callbacks'
            ]
            
            for method_name in required_methods:
                if hasattr(app.processor, method_name):
                    print(f"  ✅ 处理器方法存在: {method_name}")
                else:
                    processor_problems.append(f"处理器方法缺失: {method_name}")
                    print(f"  ❌ 处理器方法缺失: {method_name}")
            
            # 检查处理器属性
            processor_attrs = [
                'is_running',
                'should_stop',
                'all_groups',
                'groups_info'
            ]
            
            for attr_name in processor_attrs:
                if hasattr(app.processor, attr_name):
                    print(f"  ✅ 处理器属性存在: {attr_name}")
                else:
                    processor_problems.append(f"处理器属性缺失: {attr_name}")
                    print(f"  ❌ 处理器属性缺失: {attr_name}")
                    
        except Exception as e:
            processor_problems.append(f"处理器检查异常: {e}")
            print(f"  💥 处理器检查异常: {e}")
        
        self.test_results['processor_problems'] = processor_problems
        return len(processor_problems) == 0
    
    def detect_visualization_problems(self, app):
        """检测可视化问题"""
        print("🔍 检测可视化问题...")
        
        viz_problems = []
        
        try:
            # 检查可视化器
            if app.visualizer is None:
                viz_problems.append("可视化器未初始化")
                print(f"  ❌ 可视化器未初始化")
            else:
                print(f"  ✅ 可视化器已初始化")
                
                # 检查可视化器方法
                viz_methods = [
                    'clear_all',
                    'draw_entities',
                    'draw_groups',
                    'visualize_overview'
                ]
                
                for method_name in viz_methods:
                    if hasattr(app.visualizer, method_name):
                        print(f"  ✅ 可视化器方法存在: {method_name}")
                    else:
                        viz_problems.append(f"可视化器方法缺失: {method_name}")
                        print(f"  ❌ 可视化器方法缺失: {method_name}")
            
            # 检查画布
            if app.canvas is None:
                viz_problems.append("画布未初始化")
                print(f"  ❌ 画布未初始化")
            else:
                print(f"  ✅ 画布已初始化")
                
                # 检查画布方法
                if hasattr(app.canvas, 'draw'):
                    print(f"  ✅ 画布draw方法存在")
                else:
                    viz_problems.append("画布draw方法缺失")
                    print(f"  ❌ 画布draw方法缺失")
                    
        except Exception as e:
            viz_problems.append(f"可视化检查异常: {e}")
            print(f"  💥 可视化检查异常: {e}")
        
        self.test_results['visualization_problems'] = viz_problems
        return len(viz_problems) == 0
    
    def detect_data_flow_problems(self, app):
        """检测数据流问题"""
        print("🔍 检测数据流问题...")
        
        data_problems = []
        
        try:
            # 检查文件管理
            if hasattr(app, 'current_folder'):
                print(f"  ✅ 当前文件夹属性存在")
            else:
                data_problems.append("当前文件夹属性缺失")
                print(f"  ❌ 当前文件夹属性缺失")
            
            if hasattr(app, 'current_file'):
                print(f"  ✅ 当前文件属性存在")
            else:
                data_problems.append("当前文件属性缺失")
                print(f"  ❌ 当前文件属性缺失")
            
            if hasattr(app, 'all_files'):
                print(f"  ✅ 所有文件列表存在")
            else:
                data_problems.append("所有文件列表缺失")
                print(f"  ❌ 所有文件列表缺失")
            
            # 检查数据缓存
            if hasattr(app, 'file_data'):
                print(f"  ✅ 文件数据缓存存在")
            else:
                data_problems.append("文件数据缓存缺失")
                print(f"  ❌ 文件数据缓存缺失")
            
            if hasattr(app, 'file_status'):
                print(f"  ✅ 文件状态缓存存在")
            else:
                data_problems.append("文件状态缓存缺失")
                print(f"  ❌ 文件状态缓存缺失")
                
        except Exception as e:
            data_problems.append(f"数据流检查异常: {e}")
            print(f"  💥 数据流检查异常: {e}")
        
        self.test_results['data_flow_problems'] = data_problems
        return len(data_problems) == 0
    
    def run_comprehensive_check(self, app):
        """运行全面检查"""
        print("🔍 开始全面问题检测")
        print("=" * 50)
        
        checks = [
            ("UI组件检查", self.detect_ui_problems),
            ("按钮状态检查", self.detect_button_state_problems),
            ("处理阶段检查", self.detect_processing_stage_problems),
            ("处理器检查", self.detect_processor_problems),
            ("可视化检查", self.detect_visualization_problems),
            ("数据流检查", self.detect_data_flow_problems)
        ]
        
        passed_checks = 0
        total_checks = len(checks)
        
        for check_name, check_func in checks:
            print(f"\n{check_name}:")
            try:
                result = check_func(app)
                if result:
                    passed_checks += 1
                    print(f"✅ {check_name} 通过")
                else:
                    print(f"❌ {check_name} 失败")
            except Exception as e:
                print(f"💥 {check_name} 异常: {e}")
                traceback.print_exc()
        
        # 生成检查报告
        self.generate_check_report(passed_checks, total_checks)
        
        return passed_checks == total_checks
    
    def generate_check_report(self, passed, total):
        """生成检查报告"""
        print("\n" + "=" * 50)
        print("📊 问题检测报告")
        print("=" * 50)
        
        success_rate = passed / total * 100
        print(f"总检查项: {total}")
        print(f"通过检查: {passed}")
        print(f"失败检查: {total - passed}")
        print(f"成功率: {success_rate:.1f}%")
        
        # 详细问题报告
        if self.test_results:
            print("\n详细问题列表:")
            for category, problems in self.test_results.items():
                if problems:
                    print(f"\n{category}:")
                    for problem in problems:
                        print(f"  - {problem}")
        
        # 保存报告
        report_data = {
            "summary": {
                "total_checks": total,
                "passed_checks": passed,
                "failed_checks": total - passed,
                "success_rate": success_rate
            },
            "problems": self.test_results,
            "timestamp": datetime.now().isoformat()
        }
        
        report_file = f"problem_detection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: {report_file}")


def test_with_real_app():
    """使用真实应用进行测试"""
    print("🚀 启动真实应用进行问题检测")
    
    try:
        # 导入并启动应用
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        root = tk.Tk()
        app = EnhancedCADAppV2(root)
        
        # 等待应用初始化
        root.update()
        time.sleep(1)
        
        # 创建问题检测器
        detector = ProblemDetector()
        
        # 运行检查
        success = detector.run_comprehensive_check(app)
        
        if success:
            print("\n🎉 所有检查通过！应用状态良好。")
        else:
            print("\n⚠️ 发现问题，请查看详细报告。")
        
        # 关闭应用
        root.destroy()
        
        return success
        
    except Exception as e:
        print(f"💥 测试过程异常: {e}")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🧪 CAD三阶段处理 - 问题检测测试")
    print("=" * 50)
    
    try:
        success = test_with_real_app()
        
        if success:
            print("\n✅ 问题检测完成，应用状态良好")
        else:
            print("\n❌ 问题检测完成，发现问题需要修复")
            
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        traceback.print_exc()
    finally:
        print("\n🏁 问题检测结束")


if __name__ == "__main__":
    main()
