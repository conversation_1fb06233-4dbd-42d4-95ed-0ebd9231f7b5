#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试墙体组合并问题
分析为什么230个墙体组被合并为0个
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_wall_merge_logic():
    """分析墙体合并逻辑"""
    print("🔍 分析墙体合并逻辑...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 模拟230个墙体组的情况
        # 创建一些简单的墙体组用于测试
        test_groups = []
        
        # 创建一些不同大小的墙体组
        for i in range(10):  # 创建10个测试组
            # 每个组包含一个墙体实体
            entity = {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': i * 100.0,
                'start_y': 0.0,
                'end_x': i * 100.0 + 50.0,
                'end_y': 50.0,
                'label': 'wall',
                'auto_labeled': True
            }
            test_groups.append([entity])
        
        print(f"📋 创建测试组: {len(test_groups)} 个")
        
        # 测试边界框计算
        print(f"\n🔧 测试边界框计算:")
        bboxes = []
        for i, group in enumerate(test_groups):
            bbox = processor._get_group_bbox(group)
            bboxes.append(bbox)
            print(f"  组{i}: {bbox}")
        
        # 测试包含关系检查
        print(f"\n🔧 测试包含关系检查:")
        contained_count = 0
        for i, bbox1 in enumerate(bboxes):
            for j, bbox2 in enumerate(bboxes):
                if i != j and bbox1 and bbox2:
                    if processor._is_bbox_contained(bbox2, bbox1):
                        print(f"  组{i} 包含 组{j}")
                        contained_count += 1
        
        print(f"  发现包含关系: {contained_count} 个")
        
        # 测试完整的合并过程
        print(f"\n🔧 测试完整合并过程:")
        result = processor.merge_contained_wall_groups(test_groups)
        
        print(f"📊 合并结果:")
        print(f"  输入: {len(test_groups)} 个组")
        print(f"  输出: {len(result)} 个组")
        
        if len(result) == 0:
            print(f"  ❌ 所有组都被错误地合并掉了！")
            return False
        else:
            print(f"  ✅ 合并正常")
            return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bbox_contained_logic():
    """测试边界框包含逻辑"""
    print("\n🧪 测试边界框包含逻辑...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 测试不同的边界框情况
        test_cases = [
            {
                'name': '完全包含',
                'inner': (10, 10, 20, 20),
                'outer': (0, 0, 30, 30),
                'expected': True
            },
            {
                'name': '部分重叠',
                'inner': (10, 10, 25, 25),
                'outer': (0, 0, 20, 20),
                'expected': False
            },
            {
                'name': '完全分离',
                'inner': (30, 30, 40, 40),
                'outer': (0, 0, 20, 20),
                'expected': False
            },
            {
                'name': '相同边界框',
                'inner': (0, 0, 20, 20),
                'outer': (0, 0, 20, 20),
                'expected': True  # 由于容差，相同边界框应该被认为包含
            },
            {
                'name': '边界接触',
                'inner': (0, 0, 10, 10),
                'outer': (0, 0, 10, 10),
                'expected': True
            }
        ]
        
        print(f"📋 测试用例: {len(test_cases)} 个")
        
        all_correct = True
        for test_case in test_cases:
            name = test_case['name']
            inner = test_case['inner']
            outer = test_case['outer']
            expected = test_case['expected']
            
            result = processor._is_bbox_contained(inner, outer)
            
            print(f"  {name}:")
            print(f"    内边界框: {inner}")
            print(f"    外边界框: {outer}")
            print(f"    期望结果: {expected}")
            print(f"    实际结果: {result}")
            
            if result == expected:
                print(f"    ✅ 正确")
            else:
                print(f"    ❌ 错误")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_real_problem():
    """模拟真实问题场景"""
    print("\n🎯 模拟真实问题场景...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 模拟230个墙体组，每个组只有一个实体
        # 这种情况下，如果边界框计算或包含逻辑有问题，可能导致所有组被合并
        
        wall_groups = []
        
        # 创建230个小的墙体组
        for i in range(230):
            entity = {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': float(i),
                'start_y': 0.0,
                'end_x': float(i + 1),
                'end_y': 1.0,
                'label': 'wall',
                'auto_labeled': True
            }
            wall_groups.append([entity])
        
        print(f"📋 创建模拟数据: {len(wall_groups)} 个墙体组")
        
        # 检查前几个组的边界框
        print(f"\n🔧 检查边界框计算:")
        for i in range(min(5, len(wall_groups))):
            bbox = processor._get_group_bbox(wall_groups[i])
            print(f"  组{i}: {bbox}")
        
        # 执行合并
        print(f"\n🔧 执行合并...")
        result = processor.merge_contained_wall_groups(wall_groups)
        
        print(f"\n📊 模拟结果:")
        print(f"  输入: {len(wall_groups)} 个组")
        print(f"  输出: {len(result)} 个组")
        
        if len(result) == 0:
            print(f"  ❌ 复现了问题：所有组都被合并掉了！")
            
            # 分析可能的原因
            print(f"\n🔍 分析可能原因:")
            
            # 检查是否所有组都被识别为墙体组
            wall_count = 0
            for group in wall_groups[:5]:  # 检查前5个
                entities_to_check = group
                is_wall_group = False
                for entity in entities_to_check:
                    if isinstance(entity, dict):
                        if entity.get('label') == 'wall' or entity.get('auto_labeled', False):
                            is_wall_group = True
                            break
                        if any(pattern in entity.get('layer', '').lower() for pattern in ['wall', '墙']):
                            is_wall_group = True
                            break
                
                if is_wall_group:
                    wall_count += 1
            
            print(f"  前5个组中墙体组数量: {wall_count}")
            
            # 检查边界框包含关系
            bbox1 = processor._get_group_bbox(wall_groups[0])
            bbox2 = processor._get_group_bbox(wall_groups[1])
            
            if bbox1 and bbox2:
                contained_1_in_2 = processor._is_bbox_contained(bbox1, bbox2)
                contained_2_in_1 = processor._is_bbox_contained(bbox2, bbox1)
                
                print(f"  边界框1: {bbox1}")
                print(f"  边界框2: {bbox2}")
                print(f"  1包含在2中: {contained_1_in_2}")
                print(f"  2包含在1中: {contained_2_in_1}")
            
            return False
        else:
            print(f"  ✅ 合并正常，保留了 {len(result)} 个组")
            return True
        
    except Exception as e:
        print(f"❌ 模拟失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CAD分类标注工具 - 墙体组合并问题调试")
    print("=" * 60)
    
    tests = [
        ("墙体合并逻辑分析", analyze_wall_merge_logic),
        ("边界框包含逻辑测试", test_bbox_contained_logic),
        ("真实问题场景模拟", simulate_real_problem)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("调试结果总结:")
    print("=" * 60)
    
    for test_name, success in results:
        status = "✅ 正常" if success else "❌ 异常"
        print(f"  {test_name}: {status}")
    
    failed_tests = [name for name, success in results if not success]
    
    if failed_tests:
        print(f"\n⚠️ 发现问题: {len(failed_tests)} 个")
        print("需要修复墙体组合并逻辑")
    else:
        print(f"\n✅ 墙体组合并逻辑正常")
    
    print("=" * 60)
