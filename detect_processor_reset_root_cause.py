#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检测处理器重置问题的根源
追踪"⚠️ 处理器不存在，创建新的处理器"的触发原因
"""

import sys
import os
import time
import traceback
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def find_processor_reset_triggers():
    """查找所有可能触发处理器重置的位置"""
    print("🔍 查找处理器重置触发点")
    print("="*60)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找所有处理器检查的位置
        lines = content.split('\n')
        processor_checks = []
        
        for i, line in enumerate(lines, 1):
            # 查找处理器检查
            if 'if not self.processor:' in line:
                # 获取上下文
                start = max(0, i-5)
                end = min(len(lines), i+10)
                context = lines[start:end]
                
                # 查找方法名
                method_name = "未知方法"
                for j in range(i-1, max(0, i-20), -1):
                    if lines[j].strip().startswith('def '):
                        method_name = lines[j].strip()
                        break
                
                processor_checks.append({
                    'line': i,
                    'method': method_name,
                    'context': context,
                    'check_line': line.strip()
                })
        
        print(f"发现 {len(processor_checks)} 个处理器检查点:")
        
        for i, check in enumerate(processor_checks, 1):
            print(f"\n{i}. 第{check['line']}行 - {check['method']}")
            print(f"   检查: {check['check_line']}")
            
            # 分析上下文，判断触发原因
            context_str = '\n'.join(check['context'])
            
            # 判断触发场景
            if 'update_group_list' in check['method']:
                print(f"   🎯 触发场景: 组列表更新时")
            elif '_load_from_cache' in context_str:
                print(f"   🎯 触发场景: 从缓存加载文件时")
            elif 'start_processing' in context_str:
                print(f"   🎯 触发场景: 开始处理文件时")
            elif 'file_selected' in context_str:
                print(f"   🎯 触发场景: 文件选择时")
            else:
                print(f"   🎯 触发场景: 其他操作")
        
        return processor_checks
        
    except Exception as e:
        print(f"❌ 查找失败: {e}")
        return []

def analyze_processor_lifecycle():
    """分析处理器生命周期"""
    print(f"\n🔄 分析处理器生命周期")
    print("="*60)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找处理器创建的所有位置
        creation_patterns = [
            'self.processor = EnhancedCADProcessor',
            'self.processor = None',
            'processor = EnhancedCADProcessor'
        ]
        
        lines = content.split('\n')
        lifecycle_events = []
        
        for i, line in enumerate(lines, 1):
            for pattern in creation_patterns:
                if pattern in line:
                    # 获取方法名
                    method_name = "未知方法"
                    for j in range(i-1, max(0, i-20), -1):
                        if lines[j].strip().startswith('def '):
                            method_name = lines[j].strip()
                            break
                    
                    event_type = "创建" if "= EnhancedCADProcessor" in line else "重置为None"
                    
                    lifecycle_events.append({
                        'line': i,
                        'method': method_name,
                        'event': event_type,
                        'code': line.strip()
                    })
        
        print(f"发现 {len(lifecycle_events)} 个处理器生命周期事件:")
        
        for i, event in enumerate(lifecycle_events, 1):
            print(f"\n{i}. 第{event['line']}行 - {event['method']}")
            print(f"   事件: {event['event']}")
            print(f"   代码: {event['code']}")
        
        return lifecycle_events
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return []

def create_processor_monitor():
    """创建处理器监控代码"""
    print(f"\n🔧 创建处理器监控代码")
    print("="*60)
    
    monitor_code = '''
# 处理器监控代码 - 插入到类的初始化方法中
def _init_processor_monitor(self):
    """初始化处理器监控"""
    self._processor_creation_count = 0
    self._processor_reset_log = []
    self._last_processor_check = time.time()

def _log_processor_event(self, event_type, location, details=""):
    """记录处理器事件"""
    import time
    timestamp = time.strftime("%H:%M:%S", time.localtime())
    
    if event_type == "reset":
        self._processor_creation_count += 1
    
    event = {
        'timestamp': timestamp,
        'type': event_type,
        'location': location,
        'details': details,
        'count': self._processor_creation_count
    }
    
    self._processor_reset_log.append(event)
    
    # 打印详细日志
    print(f"🔍 [{timestamp}] 处理器事件: {event_type}")
    print(f"   位置: {location}")
    print(f"   详情: {details}")
    print(f"   重置次数: {self._processor_creation_count}")
    
    # 如果重置次数过多，打印调用栈
    if self._processor_creation_count > 3:
        print(f"   ⚠️ 处理器重置次数过多，打印调用栈:")
        import traceback
        traceback.print_stack()

def _check_processor_stability(self):
    """检查处理器稳定性"""
    current_time = time.time()
    
    if hasattr(self, '_last_processor_check'):
        time_diff = current_time - self._last_processor_check
        
        if not self.processor:
            self._log_processor_event("missing", "stability_check", 
                                    f"处理器在{time_diff:.2f}秒后丢失")
        elif time_diff > 1.0:  # 超过1秒检查一次
            self._log_processor_event("check", "stability_check", 
                                    f"处理器状态正常，间隔{time_diff:.2f}秒")
    
    self._last_processor_check = current_time

# 修改后的处理器检查代码 - 替换原有的检查
def _safe_processor_check(self, location):
    """安全的处理器检查"""
    if not hasattr(self, '_processor_creation_count'):
        self._init_processor_monitor()
    
    self._check_processor_stability()
    
    if not self.processor:
        self._log_processor_event("reset", location, "处理器不存在，需要创建")
        
        # 分析可能的原因
        possible_causes = []
        
        if hasattr(self, 'current_file') and not self.current_file:
            possible_causes.append("当前文件为空")
        
        if hasattr(self, 'file_cache') and not self.file_cache:
            possible_causes.append("文件缓存为空")
        
        if len(self._processor_reset_log) > 1:
            last_event = self._processor_reset_log[-2]
            time_since_last = time.time() - time.mktime(time.strptime(last_event['timestamp'], "%H:%M:%S"))
            if time_since_last < 5:
                possible_causes.append(f"距离上次重置仅{time_since_last:.1f}秒")
        
        if possible_causes:
            print(f"   🔍 可能原因: {', '.join(possible_causes)}")
        
        return True  # 需要创建处理器
    
    return False  # 处理器存在
'''
    
    print("生成的监控代码:")
    print(monitor_code)
    
    # 保存监控代码到文件
    with open('processor_monitor_code.py', 'w', encoding='utf-8') as f:
        f.write(monitor_code)
    
    print(f"\n✅ 监控代码已保存到 processor_monitor_code.py")
    return True

def create_detection_patch():
    """创建检测补丁"""
    print(f"\n🔧 创建检测补丁")
    print("="*60)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 在类初始化中添加监控
        init_patch = '''
        # 🔍 处理器监控初始化
        self._processor_creation_count = 0
        self._processor_reset_log = []
        self._last_processor_check = time.time()
        print("🔍 处理器监控已启动")
'''
        
        # 查找 __init__ 方法的结尾
        init_end = content.find('        # 调用父类初始化')
        if init_end != -1:
            content = content[:init_end] + init_patch + content[init_end:]
            print("✅ 已添加处理器监控初始化")
        
        # 替换处理器检查逻辑
        old_check = '''            if not self.processor:
                print("  ⚠️ 处理器不存在，创建新的处理器")'''
        
        new_check = '''            if not self.processor:
                # 🔍 详细记录处理器重置事件
                import time
                timestamp = time.strftime("%H:%M:%S", time.localtime())
                if not hasattr(self, '_processor_creation_count'):
                    self._processor_creation_count = 0
                self._processor_creation_count += 1
                
                print(f"🔍 [{timestamp}] 处理器重置事件 #{self._processor_creation_count}")
                print(f"   触发位置: _load_from_cache")
                print(f"   当前文件: {getattr(self, 'current_file', '未知')}")
                print(f"   缓存状态: {len(getattr(self, 'file_cache', {})) if hasattr(self, 'file_cache') else '未知'}")
                
                # 打印调用栈以追踪根源
                import traceback
                print(f"   调用栈:")
                for line in traceback.format_stack()[-5:-1]:
                    print(f"     {line.strip()}")
                
                print("  ⚠️ 处理器不存在，创建新的处理器")'''
        
        if old_check in content:
            content = content.replace(old_check, new_check)
            print("✅ 已添加详细的处理器重置检测")
        
        # 保存修改后的文件
        backup_file = 'main_enhanced_with_v2_fill_backup.py'
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 检测补丁已保存到 {backup_file}")
        print(f"💡 使用方法:")
        print(f"   1. 备份原文件: cp main_enhanced_with_v2_fill.py main_enhanced_with_v2_fill_original.py")
        print(f"   2. 应用补丁: cp {backup_file} main_enhanced_with_v2_fill.py")
        print(f"   3. 运行程序观察详细日志")
        print(f"   4. 恢复原文件: cp main_enhanced_with_v2_fill_original.py main_enhanced_with_v2_fill.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建补丁失败: {e}")
        return False

def analyze_common_causes():
    """分析常见原因"""
    print(f"\n📊 分析处理器重置的常见原因")
    print("="*60)
    
    common_causes = [
        {
            "原因": "文件切换时处理器被清空",
            "触发场景": "用户选择新文件或切换文件",
            "检测方法": "监控 on_file_selected 和相关方法",
            "解决方案": "在文件切换前保存处理器状态，切换后恢复"
        },
        {
            "原因": "界面更新时处理器引用丢失",
            "触发场景": "组列表更新、状态更新等界面操作",
            "检测方法": "监控 update_group_list 等方法",
            "解决方案": "确保界面更新不影响处理器引用"
        },
        {
            "原因": "异常处理中处理器被重置",
            "触发场景": "发生异常时的错误恢复",
            "检测方法": "监控 try-except 块中的处理器操作",
            "解决方案": "改进异常处理，避免不必要的重置"
        },
        {
            "原因": "多线程或异步操作冲突",
            "触发场景": "后台处理与前台操作同时进行",
            "检测方法": "监控线程安全性",
            "解决方案": "添加线程锁或改为同步处理"
        },
        {
            "原因": "内存管理问题",
            "触发场景": "垃圾回收或内存不足",
            "检测方法": "监控内存使用和对象引用",
            "解决方案": "改进内存管理，确保对象引用稳定"
        }
    ]
    
    for i, cause in enumerate(common_causes, 1):
        print(f"\n{i}. {cause['原因']}")
        print(f"   触发场景: {cause['触发场景']}")
        print(f"   检测方法: {cause['检测方法']}")
        print(f"   解决方案: {cause['解决方案']}")
    
    print(f"\n💡 建议的检测步骤:")
    print(f"   1. 应用检测补丁，运行程序")
    print(f"   2. 重现问题，观察详细日志")
    print(f"   3. 分析调用栈，找出触发源头")
    print(f"   4. 根据具体原因实施对应解决方案")

def main():
    """主检测函数"""
    print("🚀 开始检测处理器重置问题根源")
    
    try:
        # 1. 查找处理器重置触发点
        processor_checks = find_processor_reset_triggers()
        
        # 2. 分析处理器生命周期
        lifecycle_events = analyze_processor_lifecycle()
        
        # 3. 创建处理器监控代码
        create_processor_monitor()
        
        # 4. 创建检测补丁
        create_detection_patch()
        
        # 5. 分析常见原因
        analyze_common_causes()
        
        print(f"\n" + "="*60)
        print(f"📊 检测结果总结:")
        print(f"  发现处理器检查点: {len(processor_checks)} 个")
        print(f"  发现生命周期事件: {len(lifecycle_events)} 个")
        print(f"  生成监控代码: ✅ 完成")
        print(f"  创建检测补丁: ✅ 完成")
        
        print(f"\n🎯 下一步行动:")
        print(f"   1. 应用检测补丁到主程序")
        print(f"   2. 运行程序并重现问题")
        print(f"   3. 观察详细的处理器重置日志")
        print(f"   4. 根据日志分析找出根本原因")
        print(f"   5. 实施针对性的修复方案")
        
    except Exception as e:
        print(f"❌ 检测过程失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
