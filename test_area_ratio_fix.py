#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试面积比例修复
专门测试面积比例检查是否正确工作
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_area_ratio_logic():
    """测试面积比例逻辑"""
    print("🧪 测试面积比例逻辑...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 精确的测试用例
        test_cases = [
            {
                'name': '面积比例90%（不应该合并）',
                'outer': (0, 0, 100, 100),      # 面积: 10000
                'inner': (5, 5, 95, 95),        # 面积: 8100, 比例: 81%
                'expected': False,
                'area_ratio': 0.81
            },
            {
                'name': '面积比例50%（应该合并）',
                'outer': (0, 0, 100, 100),      # 面积: 10000
                'inner': (25, 25, 75, 75),      # 面积: 2500, 比例: 25%
                'expected': True,
                'area_ratio': 0.25
            },
            {
                'name': '面积比例10%（应该合并）',
                'outer': (0, 0, 1000, 1000),    # 面积: 1000000
                'inner': (400, 400, 500, 500),  # 面积: 10000, 比例: 1%
                'expected': True,
                'area_ratio': 0.01
            },
            {
                'name': '面积比例85%（不应该合并）',
                'outer': (0, 0, 100, 100),      # 面积: 10000
                'inner': (8, 8, 92, 92),        # 面积: 7056, 比例: 70.56%
                'expected': True,  # 实际上70.56% < 80%，所以应该合并
                'area_ratio': 0.7056
            }
        ]
        
        print(f"📋 测试用例: {len(test_cases)} 个")
        
        correct_count = 0
        for i, test_case in enumerate(test_cases):
            name = test_case['name']
            outer = test_case['outer']
            inner = test_case['inner']
            expected = test_case['expected']
            expected_ratio = test_case['area_ratio']
            
            # 计算实际面积比例
            inner_area = (inner[2] - inner[0]) * (inner[3] - inner[1])
            outer_area = (outer[2] - outer[0]) * (outer[3] - outer[1])
            actual_ratio = inner_area / outer_area if outer_area > 0 else 0
            
            result = processor._is_bbox_contained(inner, outer)
            
            print(f"  测试{i+1}: {name}")
            print(f"    外边界框: {outer} (面积: {outer_area})")
            print(f"    内边界框: {inner} (面积: {inner_area})")
            print(f"    计算面积比例: {actual_ratio:.3f}")
            print(f"    期望面积比例: {expected_ratio:.3f}")
            print(f"    期望包含: {expected}")
            print(f"    实际包含: {result}")
            
            # 检查面积比例计算是否正确
            if abs(actual_ratio - expected_ratio) < 0.01:
                print(f"    ✅ 面积比例计算正确")
            else:
                print(f"    ⚠️ 面积比例计算有差异")
            
            # 检查包含逻辑是否正确
            if result == expected:
                print(f"    ✅ 包含逻辑正确")
                correct_count += 1
            else:
                print(f"    ❌ 包含逻辑错误")
                
                # 分析原因
                if actual_ratio > 0.8 and result == True:
                    print(f"      原因: 面积比例{actual_ratio:.1%} > 80%，但仍然返回True")
                elif actual_ratio <= 0.8 and result == False:
                    print(f"      原因: 面积比例{actual_ratio:.1%} <= 80%，但返回False")
        
        success_rate = correct_count / len(test_cases)
        print(f"\n📊 测试结果: {correct_count}/{len(test_cases)} 正确 ({success_rate:.1%})")
        
        return success_rate >= 0.75
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_specific_case():
    """调试特定的失败案例"""
    print("\n🔍 调试特定失败案例...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 之前失败的案例
        outer = (0, 0, 100, 100)
        inner = (10, 10, 90, 90)
        
        print(f"调试案例:")
        print(f"  外边界框: {outer}")
        print(f"  内边界框: {inner}")
        
        # 手动计算
        inner_area = (inner[2] - inner[0]) * (inner[3] - inner[1])
        outer_area = (outer[2] - outer[0]) * (outer[3] - outer[1])
        area_ratio = inner_area / outer_area
        
        print(f"  内面积: {inner_area}")
        print(f"  外面积: {outer_area}")
        print(f"  面积比例: {area_ratio:.3f} ({area_ratio:.1%})")
        
        # 检查基本包含关系
        tolerance = 0.1
        is_contained = (inner[0] >= outer[0] - tolerance and
                       inner[1] >= outer[1] - tolerance and
                       inner[2] <= outer[2] + tolerance and
                       inner[3] <= outer[3] + tolerance)
        
        print(f"  基本包含关系: {is_contained}")
        print(f"    inner[0] >= outer[0] - tolerance: {inner[0]} >= {outer[0] - tolerance} = {inner[0] >= outer[0] - tolerance}")
        print(f"    inner[1] >= outer[1] - tolerance: {inner[1]} >= {outer[1] - tolerance} = {inner[1] >= outer[1] - tolerance}")
        print(f"    inner[2] <= outer[2] + tolerance: {inner[2]} <= {outer[2] + tolerance} = {inner[2] <= outer[2] + tolerance}")
        print(f"    inner[3] <= outer[3] + tolerance: {inner[3]} <= {outer[3] + tolerance} = {inner[3] <= outer[3] + tolerance}")
        
        # 检查面积比例
        area_check = area_ratio <= 0.8
        print(f"  面积比例检查 ({area_ratio:.3f} <= 0.8): {area_check}")
        
        # 最终结果
        expected_result = is_contained and area_check
        print(f"  期望结果: {expected_result}")
        
        # 实际调用
        actual_result = processor._is_bbox_contained(inner, outer)
        print(f"  实际结果: {actual_result}")
        
        if actual_result == expected_result:
            print(f"  ✅ 结果匹配")
            return True
        else:
            print(f"  ❌ 结果不匹配")
            return False
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CAD分类标注工具 - 面积比例修复测试")
    print("=" * 60)
    
    tests = [
        ("面积比例逻辑测试", test_area_ratio_logic),
        ("特定案例调试", debug_specific_case)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print("=" * 60)
