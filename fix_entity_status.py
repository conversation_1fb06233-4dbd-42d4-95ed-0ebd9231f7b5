#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复实体状态识别问题
确保已标注的实体能正确显示颜色
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_entity_status_recognition():
    """修复实体状态识别"""
    print("🔧 修复实体状态识别")
    print("="*50)

    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # 修复1：确保auto_labeled_entities正确传递给可视化器
        print("1. 修复auto_labeled_entities传递...")

        old_show_group = """                if current_file_entities:
                    # 🔑 关键修复：显示所有组，高亮当前组
                    print(f"  🌍 显示全图概览: 总实体={len(current_file_entities)}, 当前组实体={len(cleaned_group)}")

                    # 获取所有组信息用于完整显示
                    all_groups = getattr(self.processor, 'all_groups', [])
                    print(f"  📊 所有组数: {len(all_groups)}")

                    self.processor.visualizer.visualize_overview(
                        current_file_entities,  # 显示所有实体
                        cleaned_group,  # 高亮当前组
                        auto_labeled_entities + labeled_entities,  # 已标注实体
                        processor=self.processor,  # 传递处理器以获取组信息
                        current_group_index=group_index  # 当前组索引用于高亮
                    )
                    print(f"  ✅ 全图概览更新完成")
                else:
                    print("  ⚠️ 没有实体数据，跳过全图概览更新")"""

        new_show_group = """                if current_file_entities:
                    # 🔑 关键修复：显示所有组，高亮当前组
                    print(f"  🌍 显示全图概览: 总实体={len(current_file_entities)}, 当前组实体={len(cleaned_group)}")

                    # 获取所有组信息用于完整显示
                    all_groups = getattr(self.processor, 'all_groups', [])
                    print(f"  📊 所有组数: {len(all_groups)}")

                    # 🔧 修复：确保已标注实体正确识别
                    all_labeled = auto_labeled_entities + labeled_entities
                    print(f"  📊 已标注实体数: {len(all_labeled)} (自动:{len(auto_labeled_entities)}, 手动:{len(labeled_entities)})")

                    # 检查实体标注状态
                    for i, entity in enumerate(current_file_entities[:3]):  # 只检查前3个
                        is_labeled = any(self.processor._is_entity_in_group_safe(entity, [labeled]) for labeled in all_labeled)
                        print(f"    实体{i+1}: {'已标注' if is_labeled else '未标注'} - {entity.get('layer', 'unknown')}")

                    self.processor.visualizer.visualize_overview(
                        current_file_entities,  # 显示所有实体
                        cleaned_group,  # 高亮当前组
                        all_labeled,  # 已标注实体
                        processor=self.processor,  # 传递处理器以获取组信息
                        current_group_index=group_index  # 当前组索引用于高亮
                    )
                    print(f"  ✅ 全图概览更新完成")
                else:
                    print("  ⚠️ 没有实体数据，跳过全图概览更新")"""

        if old_show_group in content:
            content = content.replace(old_show_group, new_show_group)
            print("  ✅ 已修复auto_labeled_entities传递")

        # 修复2：改进_refresh_complete_view方法
        print("2. 修复_refresh_complete_view方法...")

        old_refresh = """            # 使用可视化器显示完整视图
            if (hasattr(self.processor, 'visualizer') and self.processor.visualizer and
                hasattr(self, 'canvas') and self.canvas):

                # 显示全图概览（不高亮任何特定组）
                self.processor.visualizer.visualize_overview(
                    current_file_entities,  # 所有实体
                    None,  # 不高亮特定组
                    auto_labeled_entities + labeled_entities,  # 已标注实体
                    processor=self.processor  # 处理器信息
                )"""

        new_refresh = """            # 使用可视化器显示完整视图
            if (hasattr(self.processor, 'visualizer') and self.processor.visualizer and
                hasattr(self, 'canvas') and self.canvas):

                # 🔧 修复：确保已标注实体正确识别
                all_labeled = auto_labeled_entities + labeled_entities
                print(f"    完整视图已标注实体: {len(all_labeled)}")

                # 显示全图概览（不高亮任何特定组）
                self.processor.visualizer.visualize_overview(
                    current_file_entities,  # 所有实体
                    None,  # 不高亮特定组
                    all_labeled,  # 已标注实体
                    processor=self.processor  # 处理器信息
                )"""

        if old_refresh in content:
            content = content.replace(old_refresh, new_refresh)
            print("  ✅ 已修复_refresh_complete_view方法")

        # 保存修改后的文件
        with open('main_enhanced_with_v2_fill.py', 'w', encoding='utf-8') as f:
            f.write(content)

        print("✅ 实体状态识别修复完成")
        return True

    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

if __name__ == "__main__":
    fix_entity_status_recognition()
