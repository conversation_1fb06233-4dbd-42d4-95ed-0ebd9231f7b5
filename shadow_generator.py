#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阴影生成模块
为CAD图层提供阴影效果，支持方向敏感、自适应强度、接触阴影等功能
"""

import numpy as np
import math
import time
from typing import List, Dict, Tuple, Any, Optional
from concurrent.futures import ThreadPoolExecutor
import threading

try:
    from PIL import Image, ImageDraw, ImageFilter
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("⚠️ PIL不可用，将使用简化版阴影生成")

try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from matplotlib.collections import PatchCollection
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ Matplotlib不可用，阴影功能受限")

try:
    from shapely.geometry import Polygon, Point
    SHAPELY_AVAILABLE = True
except ImportError:
    SHAPELY_AVAILABLE = False
    print("⚠️ Shapely不可用，将使用简化版几何处理")

class ShadowCache:
    """阴影缓存机制，提高性能"""
    
    def __init__(self):
        self.cache = {}
        self.lock = threading.RLock()
        self.max_cache_size = 1000
    
    def get_shadow(self, polygon_key, shadow_params):
        """获取缓存的阴影"""
        with self.lock:
            cache_key = (polygon_key, tuple(shadow_params.items()))
            return self.cache.get(cache_key)
    
    def set_shadow(self, polygon_key, shadow_params, shadow_data):
        """缓存阴影数据"""
        with self.lock:
            if len(self.cache) >= self.max_cache_size:
                # 清理最旧的缓存
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
            
            cache_key = (polygon_key, tuple(shadow_params.items()))
            self.cache[cache_key] = shadow_data
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()

class RasterShadowGenerator:
    """光栅阴影生成器"""
    
    def __init__(self, offset_x=10, offset_y=10, blur_radius=15, 
                 shadow_color=(32, 32, 32, 180), bg_color=(255, 255, 255, 0)):
        """
        初始化光栅阴影生成器
        
        Args:
            offset_x: X轴阴影偏移量
            offset_y: Y轴阴影偏移量
            blur_radius: 高斯模糊半径
            shadow_color: RGBA阴影颜色
            bg_color: 背景颜色（通常透明）
        """
        self.offset_x = offset_x
        self.offset_y = offset_y
        self.blur_radius = blur_radius
        self.shadow_color = shadow_color
        self.bg_color = bg_color
        self.cache = ShadowCache()
    
    def create_shadow_image(self, polygons, image_size, padding=50):
        """
        为多边形列表创建阴影图像
        
        Args:
            polygons: 多边形列表（每个多边形是坐标列表）
            image_size: 输出图像尺寸 (width, height)
            padding: 图像边缘填充
            
        Returns:
            PIL阴影图像
        """
        if not PIL_AVAILABLE:
            print("⚠️ PIL不可用，无法创建光栅阴影")
            return None
        
        # 创建透明背景
        shadow_img = Image.new('RGBA', image_size, self.bg_color)
        shadow_draw = ImageDraw.Draw(shadow_img)
        
        # 绘制所有阴影多边形
        for poly in polygons:
            if len(poly) < 3:
                continue
                
            # 应用偏移
            shadow_poly = [(x + self.offset_x + padding, 
                           y + self.offset_y + padding) for (x, y) in poly]
            
            try:
                shadow_draw.polygon(shadow_poly, fill=self.shadow_color)
            except Exception as e:
                print(f"⚠️ 绘制阴影多边形失败: {e}")
                continue
        
        # 应用高斯模糊
        try:
            blurred_shadow = shadow_img.filter(ImageFilter.GaussianBlur(self.blur_radius))
        except Exception as e:
            print(f"⚠️ 应用高斯模糊失败: {e}")
            blurred_shadow = shadow_img
        
        # 裁剪到原始尺寸
        bbox = (padding, padding, image_size[0]-padding, image_size[1]-padding)
        try:
            return blurred_shadow.crop(bbox)
        except Exception as e:
            print(f"⚠️ 裁剪图像失败: {e}")
            return blurred_shadow
    
    def apply_to_plot(self, ax, filled_groups, bounds=None):
        """
        为matplotlib图形添加阴影
        
        Args:
            ax: matplotlib坐标轴
            filled_groups: 填充实体组
            bounds: 图像边界 (min_x, min_y, max_x, max_y)
        """
        if not MATPLOTLIB_AVAILABLE:
            print("⚠️ Matplotlib不可用，无法应用阴影到图形")
            return
        
        if not bounds:
            # 自动计算边界
            all_points = []
            for group in filled_groups:
                for poly in group.get('fill_polygons', []):
                    if isinstance(poly, list):
                        all_points.extend(poly)
                    elif hasattr(poly, 'exterior'):
                        all_points.extend(list(poly.exterior.coords))
                
                for cavity in group.get('cavities', []):
                    if isinstance(cavity, list):
                        all_points.extend(cavity)
                    elif hasattr(cavity, 'exterior'):
                        all_points.extend(list(cavity.exterior.coords))
            
            if not all_points:
                return
            
            xs = [p[0] for p in all_points]
            ys = [p[1] for p in all_points]
            bounds = (min(xs), min(ys), max(xs), max(ys))
        
        # 计算图像尺寸
        width = int(bounds[2] - bounds[0]) + 100
        height = int(bounds[3] - bounds[1]) + 100
        image_size = (width, height)
        
        # 提取所有多边形
        all_polygons = []
        for group in filled_groups:
            for poly in group.get('fill_polygons', []):
                if isinstance(poly, list):
                    all_polygons.append(poly)
                elif hasattr(poly, 'exterior'):
                    all_polygons.append(list(poly.exterior.coords))
            
            for cavity in group.get('cavities', []):
                if isinstance(cavity, list):
                    all_polygons.append(cavity)
                elif hasattr(cavity, 'exterior'):
                    all_polygons.append(list(cavity.exterior.coords))
        
        if not all_polygons:
            return
        
        # 创建阴影图像
        shadow_img = self.create_shadow_image(all_polygons, image_size)
        
        if shadow_img:
            # 转换为数组并显示
            shadow_array = np.array(shadow_img)
            ax.imshow(shadow_array, 
                      extent=[bounds[0], bounds[2], bounds[1], bounds[3]], 
                      aspect='auto', 
                      zorder=-10)

class VectorShadowGenerator:
    """矢量阴影生成器"""
    
    def __init__(self, offset_x=10, offset_y=10, shadow_color='#404040', alpha=0.3):
        """
        初始化矢量阴影生成器
        
        Args:
            offset_x: X轴阴影偏移量
            offset_y: Y轴阴影偏移量
            shadow_color: 阴影颜色
            alpha: 阴影透明度
        """
        self.offset_x = offset_x
        self.offset_y = offset_y
        self.shadow_color = shadow_color
        self.alpha = alpha
        self.cache = ShadowCache()
    
    def create_shadow_patches(self, filled_groups):
        """
        为填充组创建阴影补丁
        
        Args:
            filled_groups: 填充实体组列表
            
        Returns:
            阴影补丁列表
        """
        if not MATPLOTLIB_AVAILABLE:
            print("⚠️ Matplotlib不可用，无法创建矢量阴影")
            return []
        
        shadow_patches = []
        
        for group in filled_groups:
            # 处理填充多边形
            for poly in group.get('fill_polygons', []):
                shadow_poly = self._create_shadow_polygon(poly)
                if shadow_poly:
                    patch = self._create_patch(shadow_poly)
                    if patch:
                        shadow_patches.append(patch)
            
            # 处理空洞
            for cavity in group.get('cavities', []):
                shadow_cavity = self._create_shadow_polygon(cavity)
                if shadow_cavity:
                    patch = self._create_patch(shadow_cavity)
                    if patch:
                        shadow_patches.append(patch)
        
        return shadow_patches
    
    def _create_shadow_polygon(self, polygon):
        """创建阴影多边形"""
        if isinstance(polygon, list):
            coords = polygon
        elif hasattr(polygon, 'exterior'):
            coords = list(polygon.exterior.coords)
        else:
            return None
        
        if len(coords) < 3:
            return None
        
        # 应用偏移
        shadow_coords = [(x + self.offset_x, y + self.offset_y) for x, y in coords]
        return shadow_coords
    
    def _create_patch(self, shadow_polygon):
        """创建matplotlib补丁"""
        if not MATPLOTLIB_AVAILABLE:
            return None
        
        try:
            return patches.Polygon(
                shadow_polygon,
                facecolor=self.shadow_color,
                alpha=self.alpha,
                edgecolor='none',
                zorder=-5
            )
        except Exception as e:
            print(f"⚠️ 创建阴影补丁失败: {e}")
            return None
    
    def apply_to_plot(self, ax, filled_groups):
        """将阴影应用到图形"""
        if not MATPLOTLIB_AVAILABLE:
            return
        
        shadow_patches = self.create_shadow_patches(filled_groups)
        if shadow_patches:
            try:
                ax.add_collection(PatchCollection(shadow_patches, match_original=True))
            except Exception as e:
                print(f"⚠️ 添加阴影补丁失败: {e}")

class DirectionalShadowGenerator(VectorShadowGenerator):
    """方向敏感阴影生成器"""
    
    def __init__(self, light_angle=315, max_offset=15, shadow_color='#404040', alpha=0.3):
        """
        初始化方向阴影生成器
        
        Args:
            light_angle: 光源角度（0-360度，0=正右方）
            max_offset: 最大偏移量
            shadow_color: 阴影颜色
            alpha: 阴影透明度
        """
        # 将角度转换为弧度
        angle_rad = math.radians(light_angle)
        
        # 计算偏移量
        offset_x = max_offset * math.cos(angle_rad)
        offset_y = max_offset * math.sin(angle_rad)
        
        super().__init__(offset_x, offset_y, shadow_color, alpha)
        self.light_angle = light_angle
        self.max_offset = max_offset

class AdaptiveShadowGenerator(VectorShadowGenerator):
    """自适应阴影强度生成器"""
    
    def __init__(self, base_offset=5, depth_factor=0.3, shadow_color='#404040', base_alpha=0.3):
        """
        初始化自适应阴影生成器
        
        Args:
            base_offset: 基础偏移量
            depth_factor: 深度因子
            shadow_color: 阴影颜色
            base_alpha: 基础透明度
        """
        super().__init__(base_offset, base_offset, shadow_color, base_alpha)
        self.base_offset = base_offset
        self.depth_factor = depth_factor
        self.base_alpha = base_alpha
    
    def create_shadow_patches(self, filled_groups):
        """创建自适应阴影补丁"""
        if not MATPLOTLIB_AVAILABLE:
            return []
        
        shadow_patches = []
        
        for group in filled_groups:
            # 获取实体高度（假设存储在属性中）
            elevation = group.get('elevation', 0)
            
            # 计算动态偏移和透明度
            dynamic_offset = self.base_offset + elevation * self.depth_factor
            dynamic_alpha = min(self.base_alpha + elevation * 0.1, 0.8)
            
            # 临时更新偏移量
            original_offset_x = self.offset_x
            original_offset_y = self.offset_y
            original_alpha = self.alpha
            
            self.offset_x = dynamic_offset
            self.offset_y = dynamic_offset
            self.alpha = dynamic_alpha
            
            # 处理填充多边形
            for poly in group.get('fill_polygons', []):
                shadow_poly = self._create_shadow_polygon(poly)
                if shadow_poly:
                    patch = self._create_patch(shadow_poly)
                    if patch:
                        shadow_patches.append(patch)
            
            # 恢复原始值
            self.offset_x = original_offset_x
            self.offset_y = original_offset_y
            self.alpha = original_alpha
        
        return shadow_patches

class ContactShadowGenerator:
    """接触阴影生成器"""
    
    def __init__(self, shadow_color='#000000', alpha=0.8, height=3):
        """
        初始化接触阴影生成器
        
        Args:
            shadow_color: 阴影颜色
            alpha: 阴影透明度
            height: 阴影高度
        """
        self.shadow_color = shadow_color
        self.alpha = alpha
        self.height = height
    
    def add_contact_shadow(self, ax, filled_groups):
        """
        在实体底部添加接触阴影
        
        Args:
            ax: matplotlib坐标轴
            filled_groups: 填充实体组
        """
        if not MATPLOTLIB_AVAILABLE:
            return
        
        contact_patches = []
        
        for group in filled_groups:
            for polygon in group.get('fill_polygons', []):
                # 获取多边形边界
                if SHAPELY_AVAILABLE and hasattr(polygon, 'bounds'):
                    minx, miny, maxx, maxy = polygon.bounds
                elif isinstance(polygon, list):
                    xs = [p[0] for p in polygon]
                    ys = [p[1] for p in polygon]
                    minx, maxx = min(xs), max(xs)
                    miny, maxy = min(ys), max(ys)
                else:
                    continue
                
                # 创建接触阴影矩形
                contact_shadow = [
                    (minx, miny),
                    (maxx, miny),
                    (maxx, miny + self.height),
                    (minx, miny + self.height)
                ]
                
                try:
                    contact_patches.append(patches.Polygon(
                        contact_shadow,
                        facecolor=self.shadow_color,
                        alpha=self.alpha,
                        edgecolor='none',
                        zorder=5  # 在实体下方但在主阴影上方
                    ))
                except Exception as e:
                    print(f"⚠️ 创建接触阴影失败: {e}")
                    continue
        
        if contact_patches:
            try:
                ax.add_collection(PatchCollection(contact_patches, match_original=True))
            except Exception as e:
                print(f"⚠️ 添加接触阴影失败: {e}")

def batch_create_shadows(filled_groups, generator):
    """使用多线程批量生成阴影"""
    try:
        with ThreadPoolExecutor(max_workers=4) as executor:
            # 为每个组并行创建阴影
            results = list(executor.map(
                lambda group: generator.create_shadow_patches([group]), 
                filled_groups
            ))
        
        # 合并结果
        return [patch for sublist in results for patch in sublist]
    except Exception as e:
        print(f"⚠️ 批量创建阴影失败: {e}")
        # 回退到单线程处理
        return generator.create_shadow_patches(filled_groups)

def simplify_for_shadow(polygon, tolerance=1.0):
    """简化多边形（提高阴影性能）"""
    if not SHAPELY_AVAILABLE:
        return polygon
    
    try:
        if isinstance(polygon, list):
            poly_obj = Polygon(polygon)
        else:
            poly_obj = polygon
        
        simplified = poly_obj.simplify(tolerance, preserve_topology=True)
        
        if isinstance(polygon, list):
            return list(simplified.exterior.coords)
        return simplified
    except Exception as e:
        print(f"⚠️ 简化多边形失败: {e}")
        return polygon
