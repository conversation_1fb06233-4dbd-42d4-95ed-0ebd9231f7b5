#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分组功能修复
验证 group_entities_by_connectivity_and_layer 方法错误已修复
"""

from cad_data_processor import CADDataProcessor
import traceback

def test_grouping_fix():
    """测试分组功能修复"""
    print("🔧 测试分组功能修复")
    print("="*60)
    
    # 创建测试数据
    test_entities = [
        {'type': 'LINE', 'layer': 'WALL', 'points': [(0, 0), (100, 0)]},
        {'type': 'LINE', 'layer': 'WALL', 'points': [(100, 0), (100, 100)]},
        {'type': 'LINE', 'layer': 'WALL', 'points': [(100, 100), (0, 100)]},
        {'type': 'LINE', 'layer': 'WALL', 'points': [(0, 100), (0, 0)]},
        {'type': 'LINE', 'layer': 'DOOR', 'points': [(50, 0), (50, 20)]},
        {'type': 'ARC', 'layer': 'DOOR', 'center': (50, 0), 'radius': 20, 'start_angle': 0, 'end_angle': 90},
        {'type': 'CIRCLE', 'layer': 'FURNITURE', 'center': (50, 50), 'radius': 10},
        {'type': 'TEXT', 'layer': 'ANNOTATION', 'text': '客厅', 'position': (50, 50), 'height': 5, 'rotation': 0}
    ]
    
    print(f"测试数据: {len(test_entities)} 个实体")
    for i, entity in enumerate(test_entities):
        print(f"  实体 {i+1}: {entity['type']} - {entity['layer']}")
    
    # 创建处理器
    processor = CADDataProcessor()
    
    try:
        print(f"\n🔧 测试主要分组方法 (group_entities)...")
        groups = processor.group_entities(test_entities, distance_threshold=20, debug=False)
        print(f"✅ group_entities 成功: {len(groups)} 个组")
        
        # 显示分组结果
        for i, group in enumerate(groups):
            if isinstance(group, dict):
                entities_list = group.get('entities', [])
                print(f"  组 {i+1}: {len(entities_list)} 个实体 (字典格式)")
                for j, entity in enumerate(entities_list):
                    print(f"    实体 {j+1}: {entity['type']} - {entity['layer']}")
            elif isinstance(group, list):
                print(f"  组 {i+1}: {len(group)} 个实体 (列表格式)")
                for j, entity in enumerate(group):
                    print(f"    实体 {j+1}: {entity['type']} - {entity['layer']}")
        
        print(f"\n🔧 测试处理专用分组方法 (group_entities_for_processing)...")
        groups2 = processor.group_entities_for_processing(test_entities)
        print(f"✅ group_entities_for_processing 成功: {len(groups2)} 个组")
        
        # 显示分组结果
        for i, group in enumerate(groups2):
            if isinstance(group, dict):
                entities_list = group.get('entities', [])
                print(f"  组 {i+1}: {len(entities_list)} 个实体 (字典格式)")
            elif isinstance(group, list):
                print(f"  组 {i+1}: {len(group)} 个实体 (列表格式)")
        
        print(f"\n🎉 所有测试通过！")
        print(f"✅ 原始错误 'group_entities_by_connectivity_and_layer' 方法不存在的问题已修复")
        print(f"✅ 分组功能正常工作")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return False

def test_special_layer_grouping():
    """测试特殊图层分组"""
    print(f"\n🏗️ 测试特殊图层分组")
    print("="*60)
    
    # 创建墙体测试数据
    wall_entities = [
        {'type': 'LINE', 'layer': 'WALL', 'points': [(0, 0), (100, 0)]},
        {'type': 'LINE', 'layer': 'WALL', 'points': [(100, 0), (100, 100)]},
        {'type': 'LINE', 'layer': 'WALL', 'points': [(100, 100), (0, 100)]},
        {'type': 'LINE', 'layer': 'WALL', 'points': [(0, 100), (0, 0)]}
    ]
    
    # 创建门窗测试数据
    door_window_entities = [
        {'type': 'LINE', 'layer': 'DOOR', 'points': [(50, 0), (50, 20)]},
        {'type': 'ARC', 'layer': 'DOOR', 'center': (50, 0), 'radius': 20, 'start_angle': 0, 'end_angle': 90}
    ]
    
    processor = CADDataProcessor()
    
    try:
        print(f"测试墙体分组...")
        wall_groups = processor._group_special_entities_by_layer(
            wall_entities, connection_threshold=10, entity_type="wall"
        )
        print(f"✅ 墙体分组成功: {len(wall_groups)} 个组")
        
        print(f"测试门窗分组...")
        door_window_groups = processor._group_special_entities_by_layer(
            door_window_entities, connection_threshold=50, entity_type="door_window"
        )
        print(f"✅ 门窗分组成功: {len(door_window_groups)} 个组")
        
        return True
        
    except Exception as e:
        print(f"❌ 特殊图层分组测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始分组功能修复测试")
    print("="*80)
    
    # 测试基本分组功能
    success1 = test_grouping_fix()
    
    # 测试特殊图层分组
    success2 = test_special_layer_grouping()
    
    print("\n" + "="*80)
    if success1 and success2:
        print("🎉 所有测试通过！分组功能修复成功！")
        print("✅ 原始问题已解决：'CADDataProcessor' object has no attribute 'group_entities_by_connectivity_and_layer'")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    print("="*80)
