#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动标注功能测试程序
专门测试门窗墙体的自动标注类型功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
from cad_data_processor import CADDataProcessor
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

class AutoLabelingTest:
    """自动标注测试类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("CAD自动标注功能测试")
        self.root.geometry("1200x800")
        
        # 测试状态
        self.processor = None
        self.test_results = []
        
        self.create_interface()
        self.create_comprehensive_test_data()
        
    def create_interface(self):
        """创建测试界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="自动标注测试", width=300)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 测试按钮
        ttk.Label(control_frame, text="图层识别测试", font=("Arial", 12, "bold")).pack(pady=10)
        
        ttk.Button(control_frame, text="1. 测试墙体图层识别", 
                  command=self.test_wall_layer_detection).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Button(control_frame, text="2. 测试门窗图层识别", 
                  command=self.test_door_window_layer_detection).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Button(control_frame, text="3. 测试栏杆图层识别", 
                  command=self.test_railing_layer_detection).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Separator(control_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        ttk.Label(control_frame, text="自动标注测试", font=("Arial", 12, "bold")).pack(pady=10)
        
        ttk.Button(control_frame, text="4. 测试墙体自动标注", 
                  command=self.test_wall_auto_labeling).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Button(control_frame, text="5. 测试门自动标注", 
                  command=self.test_door_auto_labeling).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Button(control_frame, text="6. 测试窗自动标注", 
                  command=self.test_window_auto_labeling).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Button(control_frame, text="7. 测试栏杆自动标注", 
                  command=self.test_railing_auto_labeling).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Separator(control_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        ttk.Label(control_frame, text="特征分析测试", font=("Arial", 12, "bold")).pack(pady=10)
        
        ttk.Button(control_frame, text="8. 测试特征提取", 
                  command=self.test_feature_extraction).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Button(control_frame, text="9. 测试类别推荐", 
                  command=self.test_category_recommendation).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Button(control_frame, text="10. 测试综合识别", 
                  command=self.test_comprehensive_recognition).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Separator(control_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        ttk.Button(control_frame, text="运行所有测试", 
                  command=self.run_all_tests,
                  style="Accent.TButton").pack(fill=tk.X, pady=5, padx=10)
        
        ttk.Button(control_frame, text="清除测试结果", 
                  command=self.clear_results).pack(fill=tk.X, pady=2, padx=10)
        
        # 状态显示
        ttk.Label(control_frame, text="测试状态", font=("Arial", 10, "bold")).pack(pady=(20, 5))
        
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(control_frame, textvariable=self.status_var, 
                                     foreground="blue")
        self.status_label.pack(pady=2)
        
        self.progress = ttk.Progressbar(control_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=5, padx=10)
        
        # 右侧显示区域
        display_frame = ttk.Frame(main_frame)
        display_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建notebook
        self.notebook = ttk.Notebook(display_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 测试结果视图
        self.results_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.results_frame, text="测试结果")
        
        # 可视化视图
        self.visual_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.visual_frame, text="可视化结果")
        
        # 创建测试结果显示
        self.create_results_display()
        
    def create_results_display(self):
        """创建测试结果显示"""
        # 结果文本框
        result_scroll_frame = ttk.Frame(self.results_frame)
        result_scroll_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.result_text = tk.Text(result_scroll_frame, wrap=tk.WORD, font=("Consolas", 10))
        result_scrollbar = ttk.Scrollbar(result_scroll_frame, orient=tk.VERTICAL, 
                                        command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=result_scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_comprehensive_test_data(self):
        """创建全面的测试数据"""
        self.test_data = {
            # 墙体测试数据 - 不同命名模式
            'walls': [
                # 中文墙体
                {'type': 'LINE', 'layer': '墙', 'points': [(0, 0), (1000, 0)], 'id': 'wall_cn_1'},
                {'type': 'LINE', 'layer': '墙体', 'points': [(1000, 0), (1000, 800)], 'id': 'wall_cn_2'},
                {'type': 'LINE', 'layer': '结构墙', 'points': [(1000, 800), (0, 800)], 'id': 'wall_cn_3'},
                {'type': 'LINE', 'layer': '承重墙', 'points': [(0, 800), (0, 0)], 'id': 'wall_cn_4'},
                
                # 英文墙体
                {'type': 'LINE', 'layer': 'WALL', 'points': [(1200, 0), (1400, 0)], 'id': 'wall_en_1'},
                {'type': 'LINE', 'layer': 'WALLS', 'points': [(1400, 0), (1400, 200)], 'id': 'wall_en_2'},
                {'type': 'LINE', 'layer': 'PARTITION', 'points': [(1400, 200), (1200, 200)], 'id': 'wall_en_3'},
                
                # 编码墙体
                {'type': 'LINE', 'layer': 'A-WALL', 'points': [(1600, 0), (1800, 0)], 'id': 'wall_code_1'},
                {'type': 'LINE', 'layer': '01-墙', 'points': [(1800, 0), (1800, 200)], 'id': 'wall_code_2'},
                {'type': 'LINE', 'layer': 'L-墙', 'points': [(1800, 200), (1600, 200)], 'id': 'wall_code_3'},
            ],
            
            # 门测试数据
            'doors': [
                # 中文门
                {'type': 'LINE', 'layer': '门', 'points': [(400, 0), (400, 80)], 'id': 'door_cn_1'},
                {'type': 'ARC', 'layer': '门', 'center': (400, 0), 'radius': 80, 'start_angle': 0, 'end_angle': 90, 'id': 'door_cn_2'},
                {'type': 'LINE', 'layer': '门洞', 'points': [(600, 0), (600, 90)], 'id': 'door_cn_3'},
                
                # 英文门
                {'type': 'LINE', 'layer': 'DOOR', 'points': [(800, 0), (800, 80)], 'id': 'door_en_1'},
                {'type': 'ARC', 'layer': 'DOORS', 'center': (800, 0), 'radius': 80, 'start_angle': 0, 'end_angle': 90, 'id': 'door_en_2'},
                
                # 编码门
                {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(1000, 0), (1000, 80)], 'id': 'door_code_1'},
                {'type': 'LINE', 'layer': '02-门', 'points': [(1200, 0), (1200, 80)], 'id': 'door_code_2'},
            ],
            
            # 窗测试数据
            'windows': [
                # 中文窗
                {'type': 'LINE', 'layer': '窗', 'points': [(200, 800), (300, 800)], 'id': 'window_cn_1'},
                {'type': 'LINE', 'layer': '窗户', 'points': [(500, 800), (600, 800)], 'id': 'window_cn_2'},
                {'type': 'LINE', 'layer': '窗洞', 'points': [(700, 800), (800, 800)], 'id': 'window_cn_3'},
                
                # 英文窗
                {'type': 'LINE', 'layer': 'WINDOW', 'points': [(1200, 200), (1300, 200)], 'id': 'window_en_1'},
                {'type': 'LINE', 'layer': 'WINDOWS', 'points': [(1400, 200), (1500, 200)], 'id': 'window_en_2'},
                
                # 编码窗
                {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(1600, 200), (1700, 200)], 'id': 'window_code_1'},
                {'type': 'LINE', 'layer': '03-窗', 'points': [(1800, 200), (1900, 200)], 'id': 'window_code_2'},
            ],
            
            # 栏杆测试数据
            'railings': [
                # 中文栏杆
                {'type': 'LINE', 'layer': '栏杆', 'points': [(500, 600), (600, 600)], 'id': 'railing_cn_1'},
                {'type': 'LINE', 'layer': '护栏', 'points': [(500, 610), (600, 610)], 'id': 'railing_cn_2'},
                {'type': 'LINE', 'layer': '扶手', 'points': [(500, 620), (600, 620)], 'id': 'railing_cn_3'},
                
                # 英文栏杆
                {'type': 'LINE', 'layer': 'RAILING', 'points': [(700, 600), (800, 600)], 'id': 'railing_en_1'},
                {'type': 'LINE', 'layer': 'HANDRAIL', 'points': [(700, 610), (800, 610)], 'id': 'railing_en_2'},
                
                # 编码栏杆
                {'type': 'LINE', 'layer': 'A-RAILING', 'points': [(900, 600), (1000, 600)], 'id': 'railing_code_1'},
                {'type': 'LINE', 'layer': '04-栏杆', 'points': [(900, 610), (1000, 610)], 'id': 'railing_code_2'},
            ]
        }
        
    def log_result(self, test_name, result, details=""):
        """记录测试结果"""
        timestamp = time.strftime("%H:%M:%S")
        status = "✅ 通过" if result else "❌ 失败"
        
        log_entry = f"[{timestamp}] {test_name}: {status}\n"
        if details:
            log_entry += f"  详情: {details}\n"
        log_entry += "-" * 50 + "\n"
        
        self.result_text.insert(tk.END, log_entry)
        self.result_text.see(tk.END)
        self.root.update()
        
        self.test_results.append({
            'test': test_name,
            'result': result,
            'details': details,
            'timestamp': timestamp
        })
    
    def test_wall_layer_detection(self):
        """测试墙体图层识别"""
        self.status_var.set("测试墙体图层识别...")
        self.progress.start()
        
        try:
            if not self.processor:
                self.processor = CADDataProcessor()
            
            # 获取所有墙体实体
            all_wall_entities = self.test_data['walls']
            
            # 执行图层识别
            detected_layers = self.processor._detect_special_layers(
                all_wall_entities,
                self.processor.wall_layer_patterns,
                debug=False,
                layer_type="墙体"
            )
            
            # 期望的墙体图层
            expected_layers = {'墙', '墙体', '结构墙', '承重墙', 'WALL', 'WALLS', 'PARTITION', 'A-WALL', '01-墙', 'L-墙'}
            
            # 验证结果
            success = detected_layers == expected_layers
            
            details = f"""
期望识别图层: {expected_layers}
实际识别图层: {detected_layers}
识别准确率: {len(detected_layers & expected_layers) / len(expected_layers) * 100:.1f}%
遗漏图层: {expected_layers - detected_layers}
误识别图层: {detected_layers - expected_layers}
"""
            
            self.log_result("墙体图层识别测试", success, details)
            
        except Exception as e:
            self.log_result("墙体图层识别测试", False, f"错误: {str(e)}")
        
        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_door_window_layer_detection(self):
        """测试门窗图层识别"""
        self.status_var.set("测试门窗图层识别...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 获取所有门窗实体
            all_door_window_entities = self.test_data['doors'] + self.test_data['windows']

            # 执行图层识别
            detected_layers = self.processor._detect_special_layers(
                all_door_window_entities,
                self.processor.door_window_layer_patterns,
                debug=False,
                layer_type="门窗"
            )

            # 期望的门窗图层
            expected_layers = {'门', '门洞', 'DOOR', 'DOORS', 'A-DOOR', '02-门',
                             '窗', '窗户', '窗洞', 'WINDOW', 'WINDOWS', 'A-WINDOW', '03-窗'}

            # 验证结果
            success = detected_layers == expected_layers

            details = f"""
期望识别图层: {expected_layers}
实际识别图层: {detected_layers}
识别准确率: {len(detected_layers & expected_layers) / len(expected_layers) * 100:.1f}%
遗漏图层: {expected_layers - detected_layers}
误识别图层: {detected_layers - expected_layers}
"""

            self.log_result("门窗图层识别测试", success, details)

        except Exception as e:
            self.log_result("门窗图层识别测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_railing_layer_detection(self):
        """测试栏杆图层识别"""
        self.status_var.set("测试栏杆图层识别...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 获取所有栏杆实体
            all_railing_entities = self.test_data['railings']

            # 执行图层识别
            detected_layers = self.processor._detect_special_layers(
                all_railing_entities,
                self.processor.railing_layer_patterns,
                debug=False,
                layer_type="栏杆"
            )

            # 期望的栏杆图层
            expected_layers = {'栏杆', '护栏', '扶手', 'RAILING', 'HANDRAIL', 'A-RAILING', '04-栏杆'}

            # 验证结果
            success = detected_layers == expected_layers

            details = f"""
期望识别图层: {expected_layers}
实际识别图层: {detected_layers}
识别准确率: {len(detected_layers & expected_layers) / len(expected_layers) * 100:.1f}%
遗漏图层: {expected_layers - detected_layers}
误识别图层: {detected_layers - expected_layers}
"""

            self.log_result("栏杆图层识别测试", success, details)

        except Exception as e:
            self.log_result("栏杆图层识别测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_wall_auto_labeling(self):
        """测试墙体自动标注"""
        self.status_var.set("测试墙体自动标注...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 测试不同类型的墙体组
            wall_test_cases = [
                ('中文墙体', [e for e in self.test_data['walls'] if '墙' in e['layer']]),
                ('英文墙体', [e for e in self.test_data['walls'] if e['layer'] in ['WALL', 'WALLS', 'PARTITION']]),
                ('编码墙体', [e for e in self.test_data['walls'] if 'A-' in e['layer'] or '-墙' in e['layer']])
            ]

            results = []
            overall_success = True

            for case_name, entities in wall_test_cases:
                if not entities:
                    continue

                # 提取特征
                features = self.processor.extract_features(entities)

                # 类别推荐
                suggestions = self.processor.suggest_category(features, entities)

                # 图层推断
                inferred_category = self.processor._infer_category_from_layer(entities)

                # 检查是否正确识别为墙体
                wall_identified = (
                    any('wall' in str(s).lower() or '墙' in str(s) for s in suggestions) or
                    'wall' in inferred_category.lower() or '墙' in inferred_category
                )

                if not wall_identified:
                    overall_success = False

                results.append(f"""
{case_name}:
  实体数: {len(entities)}
  特征: 长度={features.get('length', 0):.1f}, 面积={features.get('area', 0):.1f}
  推荐: {suggestions}
  推断: {inferred_category}
  识别: {'✅' if wall_identified else '❌'}
""")

            details = "\n".join(results)
            self.log_result("墙体自动标注测试", overall_success, details)

        except Exception as e:
            self.log_result("墙体自动标注测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_door_auto_labeling(self):
        """测试门自动标注"""
        self.status_var.set("测试门自动标注...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 测试不同类型的门
            door_test_cases = [
                ('中文门', [e for e in self.test_data['doors'] if '门' in e['layer']]),
                ('英文门', [e for e in self.test_data['doors'] if 'DOOR' in e['layer']]),
                ('编码门', [e for e in self.test_data['doors'] if 'A-DOOR' in e['layer'] or '-门' in e['layer']])
            ]

            results = []
            overall_success = True

            for case_name, entities in door_test_cases:
                if not entities:
                    continue

                # 提取特征
                features = self.processor.extract_features(entities)

                # 类别推荐
                suggestions = self.processor.suggest_category(features, entities)

                # 图层推断
                inferred_category = self.processor._infer_category_from_layer(entities)

                # 检查是否正确识别为门
                door_identified = (
                    any('door' in str(s).lower() or '门' in str(s) for s in suggestions) or
                    'door' in inferred_category.lower() or '门' in inferred_category
                )

                if not door_identified:
                    overall_success = False

                results.append(f"""
{case_name}:
  实体数: {len(entities)}
  特征: 长度={features.get('length', 0):.1f}, 面积={features.get('area', 0):.1f}
  推荐: {suggestions}
  推断: {inferred_category}
  识别: {'✅' if door_identified else '❌'}
""")

            details = "\n".join(results)
            self.log_result("门自动标注测试", overall_success, details)

        except Exception as e:
            self.log_result("门自动标注测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_window_auto_labeling(self):
        """测试窗自动标注"""
        self.status_var.set("测试窗自动标注...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 测试不同类型的窗
            window_test_cases = [
                ('中文窗', [e for e in self.test_data['windows'] if '窗' in e['layer']]),
                ('英文窗', [e for e in self.test_data['windows'] if 'WINDOW' in e['layer']]),
                ('编码窗', [e for e in self.test_data['windows'] if 'A-WINDOW' in e['layer'] or '-窗' in e['layer']])
            ]

            results = []
            overall_success = True

            for case_name, entities in window_test_cases:
                if not entities:
                    continue

                # 提取特征
                features = self.processor.extract_features(entities)

                # 类别推荐
                suggestions = self.processor.suggest_category(features, entities)

                # 图层推断
                inferred_category = self.processor._infer_category_from_layer(entities)

                # 检查是否正确识别为窗
                window_identified = (
                    any('window' in str(s).lower() or '窗' in str(s) for s in suggestions) or
                    'window' in inferred_category.lower() or '窗' in inferred_category
                )

                if not window_identified:
                    overall_success = False

                results.append(f"""
{case_name}:
  实体数: {len(entities)}
  特征: 长度={features.get('length', 0):.1f}, 面积={features.get('area', 0):.1f}
  推荐: {suggestions}
  推断: {inferred_category}
  识别: {'✅' if window_identified else '❌'}
""")

            details = "\n".join(results)
            self.log_result("窗自动标注测试", overall_success, details)

        except Exception as e:
            self.log_result("窗自动标注测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_railing_auto_labeling(self):
        """测试栏杆自动标注"""
        self.status_var.set("测试栏杆自动标注...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 测试不同类型的栏杆
            railing_test_cases = [
                ('中文栏杆', [e for e in self.test_data['railings'] if any(x in e['layer'] for x in ['栏杆', '护栏', '扶手'])]),
                ('英文栏杆', [e for e in self.test_data['railings'] if any(x in e['layer'] for x in ['RAILING', 'HANDRAIL'])]),
                ('编码栏杆', [e for e in self.test_data['railings'] if 'A-RAILING' in e['layer'] or '-栏杆' in e['layer']])
            ]

            results = []
            overall_success = True

            for case_name, entities in railing_test_cases:
                if not entities:
                    continue

                # 提取特征
                features = self.processor.extract_features(entities)

                # 类别推荐
                suggestions = self.processor.suggest_category(features, entities)

                # 图层推断
                inferred_category = self.processor._infer_category_from_layer(entities)

                # 检查是否正确识别为栏杆
                railing_identified = (
                    any(any(x in str(s).lower() for x in ['railing', 'handrail', '栏杆', '护栏', '扶手']) for s in suggestions) or
                    any(x in inferred_category.lower() for x in ['railing', 'handrail', '栏杆', '护栏', '扶手'])
                )

                if not railing_identified:
                    overall_success = False

                results.append(f"""
{case_name}:
  实体数: {len(entities)}
  特征: 长度={features.get('length', 0):.1f}, 面积={features.get('area', 0):.1f}
  推荐: {suggestions}
  推断: {inferred_category}
  识别: {'✅' if railing_identified else '❌'}
""")

            details = "\n".join(results)
            self.log_result("栏杆自动标注测试", overall_success, details)

        except Exception as e:
            self.log_result("栏杆自动标注测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_feature_extraction(self):
        """测试特征提取功能"""
        self.status_var.set("测试特征提取...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 测试不同类型实体的特征提取
            test_cases = [
                ('墙体', self.test_data['walls'][:3]),
                ('门', self.test_data['doors'][:3]),
                ('窗', self.test_data['windows'][:3]),
                ('栏杆', self.test_data['railings'][:3])
            ]

            results = []
            overall_success = True

            for case_name, entities in test_cases:
                try:
                    features = self.processor.extract_features(entities)

                    # 验证特征完整性
                    required_features = ['length', 'area', 'closed', 'entity_count']
                    missing_features = [f for f in required_features if f not in features]

                    if missing_features:
                        overall_success = False

                    results.append(f"""
{case_name}:
  实体数: {len(entities)}
  长度: {features.get('length', 0):.2f}
  面积: {features.get('area', 0):.2f}
  封闭性: {features.get('closed', 0)}
  实体计数: {features.get('entity_count', 0)}
  缺失特征: {missing_features if missing_features else '无'}
""")

                except Exception as e:
                    overall_success = False
                    results.append(f"{case_name}: 特征提取失败 - {str(e)}")

            details = "\n".join(results)
            self.log_result("特征提取测试", overall_success, details)

        except Exception as e:
            self.log_result("特征提取测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_category_recommendation(self):
        """测试类别推荐功能"""
        self.status_var.set("测试类别推荐...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 测试类别推荐的准确性
            test_cases = [
                ('墙体', self.test_data['walls'][:5], ['wall', '墙']),
                ('门', self.test_data['doors'][:5], ['door', '门']),
                ('窗', self.test_data['windows'][:5], ['window', '窗']),
                ('栏杆', self.test_data['railings'][:5], ['railing', 'handrail', '栏杆', '护栏'])
            ]

            results = []
            overall_success = True

            for case_name, entities, expected_keywords in test_cases:
                try:
                    features = self.processor.extract_features(entities)
                    suggestions = self.processor.suggest_category(features, entities)

                    # 检查推荐是否包含期望的关键词
                    suggestion_text = ' '.join(str(s).lower() for s in suggestions)
                    keyword_found = any(keyword.lower() in suggestion_text for keyword in expected_keywords)

                    if not keyword_found:
                        overall_success = False

                    results.append(f"""
{case_name}:
  推荐结果: {suggestions}
  期望关键词: {expected_keywords}
  匹配成功: {'✅' if keyword_found else '❌'}
""")

                except Exception as e:
                    overall_success = False
                    results.append(f"{case_name}: 推荐失败 - {str(e)}")

            details = "\n".join(results)
            self.log_result("类别推荐测试", overall_success, details)

        except Exception as e:
            self.log_result("类别推荐测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_comprehensive_recognition(self):
        """测试综合识别功能"""
        self.status_var.set("测试综合识别...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 混合所有类型的实体进行综合测试
            all_entities = (self.test_data['walls'] + self.test_data['doors'] +
                          self.test_data['windows'] + self.test_data['railings'])

            # 执行完整的分组和标注流程
            groups = self.processor.group_entities(all_entities, distance_threshold=20, debug=False)

            # 统计识别结果
            recognition_stats = {
                'wall': 0, 'door': 0, 'window': 0, 'railing': 0, 'other': 0
            }

            group_details = []

            for i, group in enumerate(groups):
                if isinstance(group, dict):
                    entities = group.get('entities', [])
                    label = group.get('label', '未标注')
                else:
                    entities = group
                    label = '未标注'

                if not entities:
                    continue

                # 分析组的主要图层
                layers = [e.get('layer', '') for e in entities]
                main_layer = max(set(layers), key=layers.count) if layers else ''

                # 推断类别
                features = self.processor.extract_features(entities)
                suggestions = self.processor.suggest_category(features, entities)
                inferred = self.processor._infer_category_from_layer(entities)

                # 统计识别类型
                combined_text = f"{suggestions} {inferred} {main_layer}".lower()
                if any(x in combined_text for x in ['wall', '墙']):
                    recognition_stats['wall'] += 1
                    recognized_type = 'wall'
                elif any(x in combined_text for x in ['door', '门']):
                    recognition_stats['door'] += 1
                    recognized_type = 'door'
                elif any(x in combined_text for x in ['window', '窗']):
                    recognition_stats['window'] += 1
                    recognized_type = 'window'
                elif any(x in combined_text for x in ['railing', 'handrail', '栏杆', '护栏']):
                    recognition_stats['railing'] += 1
                    recognized_type = 'railing'
                else:
                    recognition_stats['other'] += 1
                    recognized_type = 'other'

                group_details.append(f"组{i+1}: {len(entities)}个实体, 主图层:{main_layer}, 识别为:{recognized_type}")

            # 计算识别准确率
            total_groups = len(groups)
            recognized_groups = sum(recognition_stats[k] for k in ['wall', 'door', 'window', 'railing'])
            accuracy = (recognized_groups / total_groups * 100) if total_groups > 0 else 0

            success = accuracy >= 70  # 70%以上认为成功

            details = f"""
总实体数: {len(all_entities)}
生成组数: {total_groups}
识别统计:
  墙体: {recognition_stats['wall']}组
  门: {recognition_stats['door']}组
  窗: {recognition_stats['window']}组
  栏杆: {recognition_stats['railing']}组
  其他: {recognition_stats['other']}组

识别准确率: {accuracy:.1f}%
组详情:
""" + "\n".join(group_details[:10])  # 只显示前10个组

            self.log_result("综合识别测试", success, details)

            # 可视化结果
            self.visualize_recognition_result(groups)

        except Exception as e:
            self.log_result("综合识别测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def visualize_recognition_result(self, groups):
        """可视化识别结果"""
        try:
            # 清除之前的显示
            for widget in self.visual_frame.winfo_children():
                widget.destroy()

            if not groups:
                ttk.Label(self.visual_frame, text="无识别结果").pack(expand=True)
                return

            # 创建matplotlib图形
            fig, ax = plt.subplots(figsize=(12, 8))

            # 定义类型颜色
            type_colors = {
                'wall': 'brown',
                'door': 'orange',
                'window': 'blue',
                'railing': 'green',
                'other': 'gray'
            }

            for i, group in enumerate(groups):
                if isinstance(group, dict):
                    entities = group.get('entities', [])
                else:
                    entities = group

                if not entities:
                    continue

                # 确定组的类型
                layers = [e.get('layer', '') for e in entities]
                main_layer = max(set(layers), key=layers.count) if layers else ''

                # 推断类型
                combined_text = main_layer.lower()
                if any(x in combined_text for x in ['wall', '墙']):
                    color = type_colors['wall']
                elif any(x in combined_text for x in ['door', '门']):
                    color = type_colors['door']
                elif any(x in combined_text for x in ['window', '窗']):
                    color = type_colors['window']
                elif any(x in combined_text for x in ['railing', 'handrail', '栏杆', '护栏']):
                    color = type_colors['railing']
                else:
                    color = type_colors['other']

                # 绘制实体
                for entity in entities:
                    try:
                        if entity.get('type') == 'LINE' and 'points' in entity:
                            points = entity['points']
                            if len(points) >= 2:
                                ax.plot([points[0][0], points[1][0]],
                                       [points[0][1], points[1][1]],
                                       color=color, linewidth=2, alpha=0.8)

                        elif entity.get('type') == 'ARC' and 'center' in entity:
                            center = entity['center']
                            radius = entity.get('radius', 10)
                            start_angle = np.radians(entity.get('start_angle', 0))
                            end_angle = np.radians(entity.get('end_angle', 360))

                            angles = np.linspace(start_angle, end_angle, 50)
                            x = center[0] + radius * np.cos(angles)
                            y = center[1] + radius * np.sin(angles)
                            ax.plot(x, y, color=color, linewidth=2, alpha=0.8)

                    except Exception:
                        continue

            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            ax.set_title("自动识别结果可视化", fontsize=14, fontweight='bold')

            # 添加图例
            legend_elements = [plt.Line2D([0], [0], color=color, lw=3, label=type_name)
                             for type_name, color in type_colors.items()]
            ax.legend(handles=legend_elements, loc='upper right')

            # 嵌入到tkinter
            canvas = FigureCanvasTkAgg(fig, self.visual_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            # 切换到可视化标签页
            self.notebook.select(self.visual_frame)

        except Exception as e:
            print(f"可视化失败: {e}")

    def run_all_tests(self):
        """运行所有测试"""
        self.status_var.set("运行所有测试...")

        # 清除之前的结果
        self.clear_results()

        def run_tests():
            try:
                tests = [
                    ("墙体图层识别", self.test_wall_layer_detection),
                    ("门窗图层识别", self.test_door_window_layer_detection),
                    ("栏杆图层识别", self.test_railing_layer_detection),
                    ("墙体自动标注", self.test_wall_auto_labeling),
                    ("门自动标注", self.test_door_auto_labeling),
                    ("窗自动标注", self.test_window_auto_labeling),
                    ("栏杆自动标注", self.test_railing_auto_labeling),
                    ("特征提取", self.test_feature_extraction),
                    ("类别推荐", self.test_category_recommendation),
                    ("综合识别", self.test_comprehensive_recognition)
                ]

                for test_name, test_func in tests:
                    self.status_var.set(f"正在运行: {test_name}")
                    test_func()
                    time.sleep(0.5)  # 测试间隔

                # 生成测试报告
                self.generate_test_report()

            except Exception as e:
                self.log_result("测试套件", False, f"测试套件执行失败: {str(e)}")

            finally:
                self.status_var.set("所有测试完成")

        threading.Thread(target=run_tests, daemon=True).start()

    def clear_results(self):
        """清除测试结果"""
        self.result_text.delete(1.0, tk.END)
        self.test_results.clear()
        self.status_var.set("测试结果已清除")

    def generate_test_report(self):
        """生成测试报告"""
        if not self.test_results:
            return

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['result'])
        failed_tests = total_tests - passed_tests

        report = f"""
{'='*60}
自动标注功能测试报告
{'='*60}
测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
总测试数: {total_tests}
通过测试: {passed_tests}
失败测试: {failed_tests}
成功率: {(passed_tests/total_tests*100):.1f}%

核心功能测试结果:
"""

        # 分类统计
        categories = {
            '图层识别': ['墙体图层识别测试', '门窗图层识别测试', '栏杆图层识别测试'],
            '自动标注': ['墙体自动标注测试', '门自动标注测试', '窗自动标注测试', '栏杆自动标注测试'],
            '特征分析': ['特征提取测试', '类别推荐测试'],
            '综合功能': ['综合识别测试']
        }

        for category, test_names in categories.items():
            category_results = [r for r in self.test_results if r['test'] in test_names]
            if category_results:
                category_passed = sum(1 for r in category_results if r['result'])
                category_total = len(category_results)
                category_rate = (category_passed / category_total * 100) if category_total > 0 else 0

                report += f"\n{category}: {category_passed}/{category_total} ({category_rate:.1f}%)\n"
                for result in category_results:
                    status = "✅" if result['result'] else "❌"
                    report += f"  {status} {result['test']}\n"

        report += f"\n{'='*60}\n"

        self.result_text.insert(tk.END, report)
        self.result_text.see(tk.END)

    def run(self):
        """运行测试应用"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = AutoLabelingTest()
        app.run()
    except Exception as e:
        print(f"应用启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
