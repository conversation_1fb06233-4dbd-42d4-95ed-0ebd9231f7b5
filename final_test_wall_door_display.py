#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终测试墙体和门窗显示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_enhanced import EnhancedCADProcessor

def create_simple_test_data():
    """创建简单的测试数据"""
    entities = []
    
    # 墙体线条 (A-WALL图层)
    wall_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (1000, 0)], 'color': 7, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(1000, 0), (1000, 1000)], 'color': 7, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(1000, 1000), (0, 1000)], 'color': 7, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 1000), (0, 0)], 'color': 7, 'linetype': 'CONTINUOUS'},
    ]
    
    # 门窗线条 (A-WINDOW图层)
    window_entities = [
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(200, -50), (400, -50)], 'color': 3, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(200, 50), (400, 50)], 'color': 3, 'linetype': 'CONTINUOUS'},
    ]
    
    # 门线条 (A-DOOR图层)
    door_entities = [
        {'type': 'ARC', 'layer': 'A-DOOR', 'center': (1050, 500), 'radius': 300, 'start_angle': 0, 'end_angle': 90, 'color': 2},
        {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(1050, 500), (1350, 500)], 'color': 2, 'linetype': 'CONTINUOUS'},
    ]
    
    entities.extend(wall_entities)
    entities.extend(window_entities)
    entities.extend(door_entities)
    
    return entities

def test_complete_workflow():
    """测试完整的工作流程"""
    print("🧪 测试完整的墙体和门窗显示工作流程")
    print("="*60)
    
    # 创建测试数据
    test_entities = create_simple_test_data()
    print(f"创建测试数据: {len(test_entities)} 个实体")
    
    # 创建处理器
    processor = EnhancedCADProcessor()
    processor.entities = test_entities
    processor.all_groups = []
    processor.auto_labeled_entities = []
    processor.labeled_entities = []
    processor.groups_info = []
    
    # 1. 特殊图层识别
    print(f"\n1. 特殊图层识别:")
    wall_layers = processor.processor._detect_special_layers(
        test_entities, processor.processor.wall_layer_patterns, debug=False, layer_type="墙体"
    )
    door_window_layers = processor.processor._detect_special_layers(
        test_entities, processor.processor.door_window_layer_patterns, debug=False, layer_type="门窗"
    )
    
    print(f"   墙体图层: {wall_layers}")
    print(f"   门窗图层: {door_window_layers}")
    
    # 2. 分组处理
    print(f"\n2. 分组处理:")
    wall_entities = [e for e in test_entities if e['layer'] in wall_layers]
    door_window_entities = [e for e in test_entities if e['layer'] in door_window_layers]
    
    auto_groups = []
    
    # 处理墙体
    if wall_entities:
        wall_groups = processor.processor._group_special_entities_by_layer(
            wall_entities, connection_threshold=10, entity_type="wall"
        )
        
        for group in wall_groups:
            entities_list = group.get('entities', []) if isinstance(group, dict) else group
            
            # 自动标注
            for entity in entities_list:
                if isinstance(entity, dict):
                    entity['label'] = '墙体'
                    entity['auto_labeled'] = True
                    entity['confidence'] = 0.9
            
            auto_groups.append(entities_list)
            processor.auto_labeled_entities.extend(entities_list)
        
        print(f"   墙体分组: {len(wall_groups)} 个组")
    
    # 处理门窗
    if door_window_entities:
        door_window_groups = processor.processor._group_special_entities_by_layer(
            door_window_entities, connection_threshold=100, entity_type="door_window"
        )
        
        for group in door_window_groups:
            entities_list = group.get('entities', []) if isinstance(group, dict) else group
            
            # 自动标注
            for entity in entities_list:
                if isinstance(entity, dict):
                    entity['label'] = '门窗'
                    entity['auto_labeled'] = True
                    entity['confidence'] = 0.8
            
            auto_groups.append(entities_list)
            processor.auto_labeled_entities.extend(entities_list)
        
        print(f"   门窗分组: {len(door_window_groups)} 个组")
    
    # 3. 设置组数据
    print(f"\n3. 设置组数据:")
    processor.all_groups = auto_groups
    print(f"   总组数: {len(auto_groups)}")
    print(f"   自动标注实体: {len(processor.auto_labeled_entities)}")
    
    # 4. 更新组信息
    print(f"\n4. 更新组信息:")
    processor._update_groups_info()
    print(f"   组信息数量: {len(processor.groups_info)}")
    
    # 显示组信息详情
    for i, info in enumerate(processor.groups_info):
        print(f"     组 {i+1}: 状态={info.get('status')}, 标签={info.get('label')}, 类型={info.get('group_type')}, 实体数={info.get('entity_count')}")
    
    # 5. 测试可视化器
    print(f"\n5. 测试可视化器:")
    try:
        # 测试单个组的可视化
        if auto_groups:
            first_group = auto_groups[0]
            print(f"   测试第一个组的可视化: {len(first_group)} 个实体")
            
            # 这里我们只测试不会崩溃，不实际绘制
            # processor.visualizer.visualize_entity_group(first_group, {})
            print(f"   ✅ 组可视化测试通过")
        
    except Exception as e:
        print(f"   ❌ 组可视化测试失败: {e}")
    
    # 6. 测试组信息获取
    print(f"\n6. 测试组信息获取:")
    groups_info = processor.get_groups_info()
    print(f"   get_groups_info() 返回: {len(groups_info)} 个组")
    
    # 7. 测试待处理组更新
    print(f"\n7. 测试待处理组更新:")
    processor._update_pending_manual_groups()
    print(f"   待处理组数: {len(processor.pending_manual_groups)}")
    
    # 8. 统计结果
    print(f"\n8. 最终统计:")
    wall_count = len([e for e in processor.auto_labeled_entities if e.get('label') == '墙体'])
    door_window_count = len([e for e in processor.auto_labeled_entities if e.get('label') == '门窗'])
    
    print(f"   墙体实体: {wall_count} 个")
    print(f"   门窗实体: {door_window_count} 个")
    print(f"   总自动标注实体: {len(processor.auto_labeled_entities)} 个")
    print(f"   总组数: {len(processor.all_groups)} 个")
    print(f"   组信息数: {len(processor.groups_info)} 个")
    
    # 检查是否有墙体和门窗组
    wall_groups_count = len([info for info in processor.groups_info if info.get('group_type') == 'wall'])
    door_window_groups_count = len([info for info in processor.groups_info if info.get('group_type') == 'door_window'])
    
    print(f"   墙体组数: {wall_groups_count} 个")
    print(f"   门窗组数: {door_window_groups_count} 个")
    
    # 9. 验证结果
    print(f"\n9. 验证结果:")
    success = True
    
    if wall_count == 0:
        print(f"   ❌ 没有墙体实体被标注")
        success = False
    else:
        print(f"   ✅ 墙体实体标注正常: {wall_count} 个")
    
    if door_window_count == 0:
        print(f"   ❌ 没有门窗实体被标注")
        success = False
    else:
        print(f"   ✅ 门窗实体标注正常: {door_window_count} 个")
    
    if len(processor.groups_info) == 0:
        print(f"   ❌ 没有组信息生成")
        success = False
    else:
        print(f"   ✅ 组信息生成正常: {len(processor.groups_info)} 个")
    
    if wall_groups_count == 0:
        print(f"   ❌ 没有墙体组")
        success = False
    else:
        print(f"   ✅ 墙体组正常: {wall_groups_count} 个")
    
    if door_window_groups_count == 0:
        print(f"   ❌ 没有门窗组")
        success = False
    else:
        print(f"   ✅ 门窗组正常: {door_window_groups_count} 个")
    
    return success

def main():
    """主测试函数"""
    print("🚀 开始最终测试墙体和门窗显示")
    
    try:
        success = test_complete_workflow()
        
        print(f"\n" + "="*60)
        if success:
            print(f"🎉 所有测试通过！墙体和门窗显示功能正常工作。")
            print(f"💡 现在主程序应该能够正确显示墙体和门窗组了。")
        else:
            print(f"⚠️ 部分测试失败，可能还需要进一步修复。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
