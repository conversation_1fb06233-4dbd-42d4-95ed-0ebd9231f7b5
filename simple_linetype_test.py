#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的线型测试
"""

def test_case_sensitivity():
    """测试大小写敏感性修复"""
    
    print("🧪 测试线型大小写处理")
    print("=" * 40)
    
    # 模拟修复前的逻辑
    def old_check(linetype_name):
        return linetype_name != 'CONTINUOUS'
    
    # 修复后的逻辑
    def new_check(linetype_name):
        return linetype_name.upper() not in ['CONTINUOUS', 'BYLAYER', 'BYBLOCK']
    
    # 测试用例
    test_cases = [
        'CONTINUOUS',
        'Continuous', 
        'continuous',
        'BYLAYER',
        'Bylayer',
        'BYBLOCK',
        'Byblock',
        'DASHED',
        'HIDDEN'
    ]
    
    print("线型名称测试结果:")
    print("线型名称      | 修复前 | 修复后 | 说明")
    print("-" * 50)
    
    for linetype in test_cases:
        old_result = old_check(linetype)
        new_result = new_check(linetype)
        
        if linetype.upper() in ['CONTINUOUS', 'BYLAYER', 'BYBLOCK']:
            expected = False
            status = "✅" if new_result == expected else "❌"
        else:
            expected = True
            status = "✅" if new_result == expected else "❌"
        
        print(f"{linetype:12} | {str(old_result):6} | {str(new_result):6} | {status}")
    
    print("\n📋 修复说明:")
    print("- 修复前: 只检查 'CONTINUOUS' (全大写)")
    print("- 修复后: 检查 'CONTINUOUS', 'BYLAYER', 'BYBLOCK' (不区分大小写)")
    print("- 这样可以正确识别 'Continuous' 等变体，避免误报警告")

def test_linetype_resolution():
    """测试线型解析逻辑"""
    
    print("\n🧪 测试线型解析逻辑")
    print("=" * 40)
    
    # 模拟修复前的逻辑
    def old_resolve(entity_linetype):
        if entity_linetype and entity_linetype not in ['BYLAYER', 'BYBLOCK', 'CONTINUOUS']:
            return entity_linetype
        return 'CONTINUOUS'
    
    # 修复后的逻辑
    def new_resolve(entity_linetype):
        if entity_linetype and entity_linetype.upper() not in ['BYLAYER', 'BYBLOCK', 'CONTINUOUS']:
            return entity_linetype
        return 'CONTINUOUS'
    
    # 测试用例
    test_cases = [
        'CONTINUOUS',
        'Continuous',
        'continuous', 
        'BYLAYER',
        'Bylayer',
        'BYBLOCK',
        'Byblock',
        'DASHED',
        'HIDDEN'
    ]
    
    print("线型解析测试结果:")
    print("输入线型      | 修复前     | 修复后     | 说明")
    print("-" * 55)
    
    for linetype in test_cases:
        old_result = old_resolve(linetype)
        new_result = new_resolve(linetype)
        
        if linetype.upper() in ['CONTINUOUS', 'BYLAYER', 'BYBLOCK']:
            expected = 'CONTINUOUS'
            status = "✅" if new_result == expected else "❌"
        else:
            expected = linetype
            status = "✅" if new_result == expected else "❌"
        
        print(f"{linetype:12} | {old_result:10} | {new_result:10} | {status}")

if __name__ == "__main__":
    test_case_sensitivity()
    test_linetype_resolution()
