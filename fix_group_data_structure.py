#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复分组数据结构问题
确保所有分组方法返回统一的字典格式
"""

import os
import sys

def fix_group_data_structure():
    """修复分组数据结构问题"""
    print("🔧 修复分组数据结构...")
    
    try:
        # 读取cad_data_processor.py文件
        with open('cad_data_processor.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. 修复_group_special_entities_by_layer方法，确保返回字典格式
        method_start = content.find('def _group_special_entities_by_layer(self, entities, connection_threshold=10, entity_type="wall"):')
        if method_start != -1:
            print("  修复_group_special_entities_by_layer方法...")
            
            # 找到方法结束位置
            method_end = content.find('\n    def ', method_start + 1)
            if method_end == -1:
                method_end = content.find('\nclass ', method_start + 1)
            if method_end == -1:
                method_end = len(content)
            
            # 替换方法内容
            new_method = '''def _group_special_entities_by_layer(self, entities, connection_threshold=10, entity_type="wall"):
        """对特殊图层实体进行严格的按图层分组"""
        if not entities:
            return []
        
        # 按图层分组
        layer_groups = defaultdict(list)
        for entity in entities:
            layer_groups[entity['layer']].append(entity)
        
        all_groups = []
        
        # 对每个图层单独进行连接性分组
        for layer, layer_entities in layer_groups.items():
            if entity_type == "wall":
                # 墙体：精确的端点连接检测
                layer_connection_groups = self._group_entities_by_precise_connection(
                    layer_entities, connection_threshold
                )
            elif entity_type in ["door_window", "railing"]:
                # 门窗和栏杆：端点连接 + 200单位容差
                layer_connection_groups = self._group_entities_by_flexible_connection(
                    layer_entities, connection_threshold, proximity_threshold=80
                )
            else:
                # 其他：标准连接检测
                layer_connection_groups = self._group_entities_by_connection(
                    layer_entities, connection_threshold
                )
            
            # 确保返回的是字典格式的组
            for i, group in enumerate(layer_connection_groups):
                if isinstance(group, list):
                    # 如果是列表，转换为字典格式
                    dict_group = {
                        'entities': group,
                        'label': f'{entity_type}_{layer}_{i}',
                        'group_type': entity_type,
                        'layer': layer,
                        'status': 'pending',
                        'confidence': 0.8
                    }
                    all_groups.append(dict_group)
                elif isinstance(group, dict):
                    # 如果已经是字典，确保有必要的字段
                    if 'entities' not in group:
                        group['entities'] = []
                    if 'label' not in group:
                        group['label'] = f'{entity_type}_{layer}_{i}'
                    if 'group_type' not in group:
                        group['group_type'] = entity_type
                    if 'layer' not in group:
                        group['layer'] = layer
                    if 'status' not in group:
                        group['status'] = 'pending'
                    if 'confidence' not in group:
                        group['confidence'] = 0.8
                    all_groups.append(group)
        
        return all_groups

    '''
            content = content[:method_start] + new_method + content[method_end:]
        
        # 2. 修复_group_other_entities_by_layer方法的返回值
        method_start = content.find('def _group_other_entities_by_layer(self, entities, distance_threshold=20):')
        if method_start != -1:
            print("  修复_group_other_entities_by_layer方法...")
            
            # 找到方法的return语句
            return_pos = content.find('return all_groups', method_start)
            if return_pos != -1:
                # 在return之前添加数据结构转换
                insert_pos = content.rfind('\n', method_start, return_pos)
                if insert_pos != -1:
                    conversion_code = '''
        # 确保返回的是字典格式的组
        formatted_groups = []
        for i, group in enumerate(all_groups):
            if isinstance(group, list):
                # 如果是列表，转换为字典格式
                dict_group = {
                    'entities': group,
                    'label': f'other_group_{i}',
                    'group_type': 'other',
                    'status': 'pending',
                    'confidence': 0.5
                }
                formatted_groups.append(dict_group)
            elif isinstance(group, dict):
                # 如果已经是字典，确保有必要的字段
                if 'entities' not in group:
                    group['entities'] = []
                if 'label' not in group:
                    group['label'] = f'other_group_{i}'
                if 'group_type' not in group:
                    group['group_type'] = 'other'
                if 'status' not in group:
                    group['status'] = 'pending'
                if 'confidence' not in group:
                    group['confidence'] = 0.5
                formatted_groups.append(group)
        
        all_groups = formatted_groups
'''
                    content = content[:insert_pos] + conversion_code + content[insert_pos:]
        
        # 保存修改后的文件
        with open('cad_data_processor.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("  ✅ cad_data_processor.py数据结构修复完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复数据结构失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def add_debug_logging():
    """添加调试日志以便跟踪数据结构"""
    print("🔧 添加调试日志...")
    
    try:
        # 读取main_enhanced.py文件
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 在_process_other_entities方法中添加更详细的调试信息
        method_start = content.find('# 使用CAD数据处理器进行分组')
        if method_start != -1:
            # 在这行之后添加调试代码
            line_end = content.find('\n', method_start)
            if line_end != -1:
                debug_code = '''
            
            # 调试：检查输入数据
            print(f"    调试：输入实体类型: {[type(e) for e in unprocessed_entities[:3]]}")
            print(f"    调试：输入实体示例: {unprocessed_entities[0] if unprocessed_entities else 'None'}")'''
                
                content = content[:line_end] + debug_code + content[line_end:]
        
        # 在分组结果处理后添加调试信息
        result_pos = content.find('print(f"  其他实体分组结果: {len(other_groups)}个组")')
        if result_pos != -1:
            line_end = content.find('\n', result_pos)
            if line_end != -1:
                debug_code = '''
            
            # 调试：检查分组结果数据结构
            for i, group in enumerate(other_groups[:3]):  # 只检查前3个组
                print(f"    调试：组 {i} 类型: {type(group)}")
                if isinstance(group, dict):
                    print(f"    调试：组 {i} 键: {list(group.keys())}")
                elif isinstance(group, list):
                    print(f"    调试：组 {i} 长度: {len(group)}")
                else:
                    print(f"    调试：组 {i} 值: {group}")'''
                
                content = content[:line_end] + debug_code + content[line_end:]
        
        # 保存修改后的文件
        with open('main_enhanced.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("  ✅ 调试日志添加完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 添加调试日志失败: {e}")
        return False

def test_fixes():
    """测试修复效果"""
    print("🧪 测试修复效果...")
    
    try:
        # 重新导入模块
        import importlib
        
        # 清除模块缓存
        modules_to_reload = ['main_enhanced', 'cad_data_processor']
        for module_name in modules_to_reload:
            if module_name in sys.modules:
                importlib.reload(sys.modules[module_name])
        
        # 测试CAD数据处理器
        from cad_data_processor import CADDataProcessor
        cad_processor = CADDataProcessor()
        
        # 创建测试数据
        test_entities = [
            {'type': 'LINE', 'layer': '0', 'start': (0, 0), 'end': (10, 0)},
            {'type': 'LINE', 'layer': '0', 'start': (10, 0), 'end': (20, 0)},
            {'type': 'CIRCLE', 'layer': '0', 'center': (5, 5), 'radius': 2}
        ]
        
        # 测试分组
        groups = cad_processor.group_entities(test_entities, distance_threshold=20, debug=False)
        
        print(f"  测试分组结果: {len(groups)}个组")
        
        # 检查数据结构
        all_dict = True
        for i, group in enumerate(groups):
            if not isinstance(group, dict):
                print(f"    ❌ 组 {i} 不是字典类型: {type(group)}")
                all_dict = False
            else:
                required_keys = ['entities', 'label', 'group_type']
                missing_keys = [key for key in required_keys if key not in group]
                if missing_keys:
                    print(f"    ⚠️ 组 {i} 缺少键: {missing_keys}")
                else:
                    print(f"    ✅ 组 {i} 数据结构正确")
        
        if all_dict:
            print("  ✅ 所有组都是字典格式")
        else:
            print("  ❌ 部分组不是字典格式")
        
        return all_dict
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主修复函数"""
    print("🚀 开始修复分组数据结构问题")
    print("="*60)
    
    fixes = [
        ("修复分组数据结构", fix_group_data_structure),
        ("添加调试日志", add_debug_logging),
        ("测试修复效果", test_fixes)
    ]
    
    success_count = 0
    
    for fix_name, fix_func in fixes:
        print(f"\n{fix_name}...")
        try:
            if fix_func():
                success_count += 1
                print(f"✅ {fix_name}成功")
            else:
                print(f"❌ {fix_name}失败")
        except Exception as e:
            print(f"❌ {fix_name}异常: {e}")
    
    print(f"\n" + "="*60)
    print("📋 修复总结:")
    print("="*60)
    print(f"成功修复: {success_count}/{len(fixes)}")
    
    if success_count == len(fixes):
        print("🎉 数据结构修复完成！")
        print("\n💡 修复内容:")
        print("  1. 确保_group_special_entities_by_layer返回字典格式")
        print("  2. 修复_group_other_entities_by_layer的返回值")
        print("  3. 添加数据结构转换和验证")
        print("  4. 添加详细的调试日志")
        
        print("\n🔄 建议重新运行诊断脚本验证修复效果")
    else:
        print("⚠️ 部分修复失败，请检查错误信息")
    
    return success_count == len(fixes)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
