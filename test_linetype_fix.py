#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试线型警告修复
验证Continuous线型的大小写处理是否正确
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from cad_data_processor import CADDataProcessor

def test_linetype_warnings():
    """测试线型警告修复"""
    
    print("🧪 测试线型警告修复")
    print("=" * 50)
    
    # 创建处理器
    processor = CADDataProcessor()
    
    # 测试文件路径
    test_file = "C:/A-BCXM/CAD分类标注工具C01/test-dxf-wall/wall00.dxf"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print(f"📁 测试文件: {test_file}")
    print()
    
    try:
        # 加载文件（这会触发线型映射建立）
        print("🔄 开始加载DXF文件...")
        entities = processor.load_dxf_file(test_file)
        
        print(f"\n📊 加载结果:")
        print(f"   实体数量: {len(entities)}")
        print(f"   DXF版本: {processor.dxf_version}")
        print(f"   全局线型比例: {processor.global_ltscale}")
        
        print(f"\n📋 线型定义: {len(processor.linetype_dict)} 个")
        for name in sorted(processor.linetype_dict.keys()):
            entity = processor.linetype_dict[name]
            status = "默认" if entity is None else "定义"
            print(f"   - {name} ({status})")
        
        print(f"\n📋 图层线型映射: {len(processor.layer_linetype_mapping)} 个")
        continuous_layers = []
        missing_layers = []
        
        for layer_name, layer_info in sorted(processor.layer_linetype_mapping.items()):
            linetype_name = layer_info['linetype_name']
            linetype_entity = layer_info['linetype_entity']
            is_dashed = layer_info['is_dashed']
            
            if linetype_name.upper() in ['CONTINUOUS', 'BYLAYER', 'BYBLOCK']:
                continuous_layers.append(layer_name)
                status = "✅ 连续线型"
            elif linetype_entity is None:
                missing_layers.append(layer_name)
                status = "⚠️ 缺失定义"
            else:
                status = "✅ 有定义"
            
            line_style = "虚线" if is_dashed else "连续"
            print(f"   {layer_name}: {linetype_name} ({line_style}) - {status}")
        
        print(f"\n📈 统计结果:")
        print(f"   连续线型图层: {len(continuous_layers)} 个")
        print(f"   缺失定义图层: {len(missing_layers)} 个")
        print(f"   总图层数: {len(processor.layer_linetype_mapping)} 个")
        
        if missing_layers:
            print(f"\n⚠️ 仍有缺失定义的图层:")
            for layer in missing_layers:
                layer_info = processor.layer_linetype_mapping[layer]
                print(f"   - {layer}: {layer_info['linetype_name']}")
        else:
            print(f"\n✅ 所有图层的线型都已正确处理！")
        
        # 测试线型解析
        print(f"\n🔧 测试线型解析:")
        test_cases = [
            ('CONTINUOUS', '0'),
            ('Continuous', 'WALL'),
            ('continuous', 'PUB_HATCH'),
            ('BYLAYER', 'WINDOW_TEXT'),
            ('BYBLOCK', '3T_BAR')
        ]
        
        for linetype, layer in test_cases:
            try:
                # 模拟实体对象
                class MockEntity:
                    def dxftype(self):
                        return 'LINE'
                
                mock_entity = MockEntity()
                resolved = processor._resolve_effective_linetype(linetype, layer, mock_entity)
                print(f"   {linetype} + {layer} → {resolved}")
            except Exception as e:
                print(f"   {linetype} + {layer} → 错误: {e}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_case_sensitivity():
    """测试大小写敏感性"""
    
    print("\n🧪 测试大小写敏感性")
    print("=" * 50)
    
    processor = CADDataProcessor()
    
    # 模拟线型字典
    processor.linetype_dict = {
        'CONTINUOUS': None,
        'Continuous': None,
        'continuous': None,
        'BYLAYER': None,
        'BYBLOCK': None
    }
    
    # 模拟图层映射
    processor.layer_linetype_mapping = {
        'TEST_LAYER': {
            'linetype_name': 'Continuous',
            'linetype_entity': None,
            'is_dashed': False
        }
    }
    
    # 测试用例
    test_cases = [
        'CONTINUOUS',
        'Continuous', 
        'continuous',
        'BYLAYER',
        'Bylayer',
        'bylayer',
        'BYBLOCK',
        'Byblock',
        'byblock'
    ]
    
    print("测试线型名称解析:")
    for linetype in test_cases:
        try:
            class MockEntity:
                def dxftype(self):
                    return 'LINE'
            
            mock_entity = MockEntity()
            resolved = processor._resolve_effective_linetype(linetype, 'TEST_LAYER', mock_entity)
            print(f"   {linetype:12} → {resolved}")
        except Exception as e:
            print(f"   {linetype:12} → 错误: {e}")

if __name__ == "__main__":
    test_linetype_warnings()
    test_case_sensitivity()
