#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终处理器修复方案
确保处理器在应用启动时就存在，避免后续重置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_processor_initialization():
    """修复处理器初始化问题"""
    print("🔧 修复处理器初始化问题")
    print("="*60)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复1：在初始化结束时确保处理器存在
        print("1. 在初始化结束时确保处理器存在...")
        
        old_init_end = '''        # 🎨 初始化配色系统（确保配色方案可用）
        self._init_color_system()
        print("🌈 配色系统已设置")'''
        
        new_init_end = '''        # 🎨 初始化配色系统（确保配色方案可用）
        self._init_color_system()
        print("🌈 配色系统已设置")
        
        # 🔧 最终修复：确保处理器在初始化时就存在
        self._ensure_processor_initialized()'''
        
        if old_init_end in content:
            content = content.replace(old_init_end, new_init_end)
            print("  ✅ 已添加处理器初始化确保")
        
        # 修复2：添加处理器初始化确保方法
        print("2. 添加处理器初始化确保方法...")
        
        ensure_method = '''
    def _ensure_processor_initialized(self):
        """确保处理器在初始化时就存在"""
        try:
            if not hasattr(self, 'processor') or not self.processor:
                print("🔧 初始化时创建处理器")
                from main_enhanced import EnhancedCADProcessor
                self.processor = EnhancedCADProcessor(self.visualizer, self.canvas)
                
                # 设置回调
                if hasattr(self, 'on_status_update') and hasattr(self, 'on_progress_update'):
                    self.processor.set_callbacks(self.on_status_update, self.on_progress_update)
                
                # 初始化类别映射
                if not hasattr(self.processor, 'category_mapping') or not self.processor.category_mapping:
                    self.processor.category_mapping = {
                        'wall': '墙体',
                        'door_window': '门窗', 
                        'other': '其他'
                    }
                
                print("✅ 处理器初始化完成")
            else:
                print("✅ 处理器已存在")
                
        except Exception as e:
            print(f"❌ 处理器初始化失败: {e}")

'''
        
        # 在 _ensure_processor_exists 方法前插入
        insertion_point = "    def _ensure_processor_exists(self, context=\"未知\"):"
        if insertion_point in content:
            content = content.replace(insertion_point, ensure_method + insertion_point)
            print("  ✅ 已添加处理器初始化确保方法")
        
        # 修复3：改进 _load_from_cache 中的处理器检查
        print("3. 改进 _load_from_cache 中的处理器检查...")
        
        old_processor_check = '''            # 🔧 修复：智能处理器检查和恢复
            if not self.processor:
                # 🔍 详细追踪处理器重置（安全检查）
                if hasattr(self, '_track_processor_reset'):
                    self._track_processor_reset("_load_from_cache")
                else:
                    print("🔍 [追踪未初始化] 处理器重置 - _load_from_cache")
                
                # 🔧 修复：尝试从之前保存的状态恢复
                if hasattr(self, 'previous_processor') and self.previous_processor:
                    print("  🔄 从保存的状态恢复处理器")
                    self.processor = self.previous_processor
                    self.previous_processor = None
                else:
                    print("  ⚠️ 处理器不存在，创建新的处理器")'''
        
        new_processor_check = '''            # 🔧 修复：智能处理器检查和恢复
            if not self.processor:
                # 🔍 详细追踪处理器重置（安全检查）
                if hasattr(self, '_track_processor_reset'):
                    self._track_processor_reset("_load_from_cache")
                else:
                    print("🔍 [追踪未初始化] 处理器重置 - _load_from_cache")
                
                # 🔧 修复：优先使用统一的处理器确保方法
                self._ensure_processor_exists("_load_from_cache")'''
        
        if old_processor_check in content:
            content = content.replace(old_processor_check, new_processor_check)
            print("  ✅ 已改进处理器检查逻辑")
        
        # 修复4：简化处理器创建逻辑，避免重复代码
        print("4. 简化处理器创建逻辑...")
        
        # 查找并删除重复的处理器创建代码
        duplicate_creation = '''                from main_enhanced import EnhancedCADProcessor
                self.processor = EnhancedCADProcessor(self.visualizer, self.canvas)
                # 设置回调
                if hasattr(self, 'on_status_update') and hasattr(self, 'on_progress_update'):
                    self.processor.set_callbacks(self.on_status_update, self.on_progress_update)
                
                # 🔧 修复：确保新处理器有正确的初始化
                if not hasattr(self.processor, 'category_mapping') or not self.processor.category_mapping:
                    self.processor.category_mapping = {
                        'wall': '墙体',
                        'door_window': '门窗', 
                        'other': '其他'
                    }
                    print("  ✅ 初始化处理器类别映射")'''
        
        if duplicate_creation in content:
            content = content.replace(duplicate_creation, '')
            print("  ✅ 已删除重复的处理器创建代码")
        
        # 保存修改后的文件
        with open('main_enhanced_with_v2_fill.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 处理器初始化修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_final_test():
    """创建最终测试"""
    print(f"\n🧪 创建最终测试")
    print("="*60)
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终处理器修复测试
验证处理器在整个生命周期中的稳定性
"""

import sys
import os
import tkinter as tk
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_final_processor_fix():
    """测试最终处理器修复"""
    print("🧪 最终处理器修复测试")
    print("="*50)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        print("1. 导入并创建应用...")
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        app = EnhancedCADAppV2(root)
        print("  ✅ 应用创建成功")
        
        print("2. 检查初始处理器状态...")
        if app.processor:
            print("  ✅ 初始处理器存在")
            initial_processor_id = id(app.processor)
        else:
            print("  ❌ 初始处理器不存在")
            initial_processor_id = None
        
        print("3. 检查处理器属性...")
        if app.processor:
            has_mapping = hasattr(app.processor, 'category_mapping') and app.processor.category_mapping
            print(f"  类别映射: {'✅ 存在' if has_mapping else '❌ 不存在'}")
            if has_mapping:
                print(f"    映射内容: {app.processor.category_mapping}")
        
        print("4. 模拟多次文件操作...")
        test_files = ["file1.dxf", "file2.dxf", "file3.dxf"]
        processor_ids = []
        
        for i, test_file in enumerate(test_files):
            print(f"  测试文件 {i+1}: {test_file}")
            
            # 创建测试数据
            app.file_data[test_file] = {
                'entities': [{'type': 'LINE', 'layer': 'A-WALL'}],
                'all_groups': [[{'type': 'LINE', 'layer': 'A-WALL'}]],
                'auto_labeled_entities': [],
                'labeled_entities': []
            }
            
            # 模拟文件加载
            try:
                app._load_file_data(test_file)
                if app.processor:
                    processor_ids.append(id(app.processor))
                    print(f"    ✅ 处理器存在 (ID: {id(app.processor)})")
                else:
                    processor_ids.append(None)
                    print(f"    ❌ 处理器不存在")
            except Exception as e:
                print(f"    ❌ 文件加载失败: {e}")
                processor_ids.append(None)
        
        print("5. 分析处理器稳定性...")
        if initial_processor_id:
            stable_count = sum(1 for pid in processor_ids if pid == initial_processor_id)
            total_count = len(processor_ids)
            stability_rate = stable_count / total_count * 100 if total_count > 0 else 0
            
            print(f"  处理器稳定性: {stability_rate:.1f}% ({stable_count}/{total_count})")
            
            if stability_rate == 100:
                print("  ✅ 处理器完全稳定，未发生重置")
            elif stability_rate >= 50:
                print("  ⚠️ 处理器基本稳定，偶有重置")
            else:
                print("  ❌ 处理器不稳定，频繁重置")
        
        print("6. 检查重置统计...")
        if hasattr(app, '_processor_reset_count'):
            reset_count = app._processor_reset_count
            print(f"  总重置次数: {reset_count}")
            
            if reset_count == 0:
                print("  🎉 完美！没有发生任何重置")
            elif reset_count <= 1:
                print("  ✅ 很好！重置次数很少")
            elif reset_count <= 3:
                print("  ⚠️ 一般，重置次数较少")
            else:
                print("  ❌ 重置次数过多，需要进一步优化")
        
        # 清理
        root.destroy()
        
        print("\\n🎉 最终处理器修复测试完成")
        
        # 返回测试结果
        return {
            'initial_processor_exists': app.processor is not None,
            'reset_count': getattr(app, '_processor_reset_count', 0),
            'stability_rate': stability_rate if 'stability_rate' in locals() else 0
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = test_final_processor_fix()
    if result:
        print(f"\\n📊 测试结果摘要:")
        print(f"  初始处理器存在: {'✅' if result['initial_processor_exists'] else '❌'}")
        print(f"  重置次数: {result['reset_count']}")
        print(f"  稳定性: {result['stability_rate']:.1f}%")
'''
    
    with open('test_final_processor_fix.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 最终测试已创建: test_final_processor_fix.py")

def main():
    """主修复函数"""
    print("🚀 开始最终处理器修复")
    
    try:
        # 1. 修复处理器初始化
        fix_success = fix_processor_initialization()
        
        # 2. 创建最终测试
        create_final_test()
        
        print(f"\n" + "="*60)
        print(f"📊 最终修复结果:")
        print(f"  处理器初始化修复: {'✅ 成功' if fix_success else '❌ 失败'}")
        print(f"  最终测试: ✅ 已创建")
        
        if fix_success:
            print(f"\n🎯 最终修复内容:")
            print(f"   1. 确保处理器在应用初始化时就存在")
            print(f"   2. 添加统一的处理器确保方法")
            print(f"   3. 简化处理器检查逻辑")
            print(f"   4. 删除重复的处理器创建代码")
            
            print(f"\n🧪 最终测试步骤:")
            print(f"   1. 运行最终测试: python test_final_processor_fix.py")
            print(f"   2. 检查初始处理器是否存在")
            print(f"   3. 验证处理器稳定性")
            print(f"   4. 确认重置次数减少")
            
            print(f"\n🎉 预期最终效果:")
            print(f"   - 应用启动时处理器就存在")
            print(f"   - 处理器稳定性达到100%")
            print(f"   - 重置次数为0或接近0")
            print(f"   - 图像预览正常显示")
            print(f"   - 组列表正确显示类型信息")
            
            print(f"\n💡 如果测试通过，运行主程序:")
            print(f"   python main_enhanced_with_v2_fill.py")
        else:
            print(f"\n❌ 最终修复失败，请检查错误信息")
        
    except Exception as e:
        print(f"❌ 最终修复过程失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
