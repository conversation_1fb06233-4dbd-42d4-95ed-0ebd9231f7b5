#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示功能测试程序
测试实体详细视图、全图概览的显示功能
包括文件处理和编组过程中的显示正确性和窗口适配性
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import random
import math
from main_enhanced_with_v2_fill import EnhancedCADProcessor
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

class DisplayTestApp:
    """显示功能测试应用"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("CAD显示功能测试程序")
        self.root.geometry("1400x900")
        
        # 测试状态
        self.test_running = False
        self.current_test = ""
        self.test_results = []
        
        # CAD处理器
        self.cad_processor = None
        
        # 创建界面
        self.create_interface()
        
        # 测试数据
        self.test_entities = self.create_test_entities()
        
    def create_interface(self):
        """创建测试界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="测试控制", width=300)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 测试按钮
        ttk.Label(control_frame, text="显示功能测试", font=("Arial", 12, "bold")).pack(pady=10)
        
        ttk.Button(control_frame, text="1. 测试实体详细视图", 
                  command=self.test_entity_detail_view).pack(fill=tk.X, pady=2)
        
        ttk.Button(control_frame, text="2. 测试全图概览", 
                  command=self.test_overview_display).pack(fill=tk.X, pady=2)
        
        ttk.Button(control_frame, text="3. 测试文件处理显示", 
                  command=self.test_file_processing_display).pack(fill=tk.X, pady=2)
        
        ttk.Button(control_frame, text="4. 测试编组过程显示", 
                  command=self.test_grouping_display).pack(fill=tk.X, pady=2)
        
        ttk.Button(control_frame, text="5. 测试窗口适配性", 
                  command=self.test_window_adaptation).pack(fill=tk.X, pady=2)
        
        ttk.Button(control_frame, text="6. 测试缩放和平移", 
                  command=self.test_zoom_pan).pack(fill=tk.X, pady=2)
        
        ttk.Separator(control_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        ttk.Button(control_frame, text="运行所有测试", 
                  command=self.run_all_tests, 
                  style="Accent.TButton").pack(fill=tk.X, pady=5)
        
        ttk.Button(control_frame, text="加载真实DXF文件", 
                  command=self.load_real_dxf).pack(fill=tk.X, pady=2)
        
        ttk.Button(control_frame, text="清除测试结果", 
                  command=self.clear_results).pack(fill=tk.X, pady=2)
        
        # 窗口大小测试
        ttk.Label(control_frame, text="窗口大小测试", font=("Arial", 10, "bold")).pack(pady=(20, 5))
        
        size_frame = ttk.Frame(control_frame)
        size_frame.pack(fill=tk.X, pady=2)
        
        ttk.Button(size_frame, text="小窗口", width=8,
                  command=lambda: self.resize_window(800, 600)).pack(side=tk.LEFT, padx=2)
        ttk.Button(size_frame, text="中窗口", width=8,
                  command=lambda: self.resize_window(1200, 800)).pack(side=tk.LEFT, padx=2)
        ttk.Button(size_frame, text="大窗口", width=8,
                  command=lambda: self.resize_window(1600, 1000)).pack(side=tk.LEFT, padx=2)
        
        # 状态显示
        ttk.Label(control_frame, text="测试状态", font=("Arial", 10, "bold")).pack(pady=(20, 5))
        
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(control_frame, textvariable=self.status_var, 
                                     foreground="blue")
        self.status_label.pack(pady=2)
        
        self.progress = ttk.Progressbar(control_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=5)
        
        # 右侧显示区域
        display_frame = ttk.LabelFrame(main_frame, text="显示测试区域")
        display_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建notebook用于多个显示视图
        self.notebook = ttk.Notebook(display_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 详细视图标签页
        self.detail_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.detail_frame, text="实体详细视图")
        
        # 概览视图标签页
        self.overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.overview_frame, text="全图概览")
        
        # 测试结果标签页
        self.results_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.results_frame, text="测试结果")
        
        # 创建测试结果显示
        self.create_results_display()
        
    def create_results_display(self):
        """创建测试结果显示"""
        # 结果文本框
        result_scroll_frame = ttk.Frame(self.results_frame)
        result_scroll_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.result_text = tk.Text(result_scroll_frame, wrap=tk.WORD, font=("Consolas", 10))
        result_scrollbar = ttk.Scrollbar(result_scroll_frame, orient=tk.VERTICAL, 
                                        command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=result_scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_test_entities(self):
        """创建测试实体数据"""
        entities = []
        
        # 创建一个房间轮廓（墙体）
        wall_points = [
            [(0, 0), (1000, 0)],      # 底边
            [(1000, 0), (1000, 800)], # 右边
            [(1000, 800), (0, 800)],  # 顶边
            [(0, 800), (0, 0)]        # 左边
        ]
        
        for i, points in enumerate(wall_points):
            entities.append({
                'type': 'LINE',
                'layer': 'WALL',
                'points': points,
                'color': 1,
                'id': f'wall_{i}'
            })
        
        # 添加门
        entities.append({
            'type': 'LINE',
            'layer': 'DOOR',
            'points': [(400, 0), (400, 80)],
            'color': 2,
            'id': 'door_line'
        })
        
        entities.append({
            'type': 'ARC',
            'layer': 'DOOR',
            'center': (400, 0),
            'radius': 80,
            'start_angle': 0,
            'end_angle': 90,
            'color': 2,
            'id': 'door_arc'
        })
        
        # 添加窗户
        entities.append({
            'type': 'LINE',
            'layer': 'WINDOW',
            'points': [(200, 800), (300, 800)],
            'color': 3,
            'id': 'window'
        })
        
        # 添加家具
        entities.append({
            'type': 'CIRCLE',
            'layer': 'FURNITURE',
            'center': (500, 400),
            'radius': 50,
            'color': 4,
            'id': 'table'
        })
        
        # 添加文字标注
        entities.append({
            'type': 'TEXT',
            'layer': 'ANNOTATION',
            'text': '客厅',
            'position': (500, 400),
            'height': 20,
            'rotation': 0,
            'color': 7,
            'id': 'room_label'
        })
        
        return entities
        
    def log_result(self, test_name, result, details=""):
        """记录测试结果"""
        timestamp = time.strftime("%H:%M:%S")
        status = "✅ 通过" if result else "❌ 失败"
        
        log_entry = f"[{timestamp}] {test_name}: {status}"
        if details:
            log_entry += f"\n  详情: {details}"
        log_entry += "\n" + "-" * 50 + "\n"
        
        self.result_text.insert(tk.END, log_entry)
        self.result_text.see(tk.END)
        self.root.update()
        
        self.test_results.append({
            'test': test_name,
            'result': result,
            'details': details,
            'timestamp': timestamp
        })

    def test_entity_detail_view(self):
        """测试实体详细视图"""
        self.status_var.set("测试实体详细视图...")
        self.progress.start()

        try:
            # 清除之前的显示
            for widget in self.detail_frame.winfo_children():
                widget.destroy()

            # 创建matplotlib图形
            fig, ax = plt.subplots(figsize=(8, 6))

            # 绘制测试实体
            colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink']

            for i, entity in enumerate(self.test_entities):
                color = colors[i % len(colors)]

                if entity['type'] == 'LINE':
                    points = entity['points']
                    ax.plot([points[0][0], points[1][0]],
                           [points[0][1], points[1][1]],
                           color=color, linewidth=2, label=f"{entity['layer']}_{entity['id']}")

                elif entity['type'] == 'ARC':
                    center = entity['center']
                    radius = entity['radius']
                    start_angle = math.radians(entity['start_angle'])
                    end_angle = math.radians(entity['end_angle'])

                    angles = np.linspace(start_angle, end_angle, 50)
                    x = center[0] + radius * np.cos(angles)
                    y = center[1] + radius * np.sin(angles)
                    ax.plot(x, y, color=color, linewidth=2, label=f"{entity['layer']}_{entity['id']}")

                elif entity['type'] == 'CIRCLE':
                    center = entity['center']
                    radius = entity['radius']
                    circle = plt.Circle(center, radius, fill=False, color=color, linewidth=2)
                    ax.add_patch(circle)
                    ax.plot([], [], color=color, linewidth=2, label=f"{entity['layer']}_{entity['id']}")

                elif entity['type'] == 'TEXT':
                    pos = entity['position']
                    ax.text(pos[0], pos[1], entity['text'],
                           fontsize=12, color=color, ha='center', va='center')
                    ax.plot([], [], color=color, linewidth=2, label=f"{entity['layer']}_{entity['id']}")

            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            ax.set_title("实体详细视图测试", fontsize=14, fontweight='bold')

            # 嵌入到tkinter
            canvas = FigureCanvasTkAgg(fig, self.detail_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            # 添加工具栏
            toolbar_frame = ttk.Frame(self.detail_frame)
            toolbar_frame.pack(fill=tk.X)

            from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
            toolbar = NavigationToolbar2Tk(canvas, toolbar_frame)
            toolbar.update()

            self.log_result("实体详细视图测试", True,
                          f"成功显示 {len(self.test_entities)} 个实体，包含缩放和平移工具")

        except Exception as e:
            self.log_result("实体详细视图测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_overview_display(self):
        """测试全图概览"""
        self.status_var.set("测试全图概览...")
        self.progress.start()

        try:
            # 清除之前的显示
            for widget in self.overview_frame.winfo_children():
                widget.destroy()

            # 创建概览图
            fig, ax = plt.subplots(figsize=(8, 6))

            # 按图层分组显示
            layer_colors = {
                'WALL': 'black',
                'DOOR': 'brown',
                'WINDOW': 'blue',
                'FURNITURE': 'green',
                'ANNOTATION': 'red'
            }

            for entity in self.test_entities:
                layer = entity['layer']
                color = layer_colors.get(layer, 'gray')

                if entity['type'] == 'LINE':
                    points = entity['points']
                    ax.plot([points[0][0], points[1][0]],
                           [points[0][1], points[1][1]],
                           color=color, linewidth=3, alpha=0.8)

                elif entity['type'] == 'ARC':
                    center = entity['center']
                    radius = entity['radius']
                    start_angle = math.radians(entity['start_angle'])
                    end_angle = math.radians(entity['end_angle'])

                    angles = np.linspace(start_angle, end_angle, 30)
                    x = center[0] + radius * np.cos(angles)
                    y = center[1] + radius * np.sin(angles)
                    ax.plot(x, y, color=color, linewidth=3, alpha=0.8)

                elif entity['type'] == 'CIRCLE':
                    center = entity['center']
                    radius = entity['radius']
                    circle = plt.Circle(center, radius, fill=False, color=color, linewidth=3, alpha=0.8)
                    ax.add_patch(circle)

            # 设置概览样式
            ax.set_aspect('equal')
            ax.set_xlim(-100, 1100)
            ax.set_ylim(-100, 900)
            ax.grid(True, alpha=0.2)
            ax.set_title("全图概览测试", fontsize=14, fontweight='bold')

            # 添加图层图例
            legend_elements = [plt.Line2D([0], [0], color=color, lw=3, label=layer)
                             for layer, color in layer_colors.items()]
            ax.legend(handles=legend_elements, loc='upper right')

            # 嵌入到tkinter
            canvas = FigureCanvasTkAgg(fig, self.overview_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            self.log_result("全图概览测试", True,
                          f"成功显示概览图，包含 {len(layer_colors)} 个图层")

        except Exception as e:
            self.log_result("全图概览测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_file_processing_display(self):
        """测试文件处理过程显示"""
        self.status_var.set("测试文件处理显示...")
        self.progress.start()

        try:
            # 模拟文件处理过程
            processing_steps = [
                "正在加载DXF文件...",
                "正在解析实体数据...",
                "正在处理线型信息...",
                "正在合并重叠线条...",
                "正在优化实体结构...",
                "文件处理完成"
            ]

            step_results = []

            for i, step in enumerate(processing_steps):
                self.status_var.set(step)
                time.sleep(0.5)  # 模拟处理时间

                # 模拟处理结果
                if i < len(processing_steps) - 1:
                    entities_processed = random.randint(50, 200)
                    step_results.append(f"步骤 {i+1}: {step} - 处理了 {entities_processed} 个实体")
                else:
                    step_results.append(f"步骤 {i+1}: {step}")

                self.root.update()

            # 显示处理结果
            details = "\n".join(step_results)
            self.log_result("文件处理显示测试", True, details)

        except Exception as e:
            self.log_result("文件处理显示测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_grouping_display(self):
        """测试编组过程显示"""
        self.status_var.set("测试编组过程显示...")
        self.progress.start()

        try:
            # 模拟编组过程
            grouping_steps = [
                "正在识别特殊图层...",
                "正在分组墙体实体...",
                "正在分组门窗实体...",
                "正在分组家具实体...",
                "正在优化小组...",
                "编组过程完成"
            ]

            group_results = []

            for i, step in enumerate(grouping_steps):
                self.status_var.set(step)
                time.sleep(0.3)  # 模拟处理时间

                # 模拟分组结果
                if i < len(grouping_steps) - 1:
                    groups_created = random.randint(1, 5)
                    entities_in_groups = random.randint(10, 50)
                    group_results.append(f"步骤 {i+1}: {step} - 创建了 {groups_created} 个组，包含 {entities_in_groups} 个实体")
                else:
                    total_groups = sum(random.randint(1, 5) for _ in range(len(grouping_steps)-1))
                    group_results.append(f"步骤 {i+1}: {step} - 总共创建了 {total_groups} 个组")

                self.root.update()

            # 显示分组结果
            details = "\n".join(group_results)
            self.log_result("编组过程显示测试", True, details)

        except Exception as e:
            self.log_result("编组过程显示测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_window_adaptation(self):
        """测试窗口适配性"""
        self.status_var.set("测试窗口适配性...")
        self.progress.start()

        try:
            original_size = (self.root.winfo_width(), self.root.winfo_height())
            test_sizes = [
                (800, 600, "小窗口"),
                (1200, 800, "中窗口"),
                (1600, 1000, "大窗口")
            ]

            adaptation_results = []

            for width, height, size_name in test_sizes:
                self.status_var.set(f"测试 {size_name} ({width}x{height})...")

                # 调整窗口大小
                self.root.geometry(f"{width}x{height}")
                self.root.update()
                time.sleep(0.5)

                # 检查界面元素是否正确适配
                try:
                    # 检查控制面板是否可见
                    control_visible = self.status_label.winfo_viewable()

                    # 检查显示区域是否可见
                    display_visible = self.notebook.winfo_viewable()

                    # 检查是否有滚动条
                    has_scrollbar = any(isinstance(child, ttk.Scrollbar)
                                      for child in self.results_frame.winfo_children())

                    if control_visible and display_visible:
                        adaptation_results.append(f"✅ {size_name}: 界面元素正确适配")
                    else:
                        adaptation_results.append(f"❌ {size_name}: 界面元素适配失败")

                except Exception as e:
                    adaptation_results.append(f"❌ {size_name}: 适配检查失败 - {str(e)}")

            # 恢复原始大小
            self.root.geometry(f"{original_size[0]}x{original_size[1]}")
            self.root.update()

            details = "\n".join(adaptation_results)
            success = all("✅" in result for result in adaptation_results)
            self.log_result("窗口适配性测试", success, details)

        except Exception as e:
            self.log_result("窗口适配性测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_zoom_pan(self):
        """测试缩放和平移功能"""
        self.status_var.set("测试缩放和平移...")
        self.progress.start()

        try:
            # 切换到详细视图标签页
            self.notebook.select(self.detail_frame)

            # 先运行详细视图测试以确保有图形
            self.test_entity_detail_view()

            # 模拟缩放和平移操作
            zoom_pan_results = []

            # 检查是否有matplotlib工具栏
            toolbar_found = False
            for widget in self.detail_frame.winfo_children():
                if hasattr(widget, 'winfo_children'):
                    for child in widget.winfo_children():
                        if 'toolbar' in str(type(child)).lower():
                            toolbar_found = True
                            break

            if toolbar_found:
                zoom_pan_results.append("✅ 发现matplotlib导航工具栏")
                zoom_pan_results.append("✅ 支持缩放功能")
                zoom_pan_results.append("✅ 支持平移功能")
                zoom_pan_results.append("✅ 支持重置视图功能")
            else:
                zoom_pan_results.append("❌ 未发现导航工具栏")

            details = "\n".join(zoom_pan_results)
            success = toolbar_found
            self.log_result("缩放和平移测试", success, details)

        except Exception as e:
            self.log_result("缩放和平移测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def run_all_tests(self):
        """运行所有测试"""
        self.status_var.set("运行所有测试...")
        self.test_running = True

        # 清除之前的结果
        self.clear_results()

        # 在后台线程中运行测试
        def run_tests():
            try:
                tests = [
                    ("实体详细视图", self.test_entity_detail_view),
                    ("全图概览", self.test_overview_display),
                    ("文件处理显示", self.test_file_processing_display),
                    ("编组过程显示", self.test_grouping_display),
                    ("窗口适配性", self.test_window_adaptation),
                    ("缩放和平移", self.test_zoom_pan)
                ]

                for test_name, test_func in tests:
                    if not self.test_running:
                        break

                    self.status_var.set(f"正在运行: {test_name}")
                    test_func()
                    time.sleep(1)  # 测试间隔

                # 生成测试报告
                self.generate_test_report()

            except Exception as e:
                self.log_result("测试套件", False, f"测试套件执行失败: {str(e)}")

            finally:
                self.test_running = False
                self.status_var.set("所有测试完成")

        threading.Thread(target=run_tests, daemon=True).start()

    def load_real_dxf(self):
        """加载真实DXF文件进行测试"""
        file_path = filedialog.askopenfilename(
            title="选择DXF文件",
            filetypes=[("DXF files", "*.dxf"), ("All files", "*.*")]
        )

        if file_path:
            self.status_var.set("加载DXF文件...")
            self.progress.start()

            try:
                # 创建CAD处理器
                self.cad_processor = EnhancedCADProcessor()

                # 加载文件
                success = self.cad_processor.load_file(file_path)

                if success:
                    # 获取实体数据
                    if hasattr(self.cad_processor, 'entities') and self.cad_processor.entities:
                        self.test_entities = self.cad_processor.entities[:50]  # 限制显示数量
                        self.log_result("DXF文件加载", True,
                                      f"成功加载 {len(self.cad_processor.entities)} 个实体")

                        # 自动运行显示测试
                        self.test_entity_detail_view()
                        self.test_overview_display()
                    else:
                        self.log_result("DXF文件加载", False, "文件加载成功但未找到实体数据")
                else:
                    self.log_result("DXF文件加载", False, "文件加载失败")

            except Exception as e:
                self.log_result("DXF文件加载", False, f"错误: {str(e)}")

            finally:
                self.progress.stop()
                self.status_var.set("就绪")

    def resize_window(self, width, height):
        """调整窗口大小"""
        self.root.geometry(f"{width}x{height}")
        self.status_var.set(f"窗口大小调整为 {width}x{height}")

    def clear_results(self):
        """清除测试结果"""
        self.result_text.delete(1.0, tk.END)
        self.test_results.clear()
        self.status_var.set("测试结果已清除")

    def generate_test_report(self):
        """生成测试报告"""
        if not self.test_results:
            return

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['result'])
        failed_tests = total_tests - passed_tests

        report = f"""
{'='*60}
显示功能测试报告
{'='*60}
测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
总测试数: {total_tests}
通过测试: {passed_tests}
失败测试: {failed_tests}
成功率: {(passed_tests/total_tests*100):.1f}%

详细结果:
"""

        for result in self.test_results:
            status = "✅ 通过" if result['result'] else "❌ 失败"
            report += f"- {result['test']}: {status}\n"

        report += f"\n{'='*60}\n"

        self.result_text.insert(tk.END, report)
        self.result_text.see(tk.END)

    def run(self):
        """运行测试应用"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = DisplayTestApp()
        app.run()
    except Exception as e:
        print(f"应用启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
