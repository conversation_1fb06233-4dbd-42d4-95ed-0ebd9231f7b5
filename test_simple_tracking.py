#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试简化处理器追踪
"""

import sys
import os
import tkinter as tk
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_tracking():
    """测试简化追踪功能"""
    print("🧪 测试简化处理器追踪")
    print("="*50)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        print("1. 导入主程序...")
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        print("  ✅ 导入成功")
        
        print("2. 创建应用实例...")
        app = EnhancedCADAppV2(root)
        print("  ✅ 创建成功")
        
        print("3. 检查追踪功能...")
        if hasattr(app, '_processor_reset_count'):
            print(f"  ✅ 重置计数器: {app._processor_reset_count}")
        else:
            print("  ❌ 重置计数器未初始化")
        
        print("4. 模拟处理器重置...")
        # 临时清空处理器来触发重置
        original_processor = app.processor
        app.processor = None
        
        # 尝试触发重置检测
        try:
            # 这会触发处理器检查
            app._load_from_cache("test.dxf")
        except:
            pass  # 忽略错误，我们只关心追踪
        
        # 恢复处理器
        app.processor = original_processor
        
        print("5. 检查追踪结果...")
        if hasattr(app, '_processor_reset_count') and app._processor_reset_count > 0:
            print(f"  ✅ 成功追踪到 {app._processor_reset_count} 次重置")
        else:
            print("  ⚠️ 未检测到重置事件")
        
        # 清理
        root.destroy()
        
        print("\n🎉 简化追踪测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_simple_tracking()
