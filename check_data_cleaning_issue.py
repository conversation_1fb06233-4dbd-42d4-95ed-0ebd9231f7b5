#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查数据清理是否过度过滤
分析_clean_group_data方法是否意外过滤了有效实体
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_clean_group_data_method():
    """分析_clean_group_data方法"""
    print("🔍 分析_clean_group_data方法...")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor(None, None)
        
        # 检查方法是否存在
        if not hasattr(processor, '_clean_group_data'):
            print("❌ _clean_group_data方法不存在")
            return False
        
        # 查看方法的源代码逻辑
        import inspect
        source = inspect.getsource(processor._clean_group_data)
        print("📋 _clean_group_data方法源代码:")
        print(source)
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_various_entity_formats():
    """测试各种实体格式"""
    print("\n🧪 测试各种实体格式...")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor(None, None)
        
        # 测试各种可能的实体格式
        test_cases = [
            {
                'name': '标准LINE实体',
                'data': {
                    'type': 'LINE',
                    'layer': 'A-WALL',
                    'start': [0, 0],
                    'end': [100, 0],
                    'points': [(0, 0), (100, 0)]
                }
            },
            {
                'name': '缺少points的LINE实体',
                'data': {
                    'type': 'LINE',
                    'layer': 'A-WALL',
                    'start': [0, 0],
                    'end': [100, 0]
                }
            },
            {
                'name': '只有type和layer的实体',
                'data': {
                    'type': 'LINE',
                    'layer': 'A-WALL'
                }
            },
            {
                'name': '缺少type的实体',
                'data': {
                    'layer': 'A-WALL',
                    'points': [(0, 0), (100, 0)]
                }
            },
            {
                'name': '缺少layer的实体',
                'data': {
                    'type': 'LINE',
                    'points': [(0, 0), (100, 0)]
                }
            },
            {
                'name': 'LWPOLYLINE实体',
                'data': {
                    'type': 'LWPOLYLINE',
                    'layer': 'A-WALL',
                    'points': [(0, 0), (100, 0), (100, 100)]
                }
            },
            {
                'name': 'CIRCLE实体',
                'data': {
                    'type': 'CIRCLE',
                    'layer': 'A-WALL',
                    'center': [50, 50],
                    'radius': 25
                }
            },
            {
                'name': '空字典',
                'data': {}
            },
            {
                'name': '字符串（应该被过滤）',
                'data': 'invalid_string'
            },
            {
                'name': 'None值（应该被过滤）',
                'data': None
            }
        ]
        
        print(f"📋 测试用例: {len(test_cases)} 个")
        
        results = []
        for test_case in test_cases:
            name = test_case['name']
            data = test_case['data']
            
            print(f"\n  测试: {name}")
            print(f"    输入: {data}")
            
            # 测试单个实体
            if isinstance(data, dict):
                cleaned = processor._clean_group_data([data])
            else:
                cleaned = processor._clean_group_data([data])
            
            print(f"    输出: {len(cleaned)} 个实体")
            if cleaned:
                print(f"    结果: {cleaned[0]}")
            
            # 记录结果
            expected_valid = isinstance(data, dict) and data.get('type') and data.get('layer')
            actual_valid = len(cleaned) > 0
            
            results.append({
                'name': name,
                'expected': expected_valid,
                'actual': actual_valid,
                'correct': expected_valid == actual_valid
            })
        
        # 统计结果
        print(f"\n📊 测试结果统计:")
        correct_count = sum(1 for r in results if r['correct'])
        total_count = len(results)
        
        print(f"  正确: {correct_count}/{total_count}")
        
        # 显示错误的情况
        errors = [r for r in results if not r['correct']]
        if errors:
            print(f"  错误情况: {len(errors)} 个")
            for error in errors:
                print(f"    - {error['name']}: 期望{error['expected']}, 实际{error['actual']}")
        
        return len(errors) == 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_dxf_like_data():
    """测试类似真实DXF的数据"""
    print("\n📄 测试类似真实DXF的数据...")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor(None, None)
        
        # 模拟真实DXF解析后的数据结构
        real_dxf_data = [
            # 墙体线条
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start': [311909.5, 21363.0],
                'end': [312129.5, 21363.0],
                'points': [(311909.5, 21363.0), (312129.5, 21363.0)],
                'color': 7,
                'handle': '1A2B',
                'linetype': 'Continuous'
            },
            # 门实体
            {
                'type': 'LINE',
                'layer': 'A-DOOR',
                'start': [312000.0, 21363.0],
                'end': [312000.0, 21400.0],
                'points': [(312000.0, 21363.0), (312000.0, 21400.0)],
                'color': 3,
                'handle': '1A2C',
                'linetype': 'Continuous'
            },
            # 窗实体
            {
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start': [311950.0, 21400.0],
                'end': [312050.0, 21400.0],
                'points': [(311950.0, 21400.0), (312050.0, 21400.0)],
                'color': 4,
                'handle': '1A2D',
                'linetype': 'Continuous'
            },
            # 可能的问题数据：包含额外字段
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start': [312129.5, 21363.0],
                'end': [312129.5, 22243.0],
                'points': [(312129.5, 21363.0), (312129.5, 22243.0)],
                'color': 7,
                'handle': '1A2E',
                'linetype': 'Continuous',
                'thickness': 0.0,
                'elevation': 0.0,
                'extrusion': [0.0, 0.0, 1.0],
                'extra_field': 'some_value'
            }
        ]
        
        print(f"📋 真实DXF数据: {len(real_dxf_data)} 个实体")
        
        # 测试清理
        cleaned_data = processor._clean_group_data(real_dxf_data)
        
        print(f"📊 清理结果:")
        print(f"  原始: {len(real_dxf_data)} 个实体")
        print(f"  清理后: {len(cleaned_data)} 个实体")
        
        if len(cleaned_data) != len(real_dxf_data):
            print("⚠️ 数据清理可能过度过滤了有效实体！")
            
            # 找出被过滤的实体
            print("被过滤的实体:")
            for i, original in enumerate(real_dxf_data):
                found = False
                for cleaned in cleaned_data:
                    if (cleaned.get('handle') == original.get('handle') or
                        (cleaned.get('type') == original.get('type') and 
                         cleaned.get('layer') == original.get('layer') and
                         cleaned.get('start') == original.get('start'))):
                        found = True
                        break
                
                if not found:
                    print(f"  实体{i+1}: {original.get('type')} - {original.get('layer')}")
                    print(f"    原因: type={bool(original.get('type'))}, layer={bool(original.get('layer'))}")
            
            return False
        else:
            print("✅ 数据清理正确，未过滤有效实体")
            return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_before_after_cleaning():
    """对比清理前后的数据"""
    print("\n🔄 对比清理前后的数据...")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor(None, None)
        
        # 创建包含问题的混合数据
        mixed_data = [
            # 有效实体
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
            {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(50, 0), (50, 50)]},
            
            # 原始问题数据
            'index',
            'total', 
            'entity_count',
            
            # 其他无效数据
            None,
            123,
            {},
            {'layer': 'A-WALL'},  # 缺少type
            {'type': 'LINE'},     # 缺少layer
            
            # 更多有效实体
            {'type': 'CIRCLE', 'layer': 'A-WALL', 'center': [50, 50], 'radius': 25},
            {'type': 'LWPOLYLINE', 'layer': 'A-WINDOW', 'points': [(0, 0), (100, 0), (100, 100)]}
        ]
        
        print(f"📋 混合数据: {len(mixed_data)} 个项目")
        
        # 手动统计有效实体
        manual_valid_count = 0
        for item in mixed_data:
            if isinstance(item, dict) and item.get('type') and item.get('layer'):
                manual_valid_count += 1
                print(f"  有效: {item.get('type')} - {item.get('layer')}")
            else:
                print(f"  无效: {type(item)} - {str(item)[:50]}")
        
        print(f"\n📊 手动统计有效实体: {manual_valid_count} 个")
        
        # 使用清理方法
        cleaned_data = processor._clean_group_data(mixed_data)
        print(f"📊 清理方法结果: {len(cleaned_data)} 个")
        
        # 对比结果
        if len(cleaned_data) == manual_valid_count:
            print("✅ 清理方法结果正确")
            return True
        else:
            print("❌ 清理方法结果不正确")
            print(f"  期望: {manual_valid_count}, 实际: {len(cleaned_data)}")
            
            # 显示清理后的数据
            print("清理后的数据:")
            for i, item in enumerate(cleaned_data):
                print(f"  {i+1}: {item.get('type')} - {item.get('layer')}")
            
            return False
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CAD分类标注工具 - 数据清理问题检查")
    print("=" * 60)
    
    tests = [
        ("分析_clean_group_data方法", analyze_clean_group_data_method),
        ("测试各种实体格式", test_various_entity_formats),
        ("测试类似真实DXF的数据", test_real_dxf_like_data),
        ("对比清理前后的数据", compare_before_after_cleaning)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("检查结果总结:")
    print("=" * 60)
    
    for test_name, success in results:
        status = "✅ 正常" if success else "❌ 异常"
        print(f"  {test_name}: {status}")
    
    failed_tests = [name for name, success in results if not success]
    
    if failed_tests:
        print(f"\n⚠️ 发现问题: {len(failed_tests)} 个")
        print("数据清理可能存在问题，需要修复")
        
        print("\n🔧 可能的解决方案:")
        print("  1. 放宽数据清理的条件")
        print("  2. 改进实体有效性判断逻辑")
        print("  3. 添加更多的实体类型支持")
        print("  4. 保留更多的实体字段")
    else:
        print("\n✅ 数据清理功能正常")
        print("问题可能在于其他方面，如界面集成或实际数据格式")
    
    print("=" * 60)
