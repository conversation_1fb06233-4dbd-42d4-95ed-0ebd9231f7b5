#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
端到端显示测试 - 从数据创建到界面显示
"""

import sys
import os
import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.patches as patches
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_entities():
    """创建测试实体数据"""
    print("🔧 创建测试实体数据")
    print("="*60)
    
    # 创建墙体实体（矩形房间）
    wall_entities = [
        # 底边墙
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (1000, 0)], 'color': 7},
        # 右边墙
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(1000, 0), (1000, 800)], 'color': 7},
        # 顶边墙
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(1000, 800), (0, 800)], 'color': 7},
        # 左边墙
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 800), (0, 0)], 'color': 7},
    ]
    
    # 创建门窗实体
    door_window_entities = [
        # 门（在底边墙）
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(200, -10), (200, 10)], 'color': 3},
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(300, -10), (300, 10)], 'color': 3},
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(200, 0), (300, 0)], 'color': 3},
        # 窗户（在右边墙）
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(990, 300), (1010, 300)], 'color': 3},
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(990, 500), (1010, 500)], 'color': 3},
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(1000, 300), (1000, 500)], 'color': 3},
    ]
    
    # 创建其他实体
    other_entities = [
        # 文字标注
        {'type': 'TEXT', 'layer': 'A-TEXT', 'position': (500, 400), 'text': '客厅', 'color': 1},
        {'type': 'TEXT', 'layer': 'A-TEXT', 'position': (250, 50), 'text': '门', 'color': 1},
        # 尺寸线
        {'type': 'DIMENSION', 'layer': 'A-DIM', 'def_points': [(0, -50), (1000, -50)], 'color': 2},
    ]
    
    all_entities = wall_entities + door_window_entities + other_entities
    
    print(f"✅ 创建完成:")
    print(f"  墙体实体: {len(wall_entities)} 个")
    print(f"  门窗实体: {len(door_window_entities)} 个")
    print(f"  其他实体: {len(other_entities)} 个")
    print(f"  总实体数: {len(all_entities)} 个")
    
    return all_entities, wall_entities, door_window_entities, other_entities

def test_processor_creation_and_setup():
    """测试处理器创建和设置"""
    print(f"\n🔧 测试处理器创建和设置")
    print("="*60)
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        # 创建处理器
        processor = EnhancedCADProcessor()
        print(f"✅ 处理器创建成功")
        
        # 验证初始化
        print(f"📋 处理器状态检查:")
        print(f"  category_mapping: {processor.category_mapping}")
        print(f"  all_groups: {len(processor.all_groups)}")
        print(f"  groups_info: {len(processor.groups_info)}")
        print(f"  auto_labeled_entities: {len(processor.auto_labeled_entities)}")
        
        return processor
        
    except Exception as e:
        print(f"❌ 处理器创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_manual_grouping_and_labeling(processor, wall_entities, door_window_entities, other_entities):
    """测试手动分组和标注"""
    print(f"\n🔧 测试手动分组和标注")
    print("="*60)
    
    try:
        # 手动创建组
        groups = []
        
        # 墙体组
        wall_group = []
        for entity in wall_entities:
            entity['label'] = '墙体'
            entity['auto_labeled'] = True
            wall_group.append(entity)
        groups.append(wall_group)
        
        # 门窗组
        door_window_group = []
        for entity in door_window_entities:
            entity['label'] = '门窗'
            entity['auto_labeled'] = True
            door_window_group.append(entity)
        groups.append(door_window_group)
        
        # 其他组
        other_group = []
        for entity in other_entities:
            # 不设置标签，保持为待标注状态
            other_group.append(entity)
        groups.append(other_group)
        
        # 设置处理器数据
        processor.all_groups = groups
        processor.auto_labeled_entities = wall_entities + door_window_entities
        processor.current_file_entities = wall_entities + door_window_entities + other_entities
        
        print(f"✅ 手动分组完成:")
        print(f"  总组数: {len(groups)}")
        print(f"  墙体组实体数: {len(wall_group)}")
        print(f"  门窗组实体数: {len(door_window_group)}")
        print(f"  其他组实体数: {len(other_group)}")
        print(f"  自动标注实体数: {len(processor.auto_labeled_entities)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 手动分组失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_groups_info_update(processor):
    """测试组信息更新"""
    print(f"\n🔧 测试组信息更新")
    print("="*60)
    
    try:
        # 调用组信息更新
        processor._update_groups_info()
        
        print(f"✅ 组信息更新完成: {len(processor.groups_info)} 个组")
        
        # 显示详细信息
        for i, info in enumerate(processor.groups_info):
            print(f"  组 {i+1}:")
            print(f"    状态: {info.get('status')}")
            print(f"    标签: {info.get('label')}")
            print(f"    类型: {info.get('group_type')}")
            print(f"    图层: {info.get('layer')}")
            print(f"    实体数: {info.get('entity_count')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 组信息更新失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_group_list_display(processor):
    """测试UI组列表显示"""
    print(f"\n🔧 测试UI组列表显示")
    print("="*60)
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("组列表显示测试")
        root.geometry("600x400")
        
        # 创建组列表
        frame = ttk.Frame(root)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建Treeview
        columns = ('状态', '类型', '实体数')
        tree = ttk.Treeview(frame, columns=columns, show='tree headings')
        
        # 设置列标题
        tree.heading('#0', text='组ID')
        tree.heading('状态', text='状态')
        tree.heading('类型', text='类型')
        tree.heading('实体数', text='实体数')
        
        # 设置列宽
        tree.column('#0', width=100)
        tree.column('状态', width=100)
        tree.column('类型', width=100)
        tree.column('实体数', width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 插入数据
        print(f"📋 插入组列表数据:")
        
        for i, info in enumerate(processor.groups_info):
            status = info.get('status', 'unknown')
            group_type = info.get('group_type', 'unknown')
            entity_count = info.get('entity_count', 0)
            
            # 状态文本映射
            if status == 'auto_labeled':
                status_text = '自动标注'
                tag = 'auto_labeled'
            elif status == 'pending':
                status_text = '待处理'
                tag = 'pending'
            else:
                status_text = status
                tag = 'unknown'
            
            # 类型文本映射
            if hasattr(processor, 'category_mapping') and processor.category_mapping:
                type_text = processor.category_mapping.get(group_type, group_type)
            else:
                type_text = group_type
            
            # 组ID
            group_id = f"组{i+1}"
            
            # 插入到树中
            item_id = tree.insert('', 'end', text=group_id,
                                values=(status_text, type_text, entity_count),
                                tags=(tag,))
            
            print(f"  {group_id}: {status_text} - {type_text} ({entity_count} 个实体)")
        
        # 配置标签颜色
        tree.tag_configure('auto_labeled', background='lightgreen')
        tree.tag_configure('pending', background='lightyellow')
        
        # 添加关闭按钮
        close_btn = ttk.Button(frame, text="关闭", command=root.destroy)
        close_btn.pack(pady=5)
        
        print(f"✅ 组列表显示窗口已创建")
        print(f"💡 请查看弹出的窗口验证组列表显示效果")
        
        # 显示窗口（非阻塞）
        root.update()
        
        return root, tree
        
    except Exception as e:
        print(f"❌ UI组列表显示失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_entity_group_preview(processor):
    """测试实体组预览"""
    print(f"\n🔧 测试实体组预览")
    print("="*60)
    
    try:
        from cad_visualizer import CADVisualizer
        
        # 创建可视化器
        visualizer = CADVisualizer()
        print(f"✅ 可视化器创建成功")
        
        # 创建预览窗口
        root = tk.Tk()
        root.title("实体组预览测试")
        root.geometry("800x600")
        
        # 创建matplotlib图形
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        fig.suptitle('实体组预览测试')
        
        # 测试前3个组
        test_count = min(3, len(processor.all_groups))
        
        for i in range(test_count):
            group = processor.all_groups[i]
            ax = axes[i]
            
            print(f"\n  测试组 {i+1}:")
            print(f"    原始组实体数: {len(group)}")
            
            # 清理组数据
            if hasattr(processor, '_clean_group_data'):
                cleaned_group = processor._clean_group_data(group)
                print(f"    清理后实体数: {len(cleaned_group)}")
            else:
                cleaned_group = group
                print(f"    未清理实体数: {len(cleaned_group)}")
            
            # 绘制组
            if cleaned_group:
                # 设置可视化器的当前轴
                visualizer.ax_detail = ax
                
                try:
                    # 绘制实体组
                    for entity in cleaned_group:
                        if isinstance(entity, dict) and entity.get('type'):
                            visualizer._draw_entity(entity, '#0000ff', 2, 1.0, ax)
                    
                    # 设置轴属性
                    ax.set_title(f'组{i+1} ({len(cleaned_group)}个实体)')
                    ax.set_aspect('equal')
                    ax.grid(True, alpha=0.3)
                    
                    print(f"    ✅ 组预览绘制成功")
                    
                except Exception as e:
                    print(f"    ❌ 组预览绘制失败: {e}")
                    ax.text(0.5, 0.5, f'绘制失败\n{str(e)}', 
                           transform=ax.transAxes, ha='center', va='center')
            else:
                ax.text(0.5, 0.5, '无有效实体', 
                       transform=ax.transAxes, ha='center', va='center')
                print(f"    ⚠️ 组为空")
        
        # 嵌入到tkinter窗口
        canvas = FigureCanvasTkAgg(fig, root)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加关闭按钮
        close_btn = ttk.Button(root, text="关闭", command=root.destroy)
        close_btn.pack(pady=5)
        
        print(f"\n✅ 实体组预览窗口已创建")
        print(f"💡 请查看弹出的窗口验证实体组预览效果")
        
        # 显示窗口（非阻塞）
        root.update()
        
        return root, fig
        
    except Exception as e:
        print(f"❌ 实体组预览失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_overview_display(processor):
    """测试全图概览显示"""
    print(f"\n🔧 测试全图概览显示")
    print("="*60)
    
    try:
        from cad_visualizer import CADVisualizer
        
        # 创建可视化器
        visualizer = CADVisualizer()
        print(f"✅ 可视化器创建成功")
        
        # 创建概览窗口
        root = tk.Tk()
        root.title("全图概览测试")
        root.geometry("800x600")
        
        # 创建matplotlib图形
        fig, ax = plt.subplots(1, 1, figsize=(10, 8))
        fig.suptitle('全图概览测试')
        
        # 设置可视化器的轴
        visualizer.ax_overview = ax
        
        # 准备数据
        current_group = processor.all_groups[0] if processor.all_groups else []
        if hasattr(processor, '_clean_group_data'):
            current_group = processor._clean_group_data(current_group)
        
        labeled_entities = processor.auto_labeled_entities if hasattr(processor, 'auto_labeled_entities') else []
        
        print(f"📋 概览数据:")
        print(f"  总实体数: {len(processor.current_file_entities)}")
        print(f"  当前组实体数: {len(current_group)}")
        print(f"  已标注实体数: {len(labeled_entities)}")
        
        try:
            # 调用全图概览
            visualizer.visualize_overview(
                processor.current_file_entities,
                current_group,
                labeled_entities,
                processor=processor
            )
            
            print(f"✅ 全图概览绘制成功")
            
        except Exception as e:
            print(f"❌ 全图概览绘制失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 手动绘制基本概览
            ax.clear()
            ax.set_title('全图概览（手动绘制）')
            
            # 绘制所有实体
            for entity in processor.current_file_entities:
                if isinstance(entity, dict) and entity.get('type') == 'LINE':
                    points = entity.get('points', [])
                    if len(points) >= 2:
                        x = [p[0] for p in points]
                        y = [p[1] for p in points]
                        
                        # 根据标签设置颜色
                        label = entity.get('label', '')
                        if label == '墙体':
                            color = 'blue'
                        elif label == '门窗':
                            color = 'red'
                        else:
                            color = 'gray'
                        
                        ax.plot(x, y, color=color, linewidth=2)
            
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            ax.legend(['墙体', '门窗', '其他'])
        
        # 嵌入到tkinter窗口
        canvas = FigureCanvasTkAgg(fig, root)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加关闭按钮
        close_btn = ttk.Button(root, text="关闭", command=root.destroy)
        close_btn.pack(pady=5)
        
        print(f"✅ 全图概览窗口已创建")
        print(f"💡 请查看弹出的窗口验证全图概览效果")
        
        # 显示窗口（非阻塞）
        root.update()
        
        return root, fig
        
    except Exception as e:
        print(f"❌ 全图概览显示失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def main():
    """主测试函数"""
    print("🚀 开始端到端显示测试")
    print("从数据创建到界面显示的完整流程")
    
    windows = []  # 保存窗口引用
    
    try:
        # 1. 创建测试数据
        all_entities, wall_entities, door_window_entities, other_entities = create_test_entities()
        
        # 2. 创建和设置处理器
        processor = test_processor_creation_and_setup()
        if not processor:
            print("❌ 处理器创建失败，无法继续测试")
            return
        
        # 3. 手动分组和标注
        success = test_manual_grouping_and_labeling(processor, wall_entities, door_window_entities, other_entities)
        if not success:
            print("❌ 手动分组失败，无法继续测试")
            return
        
        # 4. 更新组信息
        success = test_groups_info_update(processor)
        if not success:
            print("❌ 组信息更新失败，无法继续测试")
            return
        
        # 5. 测试UI组列表显示
        root1, tree = test_ui_group_list_display(processor)
        if root1:
            windows.append(root1)
        
        # 6. 测试实体组预览
        root2, fig2 = test_entity_group_preview(processor)
        if root2:
            windows.append(root2)
        
        # 7. 测试全图概览显示
        root3, fig3 = test_overview_display(processor)
        if root3:
            windows.append(root3)
        
        print(f"\n" + "="*60)
        print(f"📊 端到端测试完成")
        print(f"✅ 已创建 {len(windows)} 个测试窗口")
        print(f"💡 请查看弹出的窗口验证显示效果:")
        print(f"   1. 组列表显示窗口")
        print(f"   2. 实体组预览窗口")
        print(f"   3. 全图概览显示窗口")
        print(f"")
        print(f"🔍 验证要点:")
        print(f"   - 组列表是否显示正确的中文类型（墙体、门窗）")
        print(f"   - 实体组预览是否能看到具体的线条图形")
        print(f"   - 全图概览是否显示完整的房间布局")
        print(f"")
        print(f"⚠️ 关闭所有窗口后程序将结束")
        
        # 等待所有窗口关闭
        if windows:
            print(f"\n⏳ 等待窗口关闭...")
            for window in windows:
                try:
                    window.wait_window()
                except:
                    pass
        
        print(f"✅ 端到端测试结束")
        
    except Exception as e:
        print(f"❌ 端到端测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
