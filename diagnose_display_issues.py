#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
诊断显示问题的根本原因
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def diagnose_processor_state():
    """诊断处理器状态"""
    print("🔍 诊断处理器状态")
    print("="*60)
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        # 创建处理器
        processor = EnhancedCADProcessor()
        print(f"✅ 处理器创建成功")
        
        # 检查处理器属性
        print(f"\n📋 处理器属性检查:")
        attrs_to_check = [
            'all_groups', 'groups_info', 'auto_labeled_entities', 
            'labeled_entities', 'current_file_entities', 'category_mapping'
        ]
        
        for attr in attrs_to_check:
            if hasattr(processor, attr):
                value = getattr(processor, attr)
                print(f"  ✅ {attr}: {type(value)} (长度: {len(value) if hasattr(value, '__len__') else 'N/A'})")
            else:
                print(f"  ❌ {attr}: 不存在")
        
        # 检查category_mapping的初始化
        print(f"\n🏷️ 类别映射检查:")
        if hasattr(processor, 'category_mapping'):
            print(f"  category_mapping: {processor.category_mapping}")
        else:
            print(f"  ❌ category_mapping 不存在")
            # 尝试初始化
            processor.category_mapping = {
                'wall': '墙体',
                'door_window': '门窗',
                'other': '其他'
            }
            print(f"  ✅ 已初始化 category_mapping: {processor.category_mapping}")
        
        return processor
        
    except Exception as e:
        print(f"❌ 处理器创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def diagnose_real_file_processing():
    """诊断真实文件处理过程"""
    print(f"\n🔍 诊断真实文件处理过程")
    print("="*60)
    
    # 查找测试文件
    test_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.dxf'):
                test_files.append(os.path.join(root, file))
                if len(test_files) >= 3:  # 只取前3个文件
                    break
        if test_files:
            break
    
    if not test_files:
        print("❌ 没有找到DXF测试文件")
        return None
    
    print(f"📁 找到测试文件: {test_files[0]}")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        # 创建处理器
        processor = EnhancedCADProcessor()
        
        # 初始化category_mapping
        processor.category_mapping = {
            'wall': '墙体',
            'door_window': '门窗',
            'other': '其他'
        }
        
        # 处理文件
        print(f"\n🔧 开始处理文件...")
        success = processor.process_file(test_files[0])
        
        if success:
            print(f"✅ 文件处理成功")
            
            # 检查处理结果
            print(f"\n📊 处理结果检查:")
            print(f"  总实体数: {len(processor.entities) if hasattr(processor, 'entities') else 0}")
            print(f"  总组数: {len(processor.all_groups) if hasattr(processor, 'all_groups') else 0}")
            print(f"  组信息数: {len(processor.groups_info) if hasattr(processor, 'groups_info') else 0}")
            print(f"  自动标注实体数: {len(processor.auto_labeled_entities) if hasattr(processor, 'auto_labeled_entities') else 0}")
            
            # 检查组信息详情
            if hasattr(processor, 'groups_info') and processor.groups_info:
                print(f"\n📋 组信息详情:")
                for i, info in enumerate(processor.groups_info[:5]):  # 只显示前5个
                    print(f"  组 {i+1}:")
                    print(f"    状态: {info.get('status')}")
                    print(f"    标签: {info.get('label')}")
                    print(f"    类型: {info.get('group_type')}")
                    print(f"    图层: {info.get('layer')}")
                    print(f"    实体数: {info.get('entity_count')}")
            
            # 统计各类组
            if hasattr(processor, 'groups_info') and processor.groups_info:
                wall_groups = [g for g in processor.groups_info if g.get('group_type') == 'wall']
                door_window_groups = [g for g in processor.groups_info if g.get('group_type') == 'door_window']
                other_groups = [g for g in processor.groups_info if g.get('group_type') == 'other']
                auto_labeled_groups = [g for g in processor.groups_info if g.get('status') == 'auto_labeled']
                pending_groups = [g for g in processor.groups_info if g.get('status') == 'pending']
                
                print(f"\n📈 组统计:")
                print(f"  墙体组: {len(wall_groups)} 个")
                print(f"  门窗组: {len(door_window_groups)} 个")
                print(f"  其他组: {len(other_groups)} 个")
                print(f"  自动标注组: {len(auto_labeled_groups)} 个")
                print(f"  待标注组: {len(pending_groups)} 个")
            
            return processor
            
        else:
            print(f"❌ 文件处理失败")
            return None
            
    except Exception as e:
        print(f"❌ 文件处理异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def diagnose_visualization_issues(processor):
    """诊断可视化问题"""
    print(f"\n🔍 诊断可视化问题")
    print("="*60)
    
    if not processor:
        print("❌ 处理器为空，无法诊断可视化问题")
        return
    
    try:
        from cad_visualizer import CADVisualizer
        
        # 创建可视化器
        visualizer = CADVisualizer()
        print(f"✅ 可视化器创建成功")
        
        # 检查可视化器属性
        print(f"\n📋 可视化器属性检查:")
        if hasattr(visualizer, 'type_colors'):
            print(f"  ✅ type_colors: {len(visualizer.type_colors)} 种类型")
        
        # 测试组可视化
        if hasattr(processor, 'all_groups') and processor.all_groups:
            print(f"\n🎨 测试组可视化:")
            
            for i, group in enumerate(processor.all_groups[:3]):  # 只测试前3个组
                print(f"  测试组 {i+1}:")
                print(f"    组类型: {type(group)}")
                
                # 清理组数据
                if hasattr(processor, '_clean_group_data'):
                    cleaned_group = processor._clean_group_data(group)
                    print(f"    清理后实体数: {len(cleaned_group)}")
                    
                    if cleaned_group:
                        try:
                            # 测试组预览
                            visualizer.visualize_entity_group(cleaned_group, processor.category_mapping or {})
                            print(f"    ✅ 组预览测试成功")
                        except Exception as e:
                            print(f"    ❌ 组预览测试失败: {e}")
                    else:
                        print(f"    ⚠️ 清理后组为空")
                else:
                    print(f"    ❌ _clean_group_data 方法不存在")
        
        # 测试全图概览
        print(f"\n🌍 测试全图概览:")
        if (hasattr(processor, 'current_file_entities') and processor.current_file_entities and
            hasattr(processor, 'all_groups') and processor.all_groups):
            
            try:
                # 获取当前组（第一个组）
                current_group = processor.all_groups[0] if processor.all_groups else []
                if hasattr(processor, '_clean_group_data'):
                    current_group = processor._clean_group_data(current_group)
                
                # 获取已标注实体
                labeled_entities = []
                if hasattr(processor, 'auto_labeled_entities'):
                    labeled_entities.extend(processor.auto_labeled_entities)
                if hasattr(processor, 'labeled_entities'):
                    labeled_entities.extend(processor.labeled_entities)
                
                # 测试全图概览
                visualizer.visualize_overview(
                    processor.current_file_entities,
                    current_group,
                    labeled_entities,
                    processor=processor
                )
                print(f"    ✅ 全图概览测试成功")
                
            except Exception as e:
                print(f"    ❌ 全图概览测试失败: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"    ❌ 缺少必要数据进行全图概览测试")
    
    except Exception as e:
        print(f"❌ 可视化诊断失败: {e}")
        import traceback
        traceback.print_exc()

def diagnose_ui_integration():
    """诊断UI集成问题"""
    print(f"\n🔍 诊断UI集成问题")
    print("="*60)
    
    try:
        # 检查主程序文件
        main_files = ['main_enhanced_with_v2_fill.py', 'main_enhanced.py']
        
        for main_file in main_files:
            if os.path.exists(main_file):
                print(f"📁 检查文件: {main_file}")
                
                # 检查关键方法
                with open(main_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                key_methods = [
                    'update_group_list', '_show_group', 'switch_to_file',
                    '_update_groups_info', '_clean_group_data'
                ]
                
                for method in key_methods:
                    if f'def {method}' in content:
                        print(f"  ✅ {method} 方法存在")
                    else:
                        print(f"  ❌ {method} 方法不存在")
                
                # 检查category_mapping的使用
                if 'category_mapping' in content:
                    print(f"  ✅ category_mapping 被使用")
                    
                    # 统计使用次数
                    count = content.count('category_mapping')
                    print(f"    使用次数: {count}")
                    
                    # 检查是否有不一致的路径
                    if 'processor.processor.category_mapping' in content:
                        print(f"    ⚠️ 发现不一致的路径: processor.processor.category_mapping")
                    if 'processor.category_mapping' in content:
                        print(f"    ✅ 发现正确的路径: processor.category_mapping")
                else:
                    print(f"  ❌ category_mapping 未被使用")
                
                break
        
    except Exception as e:
        print(f"❌ UI集成诊断失败: {e}")

def generate_fix_recommendations():
    """生成修复建议"""
    print(f"\n💡 修复建议")
    print("="*60)
    
    print("基于诊断结果，建议的修复步骤:")
    print()
    print("1. 🔧 处理器初始化问题:")
    print("   - 确保 category_mapping 在处理器创建时正确初始化")
    print("   - 检查处理器属性的完整性")
    print()
    print("2. 🎨 可视化问题:")
    print("   - 确保 _clean_group_data 方法被正确调用")
    print("   - 检查组数据格式的一致性")
    print("   - 验证可视化器的数据传递")
    print()
    print("3. 🖥️ UI集成问题:")
    print("   - 统一 category_mapping 的访问路径")
    print("   - 确保组信息更新后正确传递到UI")
    print("   - 检查事件处理和数据绑定")
    print()
    print("4. 📊 数据一致性:")
    print("   - 验证文件处理后的数据完整性")
    print("   - 确保组状态和类型的正确识别")
    print("   - 检查自动标注逻辑")

def main():
    """主诊断函数"""
    print("🚀 开始全面诊断显示问题")
    
    try:
        # 1. 诊断处理器状态
        processor = diagnose_processor_state()
        
        # 2. 诊断真实文件处理
        if processor:
            processor = diagnose_real_file_processing()
        
        # 3. 诊断可视化问题
        if processor:
            diagnose_visualization_issues(processor)
        
        # 4. 诊断UI集成
        diagnose_ui_integration()
        
        # 5. 生成修复建议
        generate_fix_recommendations()
        
        print(f"\n✅ 诊断完成")
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
