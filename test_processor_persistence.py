#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试处理器持久性修复效果
"""

import sys
import os
import tkinter as tk
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_processor_persistence():
    """测试处理器持久性"""
    print("🧪 测试处理器持久性修复")
    print("="*50)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        print("1. 导入并创建应用...")
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        app = EnhancedCADAppV2(root)
        print("  ✅ 应用创建成功")
        
        print("2. 检查初始处理器状态...")
        initial_processor = app.processor
        print(f"  初始处理器: {'存在' if initial_processor else '不存在'}")
        
        print("3. 模拟文件加载...")
        # 创建测试文件路径
        test_file = "test_file.dxf"
        
        # 模拟文件数据
        app.file_data[test_file] = {
            'entities': [{'type': 'LINE', 'layer': 'A-WALL'}],
            'all_groups': [[{'type': 'LINE', 'layer': 'A-WALL'}]],
            'auto_labeled_entities': [],
            'labeled_entities': []
        }
        
        # 调用文件加载
        app._load_file_data(test_file)
        
        print("4. 检查加载后处理器状态...")
        after_load_processor = app.processor
        print(f"  加载后处理器: {'存在' if after_load_processor else '不存在'}")
        
        if initial_processor and after_load_processor:
            if initial_processor is after_load_processor:
                print("  ✅ 处理器保持一致，未被重置")
            else:
                print("  ⚠️ 处理器被替换，但仍然存在")
        elif after_load_processor:
            print("  ✅ 处理器存在（可能是新创建的）")
        else:
            print("  ❌ 处理器丢失")
        
        print("5. 检查重置计数...")
        if hasattr(app, '_processor_reset_count'):
            print(f"  重置次数: {app._processor_reset_count}")
            if app._processor_reset_count == 0:
                print("  ✅ 没有发生重置")
            else:
                print(f"  ⚠️ 发生了 {app._processor_reset_count} 次重置")
        
        # 清理
        root.destroy()
        
        print("\n🎉 处理器持久性测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_processor_persistence()
