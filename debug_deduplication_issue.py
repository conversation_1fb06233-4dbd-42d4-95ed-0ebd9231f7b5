#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试去重问题
分析为什么26个实体被去重为1个实体
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_entity_hash_logic():
    """分析实体哈希逻辑"""
    print("🔍 分析实体哈希逻辑...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 模拟真实的实体数据（基于调试输出）
        test_entities = [
            # 实体1：使用start_x/start_y/end_x/end_y格式
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 342727.5,
                'start_y': 25051.0,
                'end_x': 342727.5,
                'end_y': 15031.0,
                'color': 256,
                'linetype': 'Continuous',
                'merged': True
            },
            # 实体2：不同的坐标
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 342727.5,
                'start_y': 15031.0,
                'end_x': 339439.5,
                'end_y': 15031.0,
                'color': 256,
                'linetype': 'Continuous',
                'merged': True
            },
            # 实体3：另一个不同的坐标
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 339439.5,
                'start_y': 15031.0,
                'end_x': 339439.5,
                'end_y': 25051.0,
                'color': 256,
                'linetype': 'Continuous',
                'merged': True
            },
            # 实体4：完全相同的坐标（真正的重复）
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 342727.5,
                'start_y': 25051.0,
                'end_x': 342727.5,
                'end_y': 15031.0,
                'color': 256,
                'linetype': 'Continuous',
                'merged': True
            }
        ]
        
        print(f"📋 测试实体: {len(test_entities)} 个")
        
        # 分析每个实体的哈希值
        hashes = []
        for i, entity in enumerate(test_entities):
            entity_hash = processor._get_entity_hash(entity)
            hashes.append(entity_hash)
            print(f"  实体{i+1}: {entity_hash}")
            print(f"    坐标: ({entity.get('start_x')}, {entity.get('start_y')}) -> ({entity.get('end_x')}, {entity.get('end_y')})")
        
        # 检查哈希冲突
        unique_hashes = set(hashes)
        print(f"\n📊 哈希分析:")
        print(f"  总哈希数: {len(hashes)}")
        print(f"  唯一哈希数: {len(unique_hashes)}")
        print(f"  哈希冲突: {len(hashes) - len(unique_hashes)} 个")
        
        if len(unique_hashes) < len(hashes):
            print(f"  ⚠️ 发现哈希冲突！")
            # 找出冲突的哈希
            hash_count = {}
            for h in hashes:
                hash_count[h] = hash_count.get(h, 0) + 1
            
            for h, count in hash_count.items():
                if count > 1:
                    print(f"    冲突哈希: {h} (出现{count}次)")
        
        # 测试去重
        print(f"\n🔧 测试去重功能:")
        unique_entities = processor._remove_duplicate_entities(test_entities)
        print(f"  去重结果: {len(test_entities)} -> {len(unique_entities)} 个实体")
        
        return len(unique_entities) == 3  # 期望保留3个不同的实体
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_line_hash_with_different_formats():
    """测试不同格式的LINE实体哈希"""
    print("\n🧪 测试不同格式的LINE实体哈希...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 测试不同格式的相同线段
        test_cases = [
            {
                'name': 'start_x/start_y/end_x/end_y格式',
                'entity': {
                    'type': 'LINE',
                    'layer': 'A-WALL',
                    'start_x': 0.0,
                    'start_y': 0.0,
                    'end_x': 100.0,
                    'end_y': 0.0
                }
            },
            {
                'name': 'points格式',
                'entity': {
                    'type': 'LINE',
                    'layer': 'A-WALL',
                    'points': [(0.0, 0.0), (100.0, 0.0)]
                }
            },
            {
                'name': 'start/end格式',
                'entity': {
                    'type': 'LINE',
                    'layer': 'A-WALL',
                    'start': (0.0, 0.0),
                    'end': (100.0, 0.0)
                }
            },
            {
                'name': '缺少坐标信息',
                'entity': {
                    'type': 'LINE',
                    'layer': 'A-WALL'
                }
            }
        ]
        
        print(f"📋 测试用例: {len(test_cases)} 个")
        
        hashes = []
        for test_case in test_cases:
            name = test_case['name']
            entity = test_case['entity']
            
            entity_hash = processor._get_entity_hash(entity)
            hashes.append(entity_hash)
            
            print(f"  {name}:")
            print(f"    实体: {entity}")
            print(f"    哈希: {entity_hash}")
        
        # 检查是否所有格式产生相同的哈希（对于相同的线段）
        unique_hashes = set(hashes[:-1])  # 排除最后一个缺少坐标的
        print(f"\n📊 哈希一致性检查:")
        print(f"  前3个哈希: {hashes[:-1]}")
        print(f"  唯一哈希数: {len(unique_hashes)}")
        
        if len(unique_hashes) == 1:
            print(f"  ✅ 不同格式产生相同哈希（正确）")
        else:
            print(f"  ❌ 不同格式产生不同哈希（问题）")
        
        return len(unique_hashes) == 1
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def identify_hash_problems():
    """识别哈希算法的具体问题"""
    print("\n🔍 识别哈希算法问题...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 测试问题场景
        problem_entity = {
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_x': 342727.5,
            'start_y': 25051.0,
            'end_x': 342727.5,
            'end_y': 15031.0,
            'color': 256,
            'linetype': 'Continuous',
            'merged': True
        }
        
        print(f"📋 问题实体分析:")
        print(f"  实体: {problem_entity}")
        
        # 检查哈希生成过程
        entity_type = problem_entity.get('type', '')
        layer = problem_entity.get('layer', '')
        
        print(f"  实体类型: {entity_type}")
        print(f"  图层: {layer}")
        
        # 检查坐标提取
        points = problem_entity.get('points', [])
        start = problem_entity.get('start', None)
        end = problem_entity.get('end', None)
        start_x = problem_entity.get('start_x', None)
        start_y = problem_entity.get('start_y', None)
        end_x = problem_entity.get('end_x', None)
        end_y = problem_entity.get('end_y', None)
        
        print(f"  坐标信息:")
        print(f"    points: {points}")
        print(f"    start: {start}")
        print(f"    end: {end}")
        print(f"    start_x/start_y: {start_x}/{start_y}")
        print(f"    end_x/end_y: {end_x}/{end_y}")
        
        # 模拟哈希生成逻辑
        if entity_type == 'LINE':
            if len(points) >= 2:
                print(f"  使用points路径")
                sorted_points = sorted([(float(p[0]), float(p[1])) for p in points])
                hash_value = f"LINE_{layer}_{sorted_points}"
            else:
                print(f"  使用start/end路径")
                if start_x is not None and start_y is not None and end_x is not None and end_y is not None:
                    # 这里是问题！当前代码没有处理start_x/start_y/end_x/end_y格式
                    print(f"    ⚠️ 发现问题：代码没有处理start_x/start_y/end_x/end_y格式！")
                    start = (0, 0)  # 默认值
                    end = (0, 0)    # 默认值
                else:
                    start = problem_entity.get('start', (0, 0))
                    end = problem_entity.get('end', (0, 0))
                
                sorted_points = sorted([(float(start[0]), float(start[1])), (float(end[0]), float(end[1]))])
                hash_value = f"LINE_{layer}_{sorted_points}"
        
        print(f"  生成的哈希: {hash_value}")
        
        # 实际调用哈希方法
        actual_hash = processor._get_entity_hash(problem_entity)
        print(f"  实际哈希: {actual_hash}")
        
        # 检查是否匹配
        if hash_value == actual_hash:
            print(f"  ✅ 哈希匹配")
        else:
            print(f"  ❌ 哈希不匹配")
        
        return True
        
    except Exception as e:
        print(f"❌ 识别失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CAD分类标注工具 - 去重问题调试")
    print("=" * 60)
    
    tests = [
        ("实体哈希逻辑分析", analyze_entity_hash_logic),
        ("不同格式LINE实体哈希测试", test_line_hash_with_different_formats),
        ("哈希算法问题识别", identify_hash_problems)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("调试结果总结:")
    print("=" * 60)
    
    for test_name, success in results:
        status = "✅ 正常" if success else "❌ 异常"
        print(f"  {test_name}: {status}")
    
    failed_tests = [name for name, success in results if not success]
    
    if failed_tests:
        print(f"\n⚠️ 发现问题: {len(failed_tests)} 个")
        print("需要修复哈希算法以正确处理不同的数据格式")
    else:
        print("\n✅ 哈希逻辑正常")
    
    print("=" * 60)
