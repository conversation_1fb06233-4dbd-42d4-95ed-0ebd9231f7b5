#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试图像控制界面调整（按红框示意）
调整要求：
1. 增加每行的高度，显示更加清晰
2. 将现有按钮水平排布
3. 取消前面的绿色按钮，改为下拉菜单（显示/隐藏），排在图层名称后面
4. 右边红框区域：应用图层设置按钮
"""

import tkinter as tk
from tkinter import ttk

class ImageControlAdjustedTest:
    """图像控制界面调整测试"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("图像控制界面调整测试 - 按红框示意")
        self.root.geometry("900x500")
        
        # 初始化图层状态
        self.layer_states = {
            'cad_lines': tk.BooleanVar(value=True),
            'wall_fill': tk.BooleanVar(value=True),
            'furniture_fill': tk.BooleanVar(value=True),
            'room_fill': tk.BooleanVar(value=True)
        }
        
        self.create_test_interface()
    
    def create_test_interface(self):
        """创建测试界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg='lightgray')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 标题
        title_label = tk.Label(main_frame, text="3. 图像控制",
                              font=('Arial', 12, 'bold'), bg='lightcoral')
        title_label.pack(fill='x', pady=(0, 5))
        
        # 创建主容器，使用水平布局
        main_container = tk.Frame(main_frame)
        main_container.pack(fill='both', expand=True, padx=5, pady=5)
        
        # 配置网格权重
        main_container.grid_rowconfigure(0, weight=1)
        main_container.grid_columnconfigure(0, weight=3)  # 左侧权重3（更宽）
        main_container.grid_columnconfigure(1, weight=1)  # 右侧权重1
        
        # 左边红框：图层控制区域
        self.layer_control_container = tk.Frame(main_container, relief='ridge', bd=2, bg='#F0F8FF')
        self.layer_control_container.grid(row=0, column=0, sticky='nsew', padx=(0, 2))
        
        # 创建图层控制区域
        self._create_layer_control_area(self.layer_control_container)
        
        # 右边红框：应用按钮区域
        self.apply_button_container = tk.Frame(main_container, relief='ridge', bd=2, bg='#FFF8DC')
        self.apply_button_container.grid(row=0, column=1, sticky='nsew', padx=(2, 0))
        
        # 创建应用按钮区域
        self._create_apply_button_area(self.apply_button_container)
    
    def _create_layer_control_area(self, parent):
        """创建图层控制区域（左边红框）"""
        # 图层列表容器
        layer_list_frame = tk.Frame(parent, bg='#F0F8FF')
        layer_list_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # 图层控制项目数据
        layer_items_data = [
            ('cad_lines', 'CAD线条', '#2196F3'),
            ('wall_fill', '墙体填充', '#4CAF50'),
            ('furniture_fill', '家具填充', '#FF9800'),
            ('room_fill', '房间填充', '#9C27B0')
        ]
        
        # 创建图层控制项
        for i, (layer_key, layer_name, color) in enumerate(layer_items_data):
            self._create_single_layer_item(layer_list_frame, layer_key, layer_name, color)
    
    def _create_single_layer_item(self, parent, layer_key, layer_name, color):
        """创建单个图层控制项（按红框示意调整）"""
        # 图层项容器 - 增加高度，显示更加清晰
        item_frame = tk.Frame(parent, relief='ridge', bd=1, bg='#FFFFFF')
        item_frame.pack(fill='x', pady=5, padx=3)  # 增加垂直间距
        
        # 主行：所有控件水平排布在一行 - 增加行高
        main_row = tk.Frame(item_frame, bg='#FFFFFF')
        main_row.pack(fill='x', padx=8, pady=10)  # 增加内边距，提高行高
        
        # 1. 圆形颜色指示器（不可点击，仅显示颜色）
        color_canvas = tk.Canvas(main_row, width=18, height=18, highlightthickness=0, bg='#FFFFFF')
        color_canvas.pack(side='left', padx=(0, 8))
        color_canvas.create_oval(2, 2, 16, 16, fill=color, outline='#333333', width=1)
        
        # 2. 图层名称标签 - 增大字体，显示更清晰
        name_label = tk.Label(main_row, text=layer_name, font=('Arial', 10, 'bold'),
                            bg='#FFFFFF', anchor='w', width=10)
        name_label.pack(side='left', padx=(0, 8))
        
        # 3. 下拉菜单（显示/隐藏）- 取消绿色按钮，改为下拉菜单
        layer_combo = ttk.Combobox(main_row, width=6, font=('Arial', 9),
                                 values=['显示', '隐藏'], state='readonly')
        layer_combo.set('显示' if self.layer_states[layer_key].get() else '隐藏')
        layer_combo.pack(side='left', padx=(0, 8))
        layer_combo.bind('<<ComboboxSelected>>', 
                       lambda e, k=layer_key: self._on_layer_combo_change(k, layer_combo.get()))
        
        # 4. 控制按钮组 - 水平排布
        btn_colors = ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#607D8B', '#795548']
        btn_texts = ['设置', '编辑', '复制', '删除', '上移', '下移']
        
        for i, (btn_text, btn_color) in enumerate(zip(btn_texts, btn_colors)):
            btn = tk.Button(main_row, text=btn_text, 
                          font=('Arial', 8), width=4, height=1,
                          bg=btn_color, fg='white', relief='raised', bd=1,
                          command=lambda t=btn_text, k=layer_key: self._on_layer_button_click(k, t))
            btn.pack(side='left', padx=1)
    
    def _create_apply_button_area(self, parent):
        """创建应用按钮区域（右边红框）"""
        # 创建按钮容器，居中显示
        button_container = tk.Frame(parent, bg='#FFF8DC')
        button_container.pack(expand=True, fill='both')
        
        # 居中放置按钮
        center_frame = tk.Frame(button_container, bg='#FFF8DC')
        center_frame.place(relx=0.5, rely=0.5, anchor='center')
        
        # 应用图层设置按钮
        apply_btn = tk.Button(center_frame, text="应用图层设置",
                            command=self._apply_layer_settings,
                            bg='#FF5722', fg='white',
                            font=('Arial', 10, 'bold'),
                            width=12, height=3,
                            relief='raised', bd=2)
        apply_btn.pack()
    
    def _on_layer_combo_change(self, layer_key, combo_value):
        """图层下拉菜单变化事件处理"""
        if combo_value == '显示':
            self.layer_states[layer_key].set(True)
        elif combo_value == '隐藏':
            self.layer_states[layer_key].set(False)
            
        print(f"🔄 图层 {layer_key} 状态变更为: {combo_value}")
    
    def _on_layer_button_click(self, layer_key, button_text):
        """图层按钮点击事件处理"""
        print(f"🔘 图层 {layer_key} 的 {button_text} 按钮被点击")
    
    def _apply_layer_settings(self):
        """应用图层设置"""
        print("✅ 应用图层设置按钮被点击")
        print("当前图层状态:")
        for layer_key, state_var in self.layer_states.items():
            print(f"  {layer_key}: {'显示' if state_var.get() else '隐藏'}")

def main():
    root = tk.Tk()
    app = ImageControlAdjustedTest(root)
    root.mainloop()

if __name__ == "__main__":
    main()
