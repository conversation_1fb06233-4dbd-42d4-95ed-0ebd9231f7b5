# CAD三阶段处理功能使用说明

## 🎯 功能概述

CAD分类标注工具现在支持三阶段处理模式，将原来的单一处理流程分解为三个独立的阶段，每个阶段完成后都会在界面中显示相应的处理结果。

## 🔄 三阶段处理流程

### 阶段1：开始处理（基础数据加载）
**功能**：加载CAD文件，提取基础实体数据  
**按钮**：蓝色"开始处理"按钮  
**处理内容**：
- 读取DXF文件
- 解析实体数据
- 提取图层信息
- 建立基础数据结构

**完成标志**：
- 全图预览显示原始CAD数据
- "线条处理"按钮变为可用状态
- 状态栏显示"基础数据加载完成"

### 阶段2：线条处理（线条合并优化）
**功能**：对线条进行合并和优化处理  
**按钮**：绿色"线条处理"按钮  
**处理内容**：
- 线段合并算法
- 重叠线条处理
- 几何形状优化
- 连接性分析

**完成标志**：
- 全图预览显示优化后的线条
- "识别分组"按钮变为可用状态
- 状态栏显示"线条处理完成"

### 阶段3：识别分组（实体分组识别）
**功能**：将实体按照几何关系进行智能分组  
**按钮**：橙色"识别分组"按钮  
**处理内容**：
- 实体连接性分析
- 智能分组算法
- 组状态管理
- 特征提取

**完成标志**：
- 全图预览显示分组结果（不同颜色）
- 实体组列表显示所有分组
- 可以进行类别选择等后续操作
- 状态栏显示"识别分组完成"

## 🖱️ 操作步骤

### 1. 准备工作
- 确保CAD文件（.dxf格式）存放在指定文件夹中
- 启动CAD分类标注工具

### 2. 选择文件夹
- 点击"选择文件夹"按钮
- 选择包含CAD文件的文件夹

### 3. 执行三阶段处理

#### 步骤1：基础数据加载
1. 点击蓝色"开始处理"按钮
2. 等待处理完成（状态栏会显示进度）
3. 查看全图预览中的原始CAD数据
4. 确认"线条处理"按钮已启用

#### 步骤2：线条处理
1. 点击绿色"线条处理"按钮
2. 等待线条合并处理完成
3. 查看全图预览中的优化结果
4. 确认"识别分组"按钮已启用

#### 步骤3：识别分组
1. 点击橙色"识别分组"按钮
2. 等待分组处理完成
3. 查看全图预览中的分组结果（不同颜色显示）
4. 查看右侧实体组列表中的分组信息

### 4. 后续操作
分组完成后，可以进行以下操作：
- **类别选择**：为每个组分配建筑类别
- **墙体填充**：对墙体组进行填充处理
- **房间识别**：识别房间区域
- **数据导出**：导出标注结果

## 🎨 界面说明

### 按钮布局
```
第一行：[开始处理] [线条处理] [识别分组]
第二行：[停止]     [导出日志]
```

### 按钮颜色含义
- **蓝色（开始处理）**：基础数据加载
- **绿色（线条处理）**：线条优化处理
- **橙色（识别分组）**：智能分组识别
- **红色（停止）**：中断当前处理
- **紫色（导出日志）**：导出处理日志

### 状态指示
- **按钮状态**：
  - 可用：按钮颜色正常，可以点击
  - 禁用：按钮呈灰色，不可点击
  - 处理中：相应按钮禁用，停止按钮启用

- **状态栏信息**：
  - 显示当前处理阶段
  - 显示处理进度
  - 显示错误信息（如有）

## 📊 显示区域说明

### 全图预览（左侧）
- **阶段1完成后**：显示原始CAD实体数据
- **阶段2完成后**：显示线条处理后的优化结果
- **阶段3完成后**：显示分组结果，不同组用不同颜色

### 实体组列表（右侧）
- **仅在阶段3完成后显示**
- 列出所有识别出的实体组
- 显示每组的实体数量
- 支持组的选择和类别分配

## ⚠️ 注意事项

### 处理顺序
- **必须按顺序执行**：开始处理 → 线条处理 → 识别分组
- **不能跳跃阶段**：每个阶段都有前置条件检查
- **可以随时停止**：点击红色"停止"按钮中断处理

### 数据依赖
- 线条处理需要基础数据加载完成
- 识别分组需要线条处理完成
- 类别选择等操作需要识别分组完成

### 处理时间
- 基础数据加载：通常较快（几秒到几十秒）
- 线条处理：取决于线条复杂度（几秒到几分钟）
- 识别分组：取决于实体数量（几十秒到几分钟）

## 🔧 故障排除

### 常见问题

#### 问题1：按钮无法点击
**原因**：未完成前置阶段  
**解决**：按顺序完成前面的处理阶段

#### 问题2：处理时间过长
**原因**：文件过大或结构复杂  
**解决**：等待处理完成或点击停止按钮

#### 问题3：显示结果异常
**原因**：数据处理错误  
**解决**：重新开始处理流程

#### 问题4：实体组列表为空
**原因**：未完成识别分组阶段  
**解决**：完成第三阶段处理

### 错误恢复
1. 点击"停止"按钮中断当前处理
2. 重新选择文件夹
3. 从第一阶段重新开始处理

## 💡 使用技巧

### 最佳实践
1. **文件准备**：确保DXF文件格式正确，图层命名规范
2. **分阶段检查**：每个阶段完成后检查显示结果
3. **及时保存**：重要结果及时保存或导出
4. **日志查看**：出现问题时查看导出的日志文件

### 性能优化
1. **文件大小**：建议单个文件不超过50MB
2. **图层数量**：过多图层会影响处理速度
3. **实体复杂度**：简化不必要的复杂几何形状

## 📞 技术支持

如果在使用过程中遇到问题，请：
1. 查看状态栏的错误信息
2. 导出日志文件进行分析
3. 记录问题发生的具体步骤
4. 联系技术支持团队

---

**版本**：V2.0  
**更新时间**：2025-07-28  
**适用范围**：CAD分类标注工具E02版本
