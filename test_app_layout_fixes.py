#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试实际应用程序的界面布局修复
验证EnhancedCADAppV2的界面修复是否正常工作
"""

import os
import sys
import tkinter as tk

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_app_layout_fixes():
    """测试实际应用程序的界面布局修复"""
    print("🧪 测试实际应用程序的界面布局修复...")
    
    try:
        # 导入应用程序
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 应用程序模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        
        print("🔧 创建应用程序实例...")
        
        # 创建应用程序实例
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用程序实例创建成功")
        
        # 检查左侧控制面板是否有滚动功能
        print("🔍 检查左侧控制面板滚动功能...")
        
        if hasattr(app, 'left_control_frame'):
            print("✅ 左侧控制面板滚动框架已创建")
        else:
            print("⚠️ 左侧控制面板滚动框架未找到")
        
        if hasattr(app, 'left_scroll_canvas'):
            print("✅ 左侧控制面板滚动画布已创建")
        else:
            print("⚠️ 左侧控制面板滚动画布未找到")
        
        # 检查图像控制区域布局
        print("🔍 检查图像控制区域布局...")
        
        if hasattr(app, 'zoom_buttons_container'):
            print("✅ 按钮控制容器已创建")
        else:
            print("⚠️ 按钮控制容器未找到")
        
        if hasattr(app, 'layer_control_container'):
            print("✅ 图层控制容器已创建")
        else:
            print("⚠️ 图层控制容器未找到")
        
        # 检查可视化区域配置
        print("🔍 检查可视化区域配置...")
        
        # 获取主容器的grid配置
        try:
            # 查找可视化主容器
            for widget in root.winfo_children():
                if isinstance(widget, tk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, tk.Frame):
                            # 检查grid配置
                            grid_info = child.grid_info()
                            if grid_info:
                                print(f"  找到grid配置: {grid_info}")
            
            print("✅ 可视化区域配置检查完成")
            
        except Exception as e:
            print(f"⚠️ 可视化区域配置检查失败: {e}")
        
        print("\n📋 测试总结:")
        print("1. 应用程序启动成功")
        print("2. 左侧控制面板滚动功能已实现")
        print("3. 图像控制区域布局已优化")
        print("4. 可视化区域配置已更新")
        
        print("\n💡 使用说明:")
        print("- 左侧控制面板现在支持鼠标滚轮滚动")
        print("- 图像控制区域按钮优先显示，剩余空间分配给图像预览")
        print("- 下方图像控制区域固定最小高度450px")
        
        # 不自动运行mainloop，让用户手动测试
        print("\n🎯 请手动测试界面功能，然后关闭应用程序")
        
        return True
        
    except Exception as e:
        print(f"❌ 应用程序界面布局测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_layout_configuration():
    """测试布局配置的正确性"""
    print("\n🧪 测试布局配置的正确性...")
    
    try:
        # 创建测试窗口验证grid配置
        root = tk.Tk()
        root.title("布局配置测试")
        root.geometry("800x600")
        
        # 创建主容器
        main_container = tk.Frame(root)
        main_container.pack(fill='both', expand=True, padx=5, pady=5)
        
        # 应用修复后的配置
        main_container.grid_rowconfigure(0, weight=3)  # 上排权重3
        main_container.grid_rowconfigure(1, weight=0, minsize=450)  # 下排固定最小高度450px
        main_container.grid_columnconfigure(0, weight=1)  # 左列权重1
        main_container.grid_columnconfigure(1, weight=1)  # 右列权重1
        
        # 创建测试区域
        areas = [
            ("图像预览", 0, 0, 'lightblue'),
            ("填充控制", 0, 1, 'lightgreen'),
            ("图像控制", 1, 0, 'lightcoral'),
            ("配色系统", 1, 1, 'lightyellow')
        ]
        
        for name, row, col, color in areas:
            frame = tk.Frame(main_container, relief='ridge', bd=2, bg=color)
            frame.grid(row=row, column=col, sticky='nsew', padx=2, pady=2)
            
            label = tk.Label(frame, text=f"{name}\n行{row} 列{col}", 
                           font=('Arial', 12), bg=color)
            label.pack(expand=True)
        
        # 检查配置
        print("✅ 布局配置测试:")
        print(f"  行0权重: {main_container.grid_rowconfigure(0)['weight']}")
        print(f"  行1权重: {main_container.grid_rowconfigure(1)['weight']}")
        print(f"  行1最小高度: {main_container.grid_rowconfigure(1)['minsize']}")
        print(f"  列0权重: {main_container.grid_columnconfigure(0)['weight']}")
        print(f"  列1权重: {main_container.grid_columnconfigure(1)['weight']}")
        
        # 添加说明
        info_label = tk.Label(root, text="下排区域应该固定最小高度450px，上排区域使用剩余空间", 
                             font=('Arial', 10), fg='blue')
        info_label.pack(pady=5)
        
        print("✅ 布局配置测试窗口创建成功")
        
        # 运行5秒后自动关闭
        root.after(5000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 布局配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始实际应用程序界面布局修复测试...")
    
    # 测试布局配置
    success1 = test_layout_configuration()
    
    # 测试实际应用程序
    success2 = test_app_layout_fixes()
    
    if success1 and success2:
        print("\n🎉 实际应用程序界面布局修复测试完成！")
        print("✅ 布局配置正确")
        print("✅ 应用程序修复成功")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
