#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试调试输出
验证添加的调试信息是否正常工作
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_debug_clean_group_data():
    """测试调试版本的_clean_group_data"""
    print("🧪 测试调试版本的_clean_group_data...")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        import tkinter as tk
        
        # 创建应用实例
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        app = EnhancedCADAppV2(root)
        
        # 创建测试数据
        test_data = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
            'index',  # 字符串数据
            {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(50, 0), (50, 50)]},
            'total',
            'entity_count',
            None,
            {'layer': 'A-WALL'},  # 缺少type
            {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(0, 50), (50, 50)]}
        ]
        
        print(f"📋 测试数据: {len(test_data)} 个项目")
        
        # 调用调试版本的方法
        print("\n" + "="*50)
        result = app._clean_group_data(test_data)
        print("="*50)
        
        print(f"\n📊 最终结果: {len(result)} 个有效实体")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_usage_instructions():
    """创建使用说明"""
    print("\n📋 调试版本使用说明:")
    print("="*60)
    
    print("\n🔧 已添加的调试功能:")
    print("1. _clean_group_data方法现在会输出详细的处理信息")
    print("2. 可视化调用前会输出数据统计信息")
    print("3. 显示有效和无效实体的详细信息")
    
    print("\n👀 在实际使用时，您会看到类似这样的输出:")
    print("🔧 [DEBUG] _clean_group_data 调用:")
    print("  输入类型: <class 'list'>")
    print("  输入长度: 5")
    print("  直接使用列表: 5 个")
    print("    ✅ 有效实体[0]: LINE - A-WALL")
    print("    ⚠️ 跳过无效实体[1]: <class 'str'> - index")
    print("  清理结果: 2 个有效实体")
    
    print("\n🎨 [DEBUG] 准备可视化概览:")
    print("  原始current_group: <class 'list'> - 5 个")
    print("  清理后current_group: 2 个")
    print("  所有实体数: 100")
    print("  已标注实体数: 10")
    
    print("\n🔍 如何使用这些信息诊断问题:")
    print("1. 检查'输入长度'是否符合预期")
    print("2. 查看'有效实体'的数量是否正确")
    print("3. 确认'所有实体数'不为0")
    print("4. 验证数据类型是否正确")
    
    print("\n⚠️ 常见问题模式:")
    print("- 如果'输入长度'为0，说明没有数据传入")
    print("- 如果'有效实体'为0，说明所有数据都被过滤了")
    print("- 如果'所有实体数'为0，说明DXF文件没有正确加载")
    print("- 如果看到大量'跳过无效实体'，说明数据结构有问题")
    
    return True

def suggest_troubleshooting_steps():
    """建议故障排除步骤"""
    print("\n🔧 故障排除步骤:")
    print("="*60)
    
    print("\n1. 运行调试版本:")
    print("   - 使用修改后的main_enhanced_with_v2_fill.py")
    print("   - 加载您遇到问题的DXF文件")
    print("   - 观察控制台输出")
    
    print("\n2. 收集关键信息:")
    print("   - 记录'输入长度'和'有效实体'的数量")
    print("   - 注意是否有大量'跳过无效实体'")
    print("   - 检查'所有实体数'是否为0")
    
    print("\n3. 对比正常情况:")
    print("   - 使用一个能正常显示的DXF文件")
    print("   - 对比调试输出的差异")
    print("   - 找出问题文件的特殊之处")
    
    print("\n4. 根据输出判断问题:")
    print("   - 如果数据清理正常但显示异常 → 可视化器问题")
    print("   - 如果有效实体为0 → 数据结构问题")
    print("   - 如果所有实体数为0 → DXF加载问题")
    
    print("\n5. 反馈信息:")
    print("   - 将调试输出发送给开发者")
    print("   - 包含问题DXF文件（如果可能）")
    print("   - 描述具体的操作步骤")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("CAD分类标注工具 - 调试输出测试")
    print("=" * 60)
    
    steps = [
        ("测试调试版本", test_debug_clean_group_data),
        ("创建使用说明", create_usage_instructions),
        ("建议故障排除步骤", suggest_troubleshooting_steps)
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            step_func()
        except Exception as e:
            print(f"❌ 步骤执行失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 总结:")
    print("=" * 60)
    print("✅ 调试版本已准备就绪")
    print("📋 现在您可以运行实际的应用程序")
    print("👀 观察控制台输出来诊断问题")
    print("🔍 根据输出信息确定问题的具体位置")
    print("=" * 60)
