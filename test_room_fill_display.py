#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试房间填充栏显示问题
"""

import os
import sys
import tkinter as tk
from tkinter import ttk

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_layer_items_creation():
    """测试图层项创建"""
    print("🧪 测试图层项创建")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("房间填充栏显示测试")
        root.geometry("1200x900")  # 增加窗口高度
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        # 等待界面完全加载
        root.update()
        
        print("🔍 检查图层数据初始化...")
        
        # 检查图层数据
        if hasattr(app, 'layer_items_data'):
            print(f"📋 图层数据总数: {len(app.layer_items_data)}")
            for i, data in enumerate(app.layer_items_data):
                print(f"  {i+1}. {data[0]} - {data[1]} - {data[2]}")
        else:
            print("❌ 图层数据未初始化")
            return False
        
        # 检查图层顺序
        if hasattr(app, 'layer_order'):
            print(f"📋 图层顺序: {app.layer_order}")
        else:
            print("❌ 图层顺序未初始化")
            return False
        
        # 检查图层状态
        if hasattr(app, 'layer_states'):
            print(f"📋 图层状态:")
            for key, var in app.layer_states.items():
                print(f"  {key}: {var.get()}")
        else:
            print("❌ 图层状态未初始化")
            return False
        
        # 检查图层列表框架
        if hasattr(app, 'layer_list_frame'):
            children = app.layer_list_frame.winfo_children()
            print(f"📋 图层列表框架子组件数量: {len(children)}")
            
            for i, child in enumerate(children):
                print(f"  子组件{i+1}: {child.__class__.__name__}")
                if hasattr(child, 'winfo_height'):
                    height = child.winfo_height()
                    y_pos = child.winfo_y()
                    print(f"    高度: {height}px, Y位置: {y_pos}px")
        else:
            print("❌ 图层列表框架未找到")
            return False
        
        # 检查容器高度
        if hasattr(app, 'layer_control_container'):
            container_height = app.layer_control_container.winfo_height()
            print(f"📏 图层控制容器高度: {container_height}px")
        
        if hasattr(app, 'layer_list_frame'):
            list_height = app.layer_list_frame.winfo_height()
            print(f"📏 图层列表框架高度: {list_height}px")
        
        # 手动触发图层项创建
        print("🔧 手动触发图层项创建...")
        app._create_layer_items()
        
        # 再次检查
        root.update()
        children = app.layer_list_frame.winfo_children()
        print(f"📋 重新创建后子组件数量: {len(children)}")
        
        # 保持窗口打开以便观察
        print("🔍 窗口将保持打开10秒以便观察...")
        print("请检查图像控制区域是否显示了4个图层项")
        
        def close_window():
            print("⏰ 自动关闭窗口")
            root.quit()
        
        root.after(10000, close_window)  # 10秒后自动关闭
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_manual_layer_test():
    """创建手动图层测试界面"""
    print("🎨 创建手动图层测试界面")
    
    root = tk.Tk()
    root.title("图层项显示测试")
    root.geometry("800x600")
    
    # 创建主框架
    main_frame = tk.Frame(root)
    main_frame.pack(fill='both', expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(main_frame, text="图层项显示测试", 
                          font=('Arial', 14, 'bold'))
    title_label.pack(pady=(0, 10))
    
    # 创建模拟的图层控制区域
    layer_container = tk.Frame(main_frame, relief='ridge', bd=2, bg='#F0F8FF')
    layer_container.pack(fill='both', expand=True)
    layer_container.config(height=400)  # 设置高度
    
    # 图层列表框架
    layer_list = tk.Frame(layer_container, bg='#F0F8FF')
    layer_list.pack(fill='both', expand=True, padx=5, pady=5)
    
    # 图层数据
    layer_data = [
        ('cad_lines', 'CAD线条', '#2196F3'),
        ('wall_fill', '墙体填充', '#4CAF50'),
        ('furniture_fill', '家具填充', '#FF9800'),
        ('room_fill', '房间填充', '#9C27B0')
    ]
    
    print(f"🔧 创建 {len(layer_data)} 个图层项...")
    
    # 创建图层项
    for i, (key, name, color) in enumerate(layer_data):
        print(f"  创建图层项 {i+1}: {name}")
        
        # 图层项框架
        item_frame = tk.Frame(layer_list, bg='white', relief='ridge', bd=1)
        item_frame.pack(fill='x', padx=3, pady=3)
        
        # 图层行
        row = tk.Frame(item_frame, bg='white')
        row.pack(fill='x', padx=5, pady=5)
        
        # 颜色指示器
        color_canvas = tk.Canvas(row, width=20, height=20, bg='white', highlightthickness=0)
        color_canvas.pack(side='left', padx=(0, 8))
        color_canvas.create_oval(2, 2, 18, 18, fill=color, outline='black')
        
        # 图层名称
        name_label = tk.Label(row, text=name, font=('Arial', 10), bg='white')
        name_label.pack(side='left', padx=(0, 8))
        
        # 下拉菜单
        combo = ttk.Combobox(row, width=6, values=['显示', '隐藏'], state='readonly')
        combo.set('显示')
        combo.pack(side='left', padx=(0, 8))
        
        # 按钮组
        buttons = ['设置', '编辑', '复制', '删除', '上移', '下移']
        colors = ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#607D8B', '#795548']
        
        for btn_text, btn_color in zip(buttons, colors):
            btn = tk.Button(row, text=btn_text, bg=btn_color, fg='white',
                           font=('Arial', 8), width=4, height=1)
            btn.pack(side='left', padx=1)
    
    # 应用按钮
    apply_btn = tk.Button(layer_list, text="⚙️ 应用设置", 
                         bg='#FF5722', fg='white', font=('Arial', 10, 'bold'))
    apply_btn.pack(fill='x', padx=3, pady=5)
    
    # 信息标签
    info_label = tk.Label(main_frame, 
                         text="如果能看到4个图层项（包括房间填充），说明高度设置正确",
                         font=('Arial', 10), fg='blue')
    info_label.pack(pady=10)
    
    # 高度信息
    def show_heights():
        container_height = layer_container.winfo_height()
        list_height = layer_list.winfo_height()
        print(f"📏 容器高度: {container_height}px")
        print(f"📏 列表高度: {list_height}px")
        
        children = layer_list.winfo_children()
        print(f"📋 子组件数量: {len(children)}")
        
        for i, child in enumerate(children):
            if hasattr(child, 'winfo_height'):
                height = child.winfo_height()
                y_pos = child.winfo_y()
                print(f"  组件{i+1}: 高度={height}px, Y位置={y_pos}px")
    
    # 延迟显示高度信息
    root.after(1000, show_heights)
    
    root.mainloop()

def main():
    """主函数"""
    print("🧪 房间填充栏显示问题测试")
    print("=" * 50)
    
    # 选择测试模式
    print("请选择测试模式：")
    print("1. 实际应用测试（检查房间填充是否显示）")
    print("2. 手动图层测试（验证布局是否正确）")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            success = test_layer_items_creation()
            if success:
                print("\n✅ 实际应用测试完成")
            else:
                print("\n❌ 实际应用测试失败")
        elif choice == "2":
            create_manual_layer_test()
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
    finally:
        print("\n🏁 测试结束")

if __name__ == "__main__":
    main()
