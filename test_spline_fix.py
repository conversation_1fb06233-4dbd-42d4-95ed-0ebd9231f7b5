#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 _force_merge_spline_entities 方法的修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cad_data_processor import CADDataProcessor

def test_force_merge_spline_entities():
    """测试强制合并SPLINE实体方法"""
    processor = CADDataProcessor()
    
    # 创建测试数据 - 混合格式的组
    test_groups = [
        # 字典格式的组
        {
            'entities': [
                {'type': 'LINE', 'layer': 'A-WALL', 'start_x': 0, 'start_y': 0, 'end_x': 10, 'end_y': 0},
                {'type': 'SPLINE', 'layer': 'A-WALL', 'control_points': [[0, 0], [5, 5], [10, 0]]},
            ],
            'label': 'test_group',
            'group_type': 'wall',
            'layer': 'A-WALL',
            'status': 'auto',
            'confidence': 0.8
        },
        # 列表格式的组
        [
            {'type': 'LINE', 'layer': 'A-DOOR', 'start_x': 20, 'start_y': 0, 'end_x': 30, 'end_y': 0},
            {'type': 'ARC', 'layer': 'A-DOOR', 'center_x': 25, 'center_y': 0, 'radius': 5},
        ],
        # 另一个字典格式的组，只有SPLINE
        {
            'entities': [
                {'type': 'SPLINE', 'layer': 'A-WALL', 'control_points': [[15, 0], [20, 5], [25, 0]]},
            ],
            'label': 'spline_only_group',
            'group_type': 'other',
            'layer': 'A-WALL',
            'status': 'manual',
            'confidence': 0.5
        }
    ]
    
    print("测试数据:")
    for i, group in enumerate(test_groups):
        if isinstance(group, dict):
            entities = group.get('entities', [])
            print(f"  组 {i} (字典格式): {len(entities)} 个实体")
            for j, entity in enumerate(entities):
                print(f"    实体 {j}: {entity.get('type', 'UNKNOWN')}")
        else:
            print(f"  组 {i} (列表格式): {len(group)} 个实体")
            for j, entity in enumerate(group):
                print(f"    实体 {j}: {entity.get('type', 'UNKNOWN')}")
    
    print("\n调用 _force_merge_spline_entities...")
    
    try:
        result_groups = processor._force_merge_spline_entities(test_groups)
        
        print(f"处理成功！结果组数: {len(result_groups)}")
        
        for i, group in enumerate(result_groups):
            if isinstance(group, dict):
                entities = group.get('entities', [])
                print(f"  结果组 {i} (字典格式): {len(entities)} 个实体")
                for j, entity in enumerate(entities):
                    print(f"    实体 {j}: {entity.get('type', 'UNKNOWN')}")
            else:
                print(f"  结果组 {i} (列表格式): {len(group)} 个实体")
                for j, entity in enumerate(group):
                    print(f"    实体 {j}: {entity.get('type', 'UNKNOWN')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试 _force_merge_spline_entities 方法修复...")
    success = test_force_merge_spline_entities()
    if success:
        print("✅ 测试通过！修复成功。")
    else:
        print("❌ 测试失败！需要进一步修复。")
