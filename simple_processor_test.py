#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的处理器追踪测试
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_processor_tracking():
    """测试处理器追踪功能"""
    print("🧪 测试处理器追踪功能")
    print("="*50)
    
    # 模拟追踪类
    class MockApp:
        def __init__(self):
            # 初始化追踪变量
            self._processor_reset_count = 0
            self._processor_reset_history = []
            self._last_processor_state = None
            print("🔍 处理器重置追踪已启动")
        
        def _track_processor_reset(self, location):
            """追踪处理器重置事件"""
            import time
            import traceback
            
            self._processor_reset_count += 1
            timestamp = time.strftime("%H:%M:%S.%f", time.localtime())[:-3]
            
            # 获取调用栈
            stack = traceback.extract_stack()
            caller_info = []
            for frame in stack[-4:-1]:
                caller_info.append(f"{os.path.basename(frame.filename)}:{frame.lineno} in {frame.name}")
            
            # 记录重置事件
            reset_event = {
                'count': self._processor_reset_count,
                'timestamp': timestamp,
                'location': location,
                'caller_stack': caller_info
            }
            
            self._processor_reset_history.append(reset_event)
            
            # 打印详细信息
            print(f"🔍 [{timestamp}] 处理器重置 #{self._processor_reset_count}")
            print(f"   📍 触发位置: {location}")
            print(f"   📞 调用路径:")
            for i, caller in enumerate(caller_info):
                print(f"     {i+1}. {caller}")
        
        def test_reset_scenario(self):
            """测试重置场景"""
            print("\n📋 测试重置场景:")
            
            # 模拟不同的重置场景
            scenarios = [
                "_load_from_cache",
                "update_group_list", 
                "_update_group_list_enhanced",
                "file_selection"
            ]
            
            for scenario in scenarios:
                print(f"\n🎯 模拟场景: {scenario}")
                self._track_processor_reset(scenario)
                time.sleep(0.1)  # 短暂延迟
            
            print(f"\n📊 测试完成，总重置次数: {self._processor_reset_count}")
    
    # 运行测试
    app = MockApp()
    app.test_reset_scenario()
    
    print("\n✅ 处理器追踪功能测试完成")

if __name__ == "__main__":
    test_processor_tracking()
