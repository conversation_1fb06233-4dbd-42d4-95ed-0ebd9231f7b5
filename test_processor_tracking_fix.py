#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试处理器追踪修复
验证追踪方法是否正确工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_tracking_method_exists():
    """测试追踪方法是否存在"""
    print("🔍 测试追踪方法是否存在")
    print("="*50)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查初始化代码
        init_checks = [
            '_processor_reset_count = 0',
            '_processor_reset_history = []',
            '_last_processor_state = None'
        ]
        
        print("1. 检查初始化代码:")
        for check in init_checks:
            if check in content:
                print(f"  ✅ {check}")
            else:
                print(f"  ❌ {check}")
        
        # 检查追踪方法
        method_checks = [
            'def _track_processor_reset(self, location):',
            'def _analyze_reset_pattern(self):',
            'def _get_processor_reset_summary(self):'
        ]
        
        print("\n2. 检查追踪方法:")
        for check in method_checks:
            if check in content:
                print(f"  ✅ {check}")
            else:
                print(f"  ❌ {check}")
        
        # 检查安全调用
        safe_call = "if hasattr(self, '_track_processor_reset'):"
        if safe_call in content:
            print(f"\n3. ✅ 安全调用检查已添加")
        else:
            print(f"\n3. ❌ 安全调用检查未添加")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_simple_test():
    """创建简单的处理器测试"""
    print(f"\n🧪 创建简单的处理器测试")
    print("="*50)
    
    test_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的处理器追踪测试
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_processor_tracking():
    """测试处理器追踪功能"""
    print("🧪 测试处理器追踪功能")
    print("="*50)
    
    # 模拟追踪类
    class MockApp:
        def __init__(self):
            # 初始化追踪变量
            self._processor_reset_count = 0
            self._processor_reset_history = []
            self._last_processor_state = None
            print("🔍 处理器重置追踪已启动")
        
        def _track_processor_reset(self, location):
            """追踪处理器重置事件"""
            import time
            import traceback
            
            self._processor_reset_count += 1
            timestamp = time.strftime("%H:%M:%S.%f", time.localtime())[:-3]
            
            # 获取调用栈
            stack = traceback.extract_stack()
            caller_info = []
            for frame in stack[-4:-1]:
                caller_info.append(f"{os.path.basename(frame.filename)}:{frame.lineno} in {frame.name}")
            
            # 记录重置事件
            reset_event = {
                'count': self._processor_reset_count,
                'timestamp': timestamp,
                'location': location,
                'caller_stack': caller_info
            }
            
            self._processor_reset_history.append(reset_event)
            
            # 打印详细信息
            print(f"🔍 [{timestamp}] 处理器重置 #{self._processor_reset_count}")
            print(f"   📍 触发位置: {location}")
            print(f"   📞 调用路径:")
            for i, caller in enumerate(caller_info):
                print(f"     {i+1}. {caller}")
        
        def test_reset_scenario(self):
            """测试重置场景"""
            print("\\n📋 测试重置场景:")
            
            # 模拟不同的重置场景
            scenarios = [
                "_load_from_cache",
                "update_group_list", 
                "_update_group_list_enhanced",
                "file_selection"
            ]
            
            for scenario in scenarios:
                print(f"\\n🎯 模拟场景: {scenario}")
                self._track_processor_reset(scenario)
                time.sleep(0.1)  # 短暂延迟
            
            print(f"\\n📊 测试完成，总重置次数: {self._processor_reset_count}")
    
    # 运行测试
    app = MockApp()
    app.test_reset_scenario()
    
    print("\\n✅ 处理器追踪功能测试完成")

if __name__ == "__main__":
    test_processor_tracking()
'''
    
    with open('simple_processor_test.py', 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    print("✅ 简单测试已创建: simple_processor_test.py")

def create_startup_test():
    """创建启动测试"""
    print(f"\n🚀 创建启动测试")
    print("="*50)
    
    startup_test = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动测试 - 验证主程序能否正常启动
"""

import sys
import os
import tkinter as tk
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_main_startup():
    """测试主程序启动"""
    print("🚀 测试主程序启动")
    print("="*50)
    
    try:
        # 创建根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        print("1. 尝试导入主程序...")
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        print("  ✅ 主程序导入成功")
        
        print("2. 尝试创建应用实例...")
        app = EnhancedCADAppV2(root)
        print("  ✅ 应用实例创建成功")
        
        print("3. 检查追踪功能初始化...")
        if hasattr(app, '_processor_reset_count'):
            print(f"  ✅ 追踪计数器: {app._processor_reset_count}")
        else:
            print("  ❌ 追踪计数器未初始化")
        
        if hasattr(app, '_track_processor_reset'):
            print("  ✅ 追踪方法存在")
        else:
            print("  ❌ 追踪方法不存在")
        
        print("4. 测试追踪方法调用...")
        if hasattr(app, '_track_processor_reset'):
            app._track_processor_reset("startup_test")
            print("  ✅ 追踪方法调用成功")
        else:
            print("  ❌ 追踪方法调用失败")
        
        # 清理
        root.destroy()
        
        print("\\n🎉 启动测试完成 - 所有功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_main_startup()
'''
    
    with open('startup_test.py', 'w', encoding='utf-8') as f:
        f.write(startup_test)
    
    print("✅ 启动测试已创建: startup_test.py")

def main():
    """主测试函数"""
    print("🚀 开始测试处理器追踪修复")
    
    try:
        # 1. 测试追踪方法是否存在
        method_success = test_tracking_method_exists()
        
        # 2. 创建简单测试
        create_simple_test()
        
        # 3. 创建启动测试
        create_startup_test()
        
        print(f"\n" + "="*60)
        print(f"📊 测试结果总结:")
        print(f"  追踪方法检查: {'✅ 通过' if method_success else '❌ 失败'}")
        print(f"  简单测试创建: ✅ 完成")
        print(f"  启动测试创建: ✅ 完成")
        
        print(f"\n🎯 下一步操作:")
        print(f"   1. 运行简单测试: python simple_processor_test.py")
        print(f"   2. 运行启动测试: python startup_test.py")
        print(f"   3. 如果测试通过，运行主程序: python main_enhanced_with_v2_fill.py")
        
        print(f"\n💡 修复说明:")
        print(f"   - 已添加安全检查: hasattr(self, '_track_processor_reset')")
        print(f"   - 如果追踪未初始化，会显示简化日志")
        print(f"   - 避免了 AttributeError 错误")
        
    except Exception as e:
        print(f"❌ 测试过程失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
