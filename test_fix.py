#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的代码
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_data_structure():
    """测试数据结构处理"""
    
    # 模拟字典格式的组
    dict_group = {
        'entities': [
            {'id': 1, 'type': 'LINE'},
            {'id': 2, 'type': 'LINE'}
        ],
        'label': 'wall_group_1',
        'group_type': 'wall'
    }
    
    # 模拟列表格式的组
    list_group = [
        {'id': 3, 'type': 'LINE'},
        {'id': 4, 'type': 'LINE'}
    ]
    
    # 测试处理逻辑
    groups = [dict_group, list_group]
    
    print("原始组:")
    for i, group in enumerate(groups):
        print(f"  组 {i+1}: {type(group)} - {group}")
    
    # 应用修复逻辑
    entity_groups = [group.get('entities', []) if isinstance(group, dict) else group for group in groups]
    
    print("\n处理后的实体组:")
    for i, group in enumerate(entity_groups):
        print(f"  组 {i+1}: {type(group)} - {group}")
        
        # 测试对实体的操作
        for entity in group:
            if isinstance(entity, dict):
                entity['label'] = 'test_label'
                entity['auto_labeled'] = True
                print(f"    实体 {entity.get('id', 'unknown')}: 标签设置成功")
            else:
                print(f"    警告: 实体不是字典格式: {entity}")

if __name__ == "__main__":
    test_data_structure()
