#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
全面测试分组功能：墙体、门窗、其他线条的分组和显示
"""

import sys
import os
import json
import math
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_enhanced import EnhancedCADProcessor

def create_test_entities():
    """创建测试实体数据"""
    entities = []
    
    print("🏗️ 创建测试实体数据...")
    
    # ========== 墙体线条 (A-WALL图层) ==========
    print("  📐 创建墙体线条...")
    
    # 墙体组1: 外墙 - 矩形房间外轮廓
    wall_group1 = [
        # 底边墙
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (6000, 0)], 'color': 7, 'linetype': 'CONTINUOUS'},
        # 右边墙
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(6000, 0), (6000, 4000)], 'color': 7, 'linetype': 'CONTINUOUS'},
        # 顶边墙
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(6000, 4000), (0, 4000)], 'color': 7, 'linetype': 'CONTINUOUS'},
        # 左边墙
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 4000), (0, 0)], 'color': 7, 'linetype': 'CONTINUOUS'},
    ]
    
    # 墙体组2: 内墙 - 房间分隔墙
    wall_group2 = [
        # 垂直分隔墙
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(3000, 0), (3000, 4000)], 'color': 7, 'linetype': 'CONTINUOUS'},
        # 水平分隔墙
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 2000), (3000, 2000)], 'color': 7, 'linetype': 'CONTINUOUS'},
    ]
    
    # 墙体组3: 独立墙段 - 分离的墙体
    wall_group3 = [
        # 独立墙段1
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(8000, 1000), (10000, 1000)], 'color': 7, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(10000, 1000), (10000, 3000)], 'color': 7, 'linetype': 'CONTINUOUS'},
        # 独立墙段2 (L形)
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(12000, 0), (14000, 0)], 'color': 7, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(14000, 0), (14000, 1500)], 'color': 7, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(14000, 1500), (13000, 1500)], 'color': 7, 'linetype': 'CONTINUOUS'},
    ]
    
    entities.extend(wall_group1)
    entities.extend(wall_group2)
    entities.extend(wall_group3)
    print(f"    ✅ 墙体线条: {len(wall_group1 + wall_group2 + wall_group3)} 条")
    
    # ========== 门窗线条 (A-WINDOW, A-DOOR图层) ==========
    print("  🚪 创建门窗线条...")
    
    # 门窗组1: 窗户 - 底墙上的窗户
    window_group1 = [
        # 窗户1 - 双线表示
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(1000, -50), (2000, -50)], 'color': 3, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(1000, 50), (2000, 50)], 'color': 3, 'linetype': 'CONTINUOUS'},
        # 窗户2 - 双线表示
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(4000, -50), (5000, -50)], 'color': 3, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(4000, 50), (5000, 50)], 'color': 3, 'linetype': 'CONTINUOUS'},
    ]
    
    # 门窗组2: 门 - 右墙上的门
    door_group1 = [
        # 门1 - 开启弧线
        {'type': 'ARC', 'layer': 'A-DOOR', 'center': (6050, 1500), 'radius': 800, 'start_angle': 0, 'end_angle': 90, 'color': 2},
        {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(6050, 1500), (6850, 1500)], 'color': 2, 'linetype': 'CONTINUOUS'},
        # 门2 - 内门
        {'type': 'ARC', 'layer': 'A-DOOR', 'center': (3050, 2500), 'radius': 600, 'start_angle': 180, 'end_angle': 270, 'color': 2},
        {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(3050, 2500), (3050, 3100)], 'color': 2, 'linetype': 'CONTINUOUS'},
    ]
    
    # 门窗组3: 独立门窗
    window_group2 = [
        # 独立窗户
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(8500, 950), (9500, 950)], 'color': 3, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(8500, 1050), (9500, 1050)], 'color': 3, 'linetype': 'CONTINUOUS'},
    ]
    
    entities.extend(window_group1)
    entities.extend(door_group1)
    entities.extend(window_group2)
    print(f"    ✅ 门窗线条: {len(window_group1 + door_group1 + window_group2)} 条")
    
    # ========== 其他线条 (不同图层) ==========
    print("  📏 创建其他线条...")
    
    # 尺寸标注线条
    dimension_group = [
        # 水平尺寸线
        {'type': 'LINE', 'layer': 'A-DIMS', 'points': [(0, -500), (6000, -500)], 'color': 1, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-DIMS', 'points': [(0, -450), (0, -550)], 'color': 1, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-DIMS', 'points': [(6000, -450), (6000, -550)], 'color': 1, 'linetype': 'CONTINUOUS'},
        # 垂直尺寸线
        {'type': 'LINE', 'layer': 'A-DIMS', 'points': [(-500, 0), (-500, 4000)], 'color': 1, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-DIMS', 'points': [(-450, 0), (-550, 0)], 'color': 1, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-DIMS', 'points': [(-450, 4000), (-550, 4000)], 'color': 1, 'linetype': 'CONTINUOUS'},
    ]
    
    # 家具线条
    furniture_group = [
        # 桌子
        {'type': 'LINE', 'layer': 'A-FURN', 'points': [(500, 500), (1500, 500)], 'color': 5, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-FURN', 'points': [(1500, 500), (1500, 1500)], 'color': 5, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-FURN', 'points': [(1500, 1500), (500, 1500)], 'color': 5, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-FURN', 'points': [(500, 1500), (500, 500)], 'color': 5, 'linetype': 'CONTINUOUS'},
        # 床
        {'type': 'LINE', 'layer': 'A-FURN', 'points': [(3500, 2500), (5500, 2500)], 'color': 5, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-FURN', 'points': [(5500, 2500), (5500, 3800)], 'color': 5, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-FURN', 'points': [(5500, 3800), (3500, 3800)], 'color': 5, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-FURN', 'points': [(3500, 3800), (3500, 2500)], 'color': 5, 'linetype': 'CONTINUOUS'},
    ]
    
    # 文字和标注
    text_group = [
        # 房间标注线
        {'type': 'LINE', 'layer': 'A-TEXT', 'points': [(1500, 1000), (2500, 1000)], 'color': 4, 'linetype': 'DASHED'},
        {'type': 'LINE', 'layer': 'A-TEXT', 'points': [(4500, 3000), (5500, 3000)], 'color': 4, 'linetype': 'DASHED'},
    ]
    
    entities.extend(dimension_group)
    entities.extend(furniture_group)
    entities.extend(text_group)
    print(f"    ✅ 其他线条: {len(dimension_group + furniture_group + text_group)} 条")
    
    print(f"  📊 总实体数: {len(entities)}")
    return entities

def test_grouping_and_display():
    """测试分组和显示功能"""
    print("\n" + "="*80)
    print("🧪 开始测试分组和显示功能")
    print("="*80)
    
    # 创建测试数据
    test_entities = create_test_entities()
    
    # 创建处理器
    processor = EnhancedCADProcessor()
    processor.entities = test_entities
    processor.all_groups = []
    
    print(f"\n📋 测试数据统计:")
    layer_stats = {}
    type_stats = {}
    
    for entity in test_entities:
        layer = entity.get('layer', 'UNKNOWN')
        entity_type = entity.get('type', 'UNKNOWN')
        
        layer_stats[layer] = layer_stats.get(layer, 0) + 1
        type_stats[entity_type] = type_stats.get(entity_type, 0) + 1
    
    print("  按图层统计:")
    for layer, count in sorted(layer_stats.items()):
        print(f"    {layer}: {count} 个实体")
    
    print("  按类型统计:")
    for entity_type, count in sorted(type_stats.items()):
        print(f"    {entity_type}: {count} 个实体")
    
    # 测试特殊图层识别
    print(f"\n🔍 测试特殊图层识别:")
    wall_layers = processor.processor._detect_special_layers(
        test_entities, processor.processor.wall_layer_patterns, debug=False, layer_type="墙体"
    )
    door_window_layers = processor.processor._detect_special_layers(
        test_entities, processor.processor.door_window_layer_patterns, debug=False, layer_type="门窗"
    )
    
    print(f"  识别到墙体图层: {wall_layers}")
    print(f"  识别到门窗图层: {door_window_layers}")
    
    # 测试墙体线条合并
    print(f"\n🔧 测试墙体线条合并:")
    wall_entities = [e for e in test_entities if e['layer'] in wall_layers]
    processor._test_wall_line_merging(wall_entities)
    
    # 测试自动分组
    print(f"\n🤖 测试自动分组:")
    try:
        # 直接测试分组方法，而不是依赖完整的处理流程

        # 测试墙体分组
        wall_entities = [e for e in test_entities if e['layer'] in wall_layers]
        if wall_entities:
            wall_groups = processor.processor._group_special_entities_by_layer(
                wall_entities, connection_threshold=10, entity_type="wall"
            )
            print(f"  墙体分组: {len(wall_groups)} 个组")
            processor.all_groups.extend(wall_groups)

        # 测试门窗分组
        door_window_entities = [e for e in test_entities if e['layer'] in door_window_layers]
        if door_window_entities:
            door_window_groups = processor.processor._group_special_entities_by_layer(
                door_window_entities, connection_threshold=50, entity_type="door_window"
            )
            print(f"  门窗分组: {len(door_window_groups)} 个组")
            processor.all_groups.extend(door_window_groups)

        # 测试其他实体分组
        processed_entity_ids = set()
        if wall_entities:
            processed_entity_ids.update(id(e) for e in wall_entities)
        if door_window_entities:
            processed_entity_ids.update(id(e) for e in door_window_entities)

        other_entities = [e for e in test_entities if id(e) not in processed_entity_ids]
        if other_entities:
            other_groups = processor.processor._group_other_entities_by_layer(other_entities, distance_threshold=40)
            print(f"  其他实体分组: {len(other_groups)} 个组")

            # 转换为统一格式
            for group in other_groups:
                if isinstance(group, list):
                    # 转换为字典格式
                    if group:  # 确保组不为空
                        layer = group[0].get('layer', 'UNKNOWN')
                        group_dict = {
                            'entities': group,
                            'label': None,
                            'group_type': 'other',
                            'layer': layer,
                            'status': 'pending',
                            'confidence': 0.5
                        }
                        processor.all_groups.append(group_dict)

        print(f"  ✅ 自动分组完成，共生成 {len(processor.all_groups)} 个组")
        
        # 分析分组结果
        print(f"\n📊 分组结果分析:")
        for i, group in enumerate(processor.all_groups):
            if isinstance(group, dict):
                entities = group.get('entities', [])
                group_type = group.get('group_type', 'unknown')
                layer = group.get('layer', 'unknown')
                status = group.get('status', 'unknown')
            elif isinstance(group, list):
                entities = group
                group_type = 'list_format'
                layer = entities[0].get('layer', 'unknown') if entities else 'empty'
                status = 'unknown'
            else:
                continue
            
            print(f"  组 {i+1}:")
            print(f"    类型: {group_type}")
            print(f"    图层: {layer}")
            print(f"    状态: {status}")
            print(f"    实体数: {len(entities)}")
            
            # 统计实体类型
            entity_types = {}
            for entity in entities:
                if isinstance(entity, dict):
                    entity_type = entity.get('type', 'UNKNOWN')
                    entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            
            print(f"    实体类型: {dict(entity_types)}")
        
    except Exception as e:
        print(f"  ❌ 自动分组失败: {e}")
        import traceback
        traceback.print_exc()
    
    return processor, test_entities

def test_visualization_data():
    """测试可视化数据准备"""
    print(f"\n🎨 测试可视化数据准备:")
    
    processor, test_entities = test_grouping_and_display()
    
    # 测试坐标范围计算
    print(f"  📐 计算坐标范围:")
    
    x_coords = []
    y_coords = []
    
    for entity in test_entities:
        if entity.get('type') == 'LINE':
            points = entity.get('points', [])
            for point in points:
                if isinstance(point, (list, tuple)) and len(point) >= 2:
                    x_coords.append(point[0])
                    y_coords.append(point[1])
        elif entity.get('type') == 'ARC':
            center = entity.get('center')
            radius = entity.get('radius', 0)
            if center and isinstance(center, (list, tuple)) and len(center) >= 2:
                x_coords.extend([center[0] - radius, center[0] + radius])
                y_coords.extend([center[1] - radius, center[1] + radius])
    
    if x_coords and y_coords:
        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        
        print(f"    X范围: {x_min} ~ {x_max} (跨度: {x_max - x_min})")
        print(f"    Y范围: {y_min} ~ {y_max} (跨度: {y_max - y_min})")
        print(f"    图纸尺寸: {(x_max - x_min)/1000:.1f}m × {(y_max - y_min)/1000:.1f}m")
    
    # 测试分组边界框计算
    print(f"  📦 计算分组边界框:")
    
    for i, group in enumerate(processor.all_groups[:5]):  # 只显示前5个组
        if isinstance(group, dict):
            entities = group.get('entities', [])
        elif isinstance(group, list):
            entities = group
        else:
            continue
        
        if not entities:
            continue
        
        group_x_coords = []
        group_y_coords = []
        
        for entity in entities:
            if isinstance(entity, dict) and entity.get('type') == 'LINE':
                points = entity.get('points', [])
                for point in points:
                    if isinstance(point, (list, tuple)) and len(point) >= 2:
                        group_x_coords.append(point[0])
                        group_y_coords.append(point[1])
        
        if group_x_coords and group_y_coords:
            gx_min, gx_max = min(group_x_coords), max(group_x_coords)
            gy_min, gy_max = min(group_y_coords), max(group_y_coords)
            
            print(f"    组 {i+1}: X({gx_min}~{gx_max}), Y({gy_min}~{gy_max})")

def generate_test_report():
    """生成测试报告"""
    print(f"\n📋 生成测试报告:")
    
    processor, test_entities = test_grouping_and_display()
    
    report = {
        "test_summary": {
            "total_entities": len(test_entities),
            "total_groups": len(processor.all_groups),
            "test_time": "2025-01-28"
        },
        "entity_statistics": {},
        "layer_statistics": {},
        "grouping_results": [],
        "expected_vs_actual": {}
    }
    
    # 统计实体信息
    for entity in test_entities:
        layer = entity.get('layer', 'UNKNOWN')
        entity_type = entity.get('type', 'UNKNOWN')
        
        if layer not in report["layer_statistics"]:
            report["layer_statistics"][layer] = {}
        
        if entity_type not in report["layer_statistics"][layer]:
            report["layer_statistics"][layer][entity_type] = 0
        
        report["layer_statistics"][layer][entity_type] += 1
        
        if entity_type not in report["entity_statistics"]:
            report["entity_statistics"][entity_type] = 0
        report["entity_statistics"][entity_type] += 1
    
    # 统计分组结果
    for i, group in enumerate(processor.all_groups):
        if isinstance(group, dict):
            entities = group.get('entities', [])
            group_info = {
                "group_id": i + 1,
                "group_type": group.get('group_type', 'unknown'),
                "layer": group.get('layer', 'unknown'),
                "status": group.get('status', 'unknown'),
                "entity_count": len(entities),
                "entity_types": {}
            }
            
            for entity in entities:
                if isinstance(entity, dict):
                    entity_type = entity.get('type', 'UNKNOWN')
                    if entity_type not in group_info["entity_types"]:
                        group_info["entity_types"][entity_type] = 0
                    group_info["entity_types"][entity_type] += 1
            
            report["grouping_results"].append(group_info)
    
    # 期望vs实际结果 - 调试统计
    wall_groups_actual = 0
    door_window_groups_actual = 0
    other_groups_actual = 0

    for g in processor.all_groups:
        if isinstance(g, dict):
            group_type = g.get('group_type', 'unknown')
            if group_type == 'wall':
                wall_groups_actual += 1
            elif group_type == 'door_window':
                door_window_groups_actual += 1
            elif group_type == 'other':
                other_groups_actual += 1

    report["expected_vs_actual"] = {
        "wall_groups_expected": 3,
        "wall_groups_actual": wall_groups_actual,
        "door_window_groups_expected": 3,
        "door_window_groups_actual": door_window_groups_actual,
        "other_groups_expected": 3,
        "other_groups_actual": other_groups_actual
    }
    
    # 保存报告
    with open('test_grouping_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"  ✅ 测试报告已保存: test_grouping_report.json")
    
    # 打印简要报告
    print(f"\n📊 测试结果简要:")
    print(f"  总实体数: {report['test_summary']['total_entities']}")
    print(f"  总分组数: {report['test_summary']['total_groups']}")
    print(f"  墙体组: 期望{report['expected_vs_actual']['wall_groups_expected']}个, 实际{report['expected_vs_actual']['wall_groups_actual']}个")
    print(f"  门窗组: 期望{report['expected_vs_actual']['door_window_groups_expected']}个, 实际{report['expected_vs_actual']['door_window_groups_actual']}个")
    print(f"  其他组: 期望{report['expected_vs_actual']['other_groups_expected']}个, 实际{report['expected_vs_actual']['other_groups_actual']}个")
    
    return report

def main():
    """主测试函数"""
    print("🚀 开始全面测试分组和显示功能")
    
    try:
        # 1. 测试分组功能
        processor, test_entities = test_grouping_and_display()
        
        # 2. 测试可视化数据
        test_visualization_data()
        
        # 3. 生成测试报告
        report = generate_test_report()
        
        print(f"\n✅ 全面测试完成!")
        print(f"  📄 详细报告: test_grouping_report.json")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
