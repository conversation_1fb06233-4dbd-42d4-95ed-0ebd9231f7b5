📁 main_enhanced_with_v2_fill.py:
  ⚠️ 第1658行: 全图概览可能使用了未清理的组数据
     代码: self.processor.visualizer.visualize_overview(
     建议: 确保传递给 visualize_overview 的组数据已清理
     修复: 使用 cleaned_group 而不是原始 group
  ⚠️ 第2141行: 全图概览可能使用了未清理的组数据
     代码: self.processor.visualizer.visualize_overview(
     建议: 确保传递给 visualize_overview 的组数据已清理
     修复: 使用 cleaned_group 而不是原始 group
  ⚠️ 第3475行: 全图概览可能使用了未清理的组数据
     代码: self.processor.visualizer.visualize_overview(
     建议: 确保传递给 visualize_overview 的组数据已清理
     修复: 使用 cleaned_group 而不是原始 group
  ⚠️ 第3684行: 全图概览可能使用了未清理的组数据
     代码: self.processor.visualizer.visualize_overview(
     建议: 确保传递给 visualize_overview 的组数据已清理
     修复: 使用 cleaned_group 而不是原始 group
  ⚠️ 第4167行: 全图概览可能使用了未清理的组数据
     代码: self.processor.visualizer.visualize_overview(
     建议: 确保传递给 visualize_overview 的组数据已清理
     修复: 使用 cleaned_group 而不是原始 group
  ⚠️ 第6809行: 全图概览可能使用了未清理的组数据
     代码: self.processor.visualizer.visualize_overview(
     建议: 确保传递给 visualize_overview 的组数据已清理
     修复: 使用 cleaned_group 而不是原始 group

📁 main_enhanced_with_v2_fill_original.py:
  ⚠️ 第1456行: 全图概览可能使用了未清理的组数据
     代码: self.processor.visualizer.visualize_overview(
     建议: 确保传递给 visualize_overview 的组数据已清理
     修复: 使用 cleaned_group 而不是原始 group
  ⚠️ 第1667行: 全图概览可能使用了未清理的组数据
     代码: self.processor.visualizer.visualize_overview(
     建议: 确保传递给 visualize_overview 的组数据已清理
     修复: 使用 cleaned_group 而不是原始 group
  ⚠️ 第2150行: 全图概览可能使用了未清理的组数据
     代码: self.processor.visualizer.visualize_overview(
     建议: 确保传递给 visualize_overview 的组数据已清理
     修复: 使用 cleaned_group 而不是原始 group
  ⚠️ 第3479行: 全图概览可能使用了未清理的组数据
     代码: self.processor.visualizer.visualize_overview(
     建议: 确保传递给 visualize_overview 的组数据已清理
     修复: 使用 cleaned_group 而不是原始 group
  ⚠️ 第3688行: 全图概览可能使用了未清理的组数据
     代码: self.processor.visualizer.visualize_overview(
     建议: 确保传递给 visualize_overview 的组数据已清理
     修复: 使用 cleaned_group 而不是原始 group
  ⚠️ 第4171行: 全图概览可能使用了未清理的组数据
     代码: self.processor.visualizer.visualize_overview(
     建议: 确保传递给 visualize_overview 的组数据已清理
     修复: 使用 cleaned_group 而不是原始 group
  ⚠️ 第6730行: 全图概览可能使用了未清理的组数据
     代码: self.processor.visualizer.visualize_overview(
     建议: 确保传递给 visualize_overview 的组数据已清理
     修复: 使用 cleaned_group 而不是原始 group

🧪 测试数据清理一致性...
  📊 找到 3 个文件有清理方法
  ✅ 所有清理方法长度一致

============================================================
审计总结:
============================================================
❌ 发现 13 个潜在问题
建议按照上述修复建议进行修复
============================================================
PS C:\A-BCXM\CAD分类标注工具E02> 