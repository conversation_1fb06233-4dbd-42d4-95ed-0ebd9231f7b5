#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
诊断和修复可视化问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_passing_to_visualizer():
    """测试数据传递到可视化器的问题"""
    print("🔍 测试数据传递到可视化器")
    print("="*60)
    
    try:
        from main_enhanced import EnhancedCADProcessor
        from cad_visualizer import CADVisualizer
        
        # 创建处理器和可视化器
        processor = EnhancedCADProcessor()
        visualizer = CADVisualizer()
        
        # 创建测试数据
        test_entities = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': '墙体', 'auto_labeled': True},
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'label': '墙体', 'auto_labeled': True},
            {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(50, -10), (50, 10)], 'label': '门窗', 'auto_labeled': True}
        ]
        
        test_groups = [
            [test_entities[0], test_entities[1]],  # 墙体组
            [test_entities[2]]                     # 门窗组
        ]
        
        # 设置处理器数据
        processor.entities = test_entities
        processor.current_file_entities = test_entities
        processor.all_groups = test_groups
        processor.auto_labeled_entities = test_entities
        processor.labeled_entities = []
        processor._update_groups_info()
        
        print(f"✅ 处理器数据设置完成")
        print(f"  总实体数: {len(processor.current_file_entities)}")
        print(f"  总组数: {len(processor.all_groups)}")
        print(f"  自动标注实体数: {len(processor.auto_labeled_entities)}")
        
        # 测试数据传递
        print(f"\n🔍 测试数据传递:")
        
        # 1. 测试当前组数据
        current_group = processor.all_groups[0] if processor.all_groups else []
        print(f"  当前组原始数据: {len(current_group)} 个实体")
        
        # 检查组数据格式
        for i, entity in enumerate(current_group):
            print(f"    实体{i+1}: {type(entity)} - {entity if isinstance(entity, dict) else 'Invalid'}")
        
        # 2. 清理组数据
        if hasattr(processor, '_clean_group_data'):
            cleaned_group = processor._clean_group_data(current_group)
            print(f"  清理后组数据: {len(cleaned_group)} 个实体")
            
            for i, entity in enumerate(cleaned_group):
                print(f"    清理后实体{i+1}: {type(entity)} - {entity.get('type', 'unknown') if isinstance(entity, dict) else 'Invalid'}")
        else:
            cleaned_group = current_group
            print(f"  ❌ _clean_group_data 方法不存在")
        
        # 3. 测试已标注实体
        labeled_entities = processor.auto_labeled_entities + processor.labeled_entities
        print(f"  已标注实体数: {len(labeled_entities)}")
        
        for i, entity in enumerate(labeled_entities[:3]):  # 只显示前3个
            print(f"    标注实体{i+1}: {type(entity)} - {entity.get('label', 'no label') if isinstance(entity, dict) else 'Invalid'}")
        
        return processor, visualizer, cleaned_group, labeled_entities
        
    except Exception as e:
        print(f"❌ 数据传递测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, None

def test_group_data_format():
    """测试组数据格式问题"""
    print(f"\n🔍 测试组数据格式问题")
    print("="*60)
    
    try:
        # 模拟有问题的组数据
        problematic_groups = [
            # 正常组
            [
                {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': '墙体'},
                {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'label': '墙体'}
            ],
            # 包含字符串的组
            [
                {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(50, -10), (50, 10)], 'label': '门窗'},
                "invalid_string_entity",  # 这会导致错误
                {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(60, -10), (60, 10)], 'label': '门窗'}
            ],
            # 包含None的组
            [
                {'type': 'TEXT', 'layer': 'A-TEXT', 'position': (200, 200), 'text': '标注'},
                None,  # 这也会导致错误
                123    # 数字也会导致错误
            ]
        ]
        
        print(f"📋 测试有问题的组数据:")
        
        for i, group in enumerate(problematic_groups):
            print(f"\n  组{i+1} (原始数据):")
            print(f"    总项目数: {len(group)}")
            
            for j, item in enumerate(group):
                print(f"      项目{j+1}: {type(item)} - {str(item)[:50]}...")
            
            # 测试 _check_all_groups_completed 会遇到的问题
            print(f"    测试完成状态检查:")
            try:
                # 模拟原始的有问题的代码
                has_unlabeled = any(not entity.get('label') for entity in group)
                print(f"      ✅ 完成状态检查成功: {not has_unlabeled}")
            except Exception as e:
                print(f"      ❌ 完成状态检查失败: {e}")
            
            # 测试修复后的代码
            print(f"    测试修复后的完成状态检查:")
            try:
                has_unlabeled = any(
                    isinstance(entity, dict) and not entity.get('label') 
                    for entity in group 
                    if isinstance(entity, dict)
                )
                print(f"      ✅ 修复后检查成功: {not has_unlabeled}")
            except Exception as e:
                print(f"      ❌ 修复后检查失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 组数据格式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_matching_issue():
    """测试配色匹配问题"""
    print(f"\n🔍 测试配色匹配问题")
    print("="*60)
    
    try:
        # 模拟可能导致配色问题的数据
        test_data = [
            "single_value",  # 单个值
            ("tuple", "with", "three", "values"),  # 三个值的元组
            ["list", "with", "multiple", "values"],  # 多个值的列表
            {"dict": "value"},  # 字典
            None,  # None值
            42,  # 数字
        ]
        
        print(f"📋 测试可能导致配色问题的数据:")
        
        for i, data in enumerate(test_data):
            print(f"\n  数据{i+1}: {type(data)} - {data}")
            
            # 测试解包操作
            try:
                # 模拟原始的有问题的代码
                if isinstance(data, (tuple, list)) and len(data) >= 2:
                    a, b = data[:2]  # 只取前两个值
                    print(f"      ✅ 解包成功: a={a}, b={b}")
                else:
                    print(f"      ⚠️ 数据格式不适合解包")
            except Exception as e:
                print(f"      ❌ 解包失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配色匹配测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_visualizer_call():
    """测试可视化器调用"""
    print(f"\n🔍 测试可视化器调用")
    print("="*60)
    
    try:
        from main_enhanced import EnhancedCADProcessor
        from cad_visualizer import CADVisualizer
        
        # 创建处理器和可视化器
        processor = EnhancedCADProcessor()
        visualizer = CADVisualizer()
        
        # 创建测试数据
        test_entities = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': '墙体', 'auto_labeled': True},
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'label': '墙体', 'auto_labeled': True},
            {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(50, -10), (50, 10)], 'label': '门窗', 'auto_labeled': True}
        ]
        
        test_groups = [
            [test_entities[0], test_entities[1]],  # 墙体组
            [test_entities[2]]                     # 门窗组
        ]
        
        # 设置处理器数据
        processor.entities = test_entities
        processor.current_file_entities = test_entities
        processor.all_groups = test_groups
        processor.auto_labeled_entities = test_entities
        processor.labeled_entities = []
        processor._update_groups_info()
        
        # 准备可视化数据
        current_group = processor._clean_group_data(test_groups[0])
        labeled_entities = processor.auto_labeled_entities
        
        print(f"📋 可视化器调用参数:")
        print(f"  总实体数: {len(processor.current_file_entities)}")
        print(f"  当前组实体数: {len(current_group)}")
        print(f"  已标注实体数: {len(labeled_entities)}")
        
        # 测试可视化器调用
        print(f"\n🎨 测试可视化器调用:")
        
        try:
            # 测试全图概览
            visualizer.visualize_overview(
                processor.current_file_entities,
                current_group,
                labeled_entities,
                processor=processor
            )
            print(f"  ✅ 全图概览调用成功")
        except Exception as e:
            print(f"  ❌ 全图概览调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        try:
            # 测试组预览
            visualizer.visualize_entity_group(current_group, processor.category_mapping)
            print(f"  ✅ 组预览调用成功")
        except Exception as e:
            print(f"  ❌ 组预览调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ 可视化器调用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始可视化问题诊断和修复")
    
    try:
        # 1. 测试数据传递
        processor, visualizer, current_group, labeled_entities = test_data_passing_to_visualizer()
        
        # 2. 测试组数据格式
        test2_success = test_group_data_format()
        
        # 3. 测试配色匹配
        test3_success = test_color_matching_issue()
        
        # 4. 测试可视化器调用
        test4_success = test_visualizer_call()
        
        print(f"\n" + "="*60)
        print(f"📊 可视化问题诊断结果:")
        print(f"  数据传递: {'✅ 正常' if processor else '❌ 异常'}")
        print(f"  组数据格式: {'✅ 正常' if test2_success else '❌ 异常'}")
        print(f"  配色匹配: {'✅ 正常' if test3_success else '❌ 异常'}")
        print(f"  可视化器调用: {'✅ 正常' if test4_success else '❌ 异常'}")
        
        print(f"\n🔧 需要修复的问题:")
        if not processor:
            print(f"  - 数据传递到可视化器有问题")
        if not test2_success:
            print(f"  - 组数据包含非字典对象，需要清理")
        if not test3_success:
            print(f"  - 配色匹配逻辑需要修复")
        if not test4_success:
            print(f"  - 可视化器调用参数有问题")
        
        print(f"\n💡 修复建议:")
        print(f"  1. 修复 _check_all_groups_completed 方法中的类型检查")
        print(f"  2. 修复配色匹配中的解包错误")
        print(f"  3. 确保数据正确传递到可视化器")
        print(f"  4. 加强组数据的清理和验证")
        
    except Exception as e:
        print(f"❌ 可视化问题诊断失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
