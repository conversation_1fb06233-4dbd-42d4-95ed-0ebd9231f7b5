#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线段合并工具
在DXF文件导入后，对墙体图层的线条进行简化处理
使用距离阈值和角度阈值来识别可合并的线段
"""

import numpy as np
import math
import time
from collections import defaultdict
from typing import List, Dict, Tuple, Any, Optional

try:
    from shapely.geometry import LineString, Point
    from rtree import index
    SHAPELY_AVAILABLE = True
except ImportError:
    SHAPELY_AVAILABLE = False
    print("⚠️ Shapely或rtree未安装，将使用简化版本的线段合并")

class SimpleLineMerger:
    """简化的线段合并工具：连接端点相连且平行的线段"""

    def __init__(self, distance_threshold=5, angle_threshold=2, enable_iterative=True, max_iterations=3):
        """
        初始化合并工具

        Args:
            distance_threshold: 端点连接距离阈值（毫米）
            angle_threshold: 平行角度阈值（度）
            enable_iterative: 是否启用迭代合并
            max_iterations: 最大迭代次数
        """
        self.dist_thresh = distance_threshold
        self.angle_thresh = angle_threshold
        self.enable_iterative = enable_iterative
        self.max_iterations = max_iterations
        self.spatial_index = None
        self.line_index = {}
        self.stats = {
            'original_lines': 0,
            'merged_lines': 0,
            'final_lines': 0,
            'processing_time': 0.0,
            'iterations_performed': 0,
            'iteration_details': []
        }

        # 检查依赖
        self.use_advanced = SHAPELY_AVAILABLE
        if not self.use_advanced:
            print("🔧 使用简化版线段合并算法")
    
    def merge_lines(self, lines):
        """
        合并端点相连且平行的线段（支持迭代合并）

        Args:
            lines: 输入线段列表（LineString对象或坐标对列表）

        Returns:
            list: 合并后的线段列表
        """
        start_time = time.time()

        # 记录原始线段数量
        self.stats['original_lines'] = len(lines)
        self.stats['iteration_details'] = []

        if self.enable_iterative:
            merged_lines = self._iterative_merge(lines)
        else:
            if self.use_advanced:
                merged_lines = self._merge_lines_advanced(lines)
            else:
                merged_lines = self._merge_lines_simple(lines)
            self.stats['iterations_performed'] = 1

        # 记录统计信息
        self.stats['final_lines'] = len(merged_lines)
        self.stats['merged_lines'] = self.stats['original_lines'] - self.stats['final_lines']
        self.stats['processing_time'] = time.time() - start_time

        return merged_lines

    def _iterative_merge(self, lines):
        """
        迭代合并直到没有更多合并发生

        Args:
            lines: 输入线段列表

        Returns:
            list: 迭代合并后的线段列表
        """
        print(f"🔄 开始迭代合并，最大迭代次数: {self.max_iterations}")

        prev_count = len(lines)
        merged_lines = lines
        iteration = 0

        for i in range(self.max_iterations):
            iteration = i + 1
            print(f"  📍 迭代 {iteration}: 输入线段数量 {len(merged_lines)}")

            # 执行单次合并
            if self.use_advanced:
                current_merged = self._merge_lines_advanced(merged_lines)
            else:
                current_merged = self._merge_lines_simple(merged_lines)

            new_count = len(current_merged)
            merged_count = prev_count - new_count

            # 记录迭代详情
            iteration_info = {
                'iteration': iteration,
                'input_count': prev_count,
                'output_count': new_count,
                'merged_count': merged_count
            }
            self.stats['iteration_details'].append(iteration_info)

            print(f"    ✅ 迭代 {iteration} 完成: {prev_count} -> {new_count} (合并了 {merged_count} 条)")

            # 检查是否还有合并发生
            if new_count == prev_count:
                print(f"    🎯 迭代 {iteration} 后无更多合并，停止迭代")
                break

            # 更新状态
            merged_lines = current_merged
            prev_count = new_count

        self.stats['iterations_performed'] = iteration

        if iteration == self.max_iterations and len(merged_lines) < len(lines):
            print(f"    ⚠️ 达到最大迭代次数 {self.max_iterations}，可能还有合并机会")

        print(f"🎉 迭代合并完成: 总共 {iteration} 次迭代")
        return merged_lines

    def _merge_lines_advanced(self, lines):
        """使用Shapely和rtree的高级合并算法"""
        if not SHAPELY_AVAILABLE:
            return self._merge_lines_simple(lines)
        
        # 转换为LineString对象
        shapely_lines = []
        for line in lines:
            if isinstance(line, LineString):
                shapely_lines.append(line)
            else:
                # 假设是坐标对列表
                if len(line) >= 2:
                    shapely_lines.append(LineString(line))
        
        # 构建空间索引
        self._build_spatial_index(shapely_lines)
        
        # 创建线段图
        line_graph = self._build_line_graph(shapely_lines)
        
        # 合并连通分量
        merged_lines = self._merge_connected_components(shapely_lines, line_graph)
        
        return merged_lines
    
    def _merge_lines_simple(self, lines):
        """简化版合并算法（不依赖外部库）"""
        # 转换为统一格式
        simple_lines = []
        for line in lines:
            if hasattr(line, 'coords'):
                # Shapely LineString
                coords = list(line.coords)
            else:
                # 坐标列表
                coords = line
            
            if len(coords) >= 2:
                simple_lines.append(coords)
        
        # 构建简单的连接图
        line_graph = self._build_simple_line_graph(simple_lines)
        
        # 合并连通分量
        merged_lines = self._merge_simple_connected_components(simple_lines, line_graph)
        
        return merged_lines
    
    def _build_spatial_index(self, lines):
        """构建空间索引（高级版本）"""
        if not SHAPELY_AVAILABLE:
            return
        
        self.spatial_index = index.Index()
        self.line_index = {}
        
        for idx, line in enumerate(lines):
            bounds = line.bounds
            # 扩展边界以包含容差范围
            expanded_bounds = (
                bounds[0] - self.dist_thresh,
                bounds[1] - self.dist_thresh,
                bounds[2] + self.dist_thresh,
                bounds[3] + self.dist_thresh
            )
            self.spatial_index.insert(idx, expanded_bounds)
            self.line_index[idx] = line
    
    def _build_line_graph(self, lines):
        """构建线段连接图（高级版本）"""
        graph = defaultdict(list)
        endpoint_map = defaultdict(list)
        
        # 创建端点索引
        for idx, line in enumerate(lines):
            start, end = line.coords[0], line.coords[-1]
            endpoint_map[start].append(idx)
            endpoint_map[end].append(idx)
        
        # 查找可连接的线段
        for point, line_ids in endpoint_map.items():
            # 在空间索引中查找附近的端点
            nearby_indices = list(self.spatial_index.intersection((
                point[0] - self.dist_thresh,
                point[1] - self.dist_thresh,
                point[0] + self.dist_thresh,
                point[1] + self.dist_thresh
            )))
            
            for near_idx in nearby_indices:
                # 跳过自身
                if near_idx in line_ids:
                    continue
                
                # 检查线段是否平行
                if self._are_parallel_advanced(lines[line_ids[0]], lines[near_idx]):
                    # 连接这些线段
                    for line_id in line_ids:
                        if near_idx not in graph[line_id]:
                            graph[line_id].append(near_idx)
                        if line_id not in graph[near_idx]:
                            graph[near_idx].append(line_id)
        
        return graph
    
    def _build_simple_line_graph(self, lines):
        """构建简单线段连接图"""
        graph = defaultdict(list)
        
        for i in range(len(lines)):
            for j in range(i + 1, len(lines)):
                if self._can_connect_simple(lines[i], lines[j]):
                    graph[i].append(j)
                    graph[j].append(i)
        
        return graph
    
    def _can_connect_simple(self, line1, line2):
        """检查两条线段是否可以连接（简化版本）"""
        # 获取端点
        start1, end1 = line1[0], line1[-1]
        start2, end2 = line2[0], line2[-1]
        
        # 检查端点距离
        connections = [
            (start1, start2), (start1, end2),
            (end1, start2), (end1, end2)
        ]
        
        min_distance = float('inf')
        for p1, p2 in connections:
            dist = math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
            min_distance = min(min_distance, dist)
        
        if min_distance > self.dist_thresh:
            return False
        
        # 检查是否平行
        return self._are_parallel_simple(line1, line2)
    
    def _are_parallel_advanced(self, line1, line2):
        """检查两条线段是否平行（高级版本）"""
        # 计算线段1的方向向量
        dx1 = line1.coords[-1][0] - line1.coords[0][0]
        dy1 = line1.coords[-1][1] - line1.coords[0][1]
        
        # 计算线段2的方向向量
        dx2 = line2.coords[-1][0] - line2.coords[0][0]
        dy2 = line2.coords[-1][1] - line2.coords[0][1]
        
        return self._check_parallel_vectors(dx1, dy1, dx2, dy2)
    
    def _are_parallel_simple(self, line1, line2):
        """检查两条线段是否平行（简化版本）"""
        # 计算线段1的方向向量
        dx1 = line1[-1][0] - line1[0][0]
        dy1 = line1[-1][1] - line1[0][1]
        
        # 计算线段2的方向向量
        dx2 = line2[-1][0] - line2[0][0]
        dy2 = line2[-1][1] - line2[0][1]
        
        return self._check_parallel_vectors(dx1, dy1, dx2, dy2)
    
    def _check_parallel_vectors(self, dx1, dy1, dx2, dy2):
        """检查两个向量是否平行"""
        # 计算向量长度
        length1 = math.sqrt(dx1**2 + dy1**2)
        length2 = math.sqrt(dx2**2 + dy2**2)
        
        # 避免除以零
        if length1 < 1e-5 or length2 < 1e-5:
            return False
        
        # 计算点积
        dot_product = (dx1 * dx2 + dy1 * dy2) / (length1 * length2)
        
        # 计算角度（弧度）
        angle_rad = math.acos(max(-1, min(1, abs(dot_product))))
        
        # 转换为角度
        angle_deg = math.degrees(angle_rad)
        
        # 检查角度是否在阈值内
        return angle_deg < self.angle_thresh
    
    def _merge_connected_components(self, lines, graph):
        """合并连通分量中的线段（高级版本）"""
        visited = set()
        merged_lines = []
        
        for idx in range(len(lines)):
            if idx in visited:
                continue
                
            # 查找连通分量
            component = self._find_component(idx, graph, visited)
            
            # 合并分量中的线段
            if len(component) > 1:
                # 收集所有端点
                all_points = []
                for line_id in component:
                    all_points.append(lines[line_id].coords[0])
                    all_points.append(lines[line_id].coords[-1])
                
                # 找到最远的两点
                point1, point2 = self._find_farthest_points_advanced(all_points)
                
                # 创建新线段
                merged_lines.append(LineString([point1, point2]))
            else:
                # 保留单个线段
                merged_lines.append(lines[idx])
        
        return merged_lines
    
    def _merge_simple_connected_components(self, lines, graph):
        """合并连通分量中的线段（简化版本）"""
        visited = set()
        merged_lines = []
        
        for idx in range(len(lines)):
            if idx in visited:
                continue
                
            # 查找连通分量
            component = self._find_component(idx, graph, visited)
            
            # 合并分量中的线段
            if len(component) > 1:
                # 收集所有端点
                all_points = []
                for line_id in component:
                    all_points.append(lines[line_id][0])
                    all_points.append(lines[line_id][-1])
                
                # 找到最远的两点
                point1, point2 = self._find_farthest_points_simple(all_points)
                
                # 创建新线段
                merged_lines.append([point1, point2])
            else:
                # 保留单个线段
                merged_lines.append(lines[idx])
        
        return merged_lines
    
    def _find_component(self, start_idx, graph, visited):
        """查找连通分量（BFS）"""
        component = []
        queue = [start_idx]
        
        while queue:
            current = queue.pop(0)
            if current in visited:
                continue
                
            visited.add(current)
            component.append(current)
            
            # 添加相邻线段
            for neighbor in graph.get(current, []):
                if neighbor not in visited:
                    queue.append(neighbor)
        
        return component
    
    def _find_farthest_points_advanced(self, points):
        """找到点集中最远的两点（高级版本）"""
        max_distance = -1
        point1 = None
        point2 = None
        
        # 简单方法：遍历所有点对
        for i in range(len(points)):
            for j in range(i + 1, len(points)):
                dist = Point(points[i]).distance(Point(points[j]))
                if dist > max_distance:
                    max_distance = dist
                    point1 = points[i]
                    point2 = points[j]
        
        return point1, point2
    
    def _find_farthest_points_simple(self, points):
        """找到点集中最远的两点（简化版本）"""
        max_distance = -1
        point1 = None
        point2 = None
        
        # 简单方法：遍历所有点对
        for i in range(len(points)):
            for j in range(i + 1, len(points)):
                dist = math.sqrt((points[i][0] - points[j][0])**2 + 
                               (points[i][1] - points[j][1])**2)
                if dist > max_distance:
                    max_distance = dist
                    point1 = points[i]
                    point2 = points[j]
        
        return point1, point2
    
    def print_stats(self):
        """打印处理统计信息"""
        print("\n📊 线段合并统计:")
        print(f"  原始线段数量: {self.stats['original_lines']}")
        print(f"  合并线段数量: {self.stats['merged_lines']}")
        print(f"  最终线段数量: {self.stats['final_lines']}")
        print(f"  处理时间: {self.stats['processing_time']:.3f}秒")

        if self.stats['original_lines'] > 0:
            reduction_rate = (self.stats['merged_lines'] / self.stats['original_lines']) * 100
            print(f"  简化率: {reduction_rate:.1f}%")

        # 显示迭代详情
        if self.enable_iterative and 'iterations_performed' in self.stats:
            print(f"  迭代次数: {self.stats['iterations_performed']}")

            if self.stats['iteration_details']:
                print("  迭代详情:")
                for detail in self.stats['iteration_details']:
                    print(f"    迭代 {detail['iteration']}: "
                          f"{detail['input_count']} -> {detail['output_count']} "
                          f"(合并 {detail['merged_count']} 条)")
        elif not self.enable_iterative:
            print("  迭代模式: 已禁用")
    
    def get_stats(self):
        """获取处理统计信息"""
        return self.stats.copy()

class DXFLineMerger:
    """DXF文件线段合并处理器"""

    def __init__(self, distance_threshold=5, angle_threshold=2, enable_iterative=True, max_iterations=3):
        """
        初始化DXF线段合并器

        Args:
            distance_threshold: 端点连接距离阈值（毫米）
            angle_threshold: 平行角度阈值（度）
            enable_iterative: 是否启用迭代合并
            max_iterations: 最大迭代次数
        """
        self.merger = SimpleLineMerger(
            distance_threshold=distance_threshold,
            angle_threshold=angle_threshold,
            enable_iterative=enable_iterative,
            max_iterations=max_iterations
        )
        self.wall_layers = ['0', 'WALL', 'WALLS', '墙体', '墙', 'ARCH']  # 常见墙体图层名
    
    def process_entities(self, entities):
        """
        处理DXF实体，对墙体图层的线条进行合并
        
        Args:
            entities: DXF实体列表
            
        Returns:
            list: 处理后的实体列表
        """
        print("🔧 开始线段合并处理...")
        
        # 分离墙体线条和其他实体
        wall_lines = []
        other_entities = []
        
        for entity in entities:
            if self._is_wall_line(entity):
                wall_lines.append(entity)
            else:
                other_entities.append(entity)
        
        print(f"  识别到墙体线条: {len(wall_lines)} 条")
        print(f"  其他实体: {len(other_entities)} 个")
        
        if not wall_lines:
            print("  未找到墙体线条，跳过合并处理")
            return entities
        
        # 转换为线段格式
        line_segments = self._entities_to_lines(wall_lines)
        
        # 执行合并
        merged_lines = self.merger.merge_lines(line_segments)
        
        # 转换回实体格式
        merged_entities = self._lines_to_entities(merged_lines, wall_lines[0])
        
        # 打印统计信息
        self.merger.print_stats()
        
        # 返回合并后的结果
        return other_entities + merged_entities
    
    def _is_wall_line(self, entity):
        """判断实体是否为墙体线条"""
        # 检查实体类型
        if entity.get('type') not in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
            return False
        
        # 检查图层
        layer = entity.get('layer', '0').upper()
        return any(wall_layer.upper() in layer for wall_layer in self.wall_layers)
    
    def _entities_to_lines(self, entities):
        """将DXF实体转换为线段"""
        lines = []

        for entity in entities:
            entity_type = entity.get('type')

            if entity_type == 'LINE':
                # 方法1: 尝试扁平化坐标格式 (传统格式)
                if all(key in entity for key in ['start_x', 'start_y', 'end_x', 'end_y']):
                    start = (entity['start_x'], entity['start_y'])
                    end = (entity['end_x'], entity['end_y'])
                    lines.append([start, end])

                # 方法2: 尝试points格式 (专业DXF读取器格式)
                elif 'points' in entity:
                    points = entity['points']
                    if len(points) >= 2:
                        start = points[0]
                        end = points[-1]
                        lines.append([start, end])

                # 方法3: 尝试标准结构格式
                elif 'start' in entity and 'end' in entity:
                    start_dict = entity['start']
                    end_dict = entity['end']
                    if isinstance(start_dict, dict) and isinstance(end_dict, dict):
                        start = (start_dict.get('x', 0), start_dict.get('y', 0))
                        end = (end_dict.get('x', 0), end_dict.get('y', 0))
                        lines.append([start, end])

                # 如果都没有找到有效坐标，跳过这个实体
                else:
                    print(f"⚠️ LINE实体缺少坐标信息: {list(entity.keys())}")
                    continue

            elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                points = entity.get('points', [])
                if len(points) >= 2:
                    # 将多段线分解为多个线段
                    for i in range(len(points) - 1):
                        lines.append([points[i], points[i + 1]])

        return lines
    
    def _lines_to_entities(self, lines, template_entity):
        """将线段转换回DXF实体格式"""
        entities = []
        
        for line in lines:
            if hasattr(line, 'coords'):
                # Shapely LineString
                coords = list(line.coords)
            else:
                # 坐标列表
                coords = line
            
            if len(coords) >= 2:
                # 创建新的LINE实体
                entity = {
                    'type': 'LINE',
                    'layer': template_entity.get('layer', '0'),
                    'start_x': coords[0][0],
                    'start_y': coords[0][1],
                    'end_x': coords[-1][0],
                    'end_y': coords[-1][1],
                    'color': template_entity.get('color', 7),
                    'linetype': template_entity.get('linetype', 'CONTINUOUS'),
                    'merged': True  # 标记为合并后的线段
                }
                entities.append(entity)
        
        return entities
    
    def get_stats(self):
        """获取处理统计信息"""
        return self.merger.get_stats()
