#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试真实场景下的可视化问题
模拟实际使用中的数据结构和调用方式
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_real_data_structure():
    """测试真实的数据结构"""
    print("🔍 测试真实数据结构...")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        from cad_visualizer import CADVisualizer
        
        # 创建处理器和可视化器
        processor = EnhancedCADProcessor(None, None)
        visualizer = CADVisualizer()
        
        # 模拟真实的实体数据结构（基于DXF文件解析）
        real_entities = [
            # 墙体实体
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start': [0, 0],
                'end': [100, 0],
                'points': [(0, 0), (100, 0)],
                'color': 7,
                'handle': 'wall_1'
            },
            {
                'type': 'LINE', 
                'layer': 'A-WALL',
                'start': [100, 0],
                'end': [100, 100],
                'points': [(100, 0), (100, 100)],
                'color': 7,
                'handle': 'wall_2'
            },
            # 门窗实体
            {
                'type': 'LINE',
                'layer': 'A-DOOR',
                'start': [50, 0],
                'end': [50, 50],
                'points': [(50, 0), (50, 50)],
                'color': 3,
                'handle': 'door_1'
            },
            {
                'type': 'LINE',
                'layer': 'A-WINDOW', 
                'start': [0, 50],
                'end': [50, 50],
                'points': [(0, 50), (50, 50)],
                'color': 4,
                'handle': 'window_1'
            }
        ]
        
        print(f"📋 真实实体数据: {len(real_entities)} 个")
        for i, entity in enumerate(real_entities):
            print(f"  实体{i+1}: {entity['type']} - {entity['layer']} - handle:{entity.get('handle')}")
        
        # 模拟分组后的数据
        groups = [
            [real_entities[0], real_entities[1]],  # 墙体组
            [real_entities[2]],                    # 门组
            [real_entities[3]]                     # 窗组
        ]
        
        print(f"📊 分组结果: {len(groups)} 个组")
        for i, group in enumerate(groups):
            print(f"  组{i+1}: {len(group)} 个实体")
        
        # 模拟标注后的数据
        labeled_entities = [real_entities[0]]  # 第一个墙体实体已标注
        labeled_entities[0]['label'] = 'wall'
        labeled_entities[0]['auto_labeled'] = False
        
        current_group = groups[1]  # 当前正在处理门组
        
        print(f"📝 标注状态:")
        print(f"  已标注实体: {len(labeled_entities)} 个")
        print(f"  当前组: {len(current_group)} 个实体")
        
        # 测试数据清理
        print("\n🔧 测试数据清理...")
        cleaned_current_group = processor._clean_group_data(current_group)
        print(f"  清理前: {len(current_group)} 个")
        print(f"  清理后: {len(cleaned_current_group)} 个")
        
        # 测试可视化
        print("\n🎨 测试可视化...")
        
        # 1. 测试实体组可视化
        print("  测试实体组可视化...")
        visualizer.visualize_entity_group(cleaned_current_group, {'door': '门'})
        
        # 2. 测试全图概览可视化
        print("  测试全图概览可视化...")
        visualizer.visualize_overview(
            real_entities,
            cleaned_current_group,
            labeled_entities,
            processor=processor
        )
        
        # 检查可视化结果
        print("\n📊 检查可视化结果...")
        
        # 检查详细视图
        if hasattr(visualizer, 'ax_detail') and visualizer.ax_detail:
            detail_children = visualizer.ax_detail.get_children()
            detail_lines = [child for child in detail_children if hasattr(child, 'get_data')]
            print(f"  详细视图: {len(detail_lines)} 个线条对象")
        
        # 检查概览视图
        if hasattr(visualizer, 'ax_overview') and visualizer.ax_overview:
            overview_children = visualizer.ax_overview.get_children()
            overview_lines = [child for child in overview_children if hasattr(child, 'get_data')]
            print(f"  概览视图: {len(overview_lines)} 个线条对象")
            
            # 检查颜色分布
            colors_used = set()
            for line_obj in overview_lines:
                if hasattr(line_obj, 'get_color'):
                    color = line_obj.get_color()
                    colors_used.add(str(color))
            
            print(f"  使用的颜色: {len(colors_used)} 种")
            for color in sorted(colors_used):
                print(f"    - {color}")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实数据结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_assignment():
    """测试颜色分配逻辑"""
    print("\n🎨 测试颜色分配逻辑...")
    
    try:
        from cad_visualizer import CADVisualizer
        
        visualizer = CADVisualizer()
        
        # 测试不同状态的实体
        test_entities = [
            # 未标注实体
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'points': [(0, 0), (100, 0)]
            },
            # 已标注实体
            {
                'type': 'LINE',
                'layer': 'A-WALL', 
                'points': [(100, 0), (100, 100)],
                'label': 'wall',
                'auto_labeled': False
            },
            # 自动标注实体
            {
                'type': 'LINE',
                'layer': 'A-DOOR',
                'points': [(50, 0), (50, 50)],
                'label': 'door',
                'auto_labeled': True
            },
            # 门窗实体（应该显示为灰色）
            {
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'points': [(0, 50), (50, 50)]
            }
        ]
        
        print(f"📋 测试实体: {len(test_entities)} 个")
        
        # 测试每个实体的颜色分配
        for i, entity in enumerate(test_entities):
            print(f"\n  实体{i+1}: {entity.get('layer', 'unknown')}")
            print(f"    标签: {entity.get('label', 'None')}")
            print(f"    自动标注: {entity.get('auto_labeled', False)}")
            
            # 测试颜色分配
            color, alpha, linewidth, status = visualizer._get_entity_display_info_enhanced(
                entity, 
                labeled_entities=[test_entities[1]],  # 第二个实体已标注
                current_group_entities=[test_entities[2]],  # 第三个实体是当前组
                all_groups=None,
                groups_info=None,
                processor=None
            )
            
            print(f"    结果: 颜色={color}, 透明度={alpha}, 线宽={linewidth}, 状态={status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 颜色分配测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_entity_matching():
    """测试实体匹配逻辑"""
    print("\n🔍 测试实体匹配逻辑...")
    
    try:
        from cad_visualizer import CADVisualizer
        
        visualizer = CADVisualizer()
        
        # 创建测试实体
        entity1 = {
            'type': 'LINE',
            'layer': 'A-WALL',
            'points': [(0, 0), (100, 0)],
            'handle': 'wall_1'
        }
        
        entity2 = {
            'type': 'LINE',
            'layer': 'A-WALL',
            'points': [(0, 0), (100, 0)],
            'handle': 'wall_1'
        }
        
        entity3 = {
            'type': 'LINE',
            'layer': 'A-DOOR',
            'points': [(50, 0), (50, 50)],
            'handle': 'door_1'
        }
        
        # 测试实体匹配方法
        print("📋 测试实体匹配:")
        
        if hasattr(visualizer, '_is_entity_in_group_safe'):
            result1 = visualizer._is_entity_in_group_safe(entity1, [entity2, entity3])
            print(f"  entity1 在 [entity2, entity3] 中: {result1}")
            
            result2 = visualizer._is_entity_in_group_safe(entity1, [entity3])
            print(f"  entity1 在 [entity3] 中: {result2}")
        else:
            print("  ⚠️ _is_entity_in_group_safe 方法不存在")
        
        if hasattr(visualizer, '_is_entity_in_labeled_list'):
            result3 = visualizer._is_entity_in_labeled_list(entity1, [entity2])
            print(f"  entity1 在已标注列表 [entity2] 中: {result3}")
            
            result4 = visualizer._is_entity_in_labeled_list(entity1, [entity3])
            print(f"  entity1 在已标注列表 [entity3] 中: {result4}")
        else:
            print("  ⚠️ _is_entity_in_labeled_list 方法不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 实体匹配测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_display_issue():
    """分析显示问题"""
    print("\n🔍 分析显示问题...")
    
    print("📋 可能的问题原因:")
    print("  1. 实体数据结构不匹配 - 可视化器期望的字段与实际数据不符")
    print("  2. 颜色映射问题 - 某些实体被分配了透明或不可见的颜色")
    print("  3. 坐标范围问题 - 实体坐标超出显示范围")
    print("  4. 数据清理过度 - 有效实体被错误过滤")
    print("  5. 图层过滤问题 - 某些图层的实体被隐藏")
    print("  6. 画布更新问题 - 绘制完成但画布未刷新")
    
    print("\n🔧 建议的检查步骤:")
    print("  1. 检查实际传递给可视化器的数据结构")
    print("  2. 验证颜色分配逻辑是否正确")
    print("  3. 确认坐标范围计算是否合理")
    print("  4. 检查数据清理是否过滤了有效实体")
    print("  5. 验证画布更新机制")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("CAD分类标注工具 - 真实场景可视化测试")
    print("=" * 60)
    
    tests = [
        ("真实数据结构测试", test_real_data_structure),
        ("颜色分配逻辑测试", test_color_assignment),
        ("实体匹配逻辑测试", test_entity_matching),
        ("显示问题分析", analyze_display_issue)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    failed_tests = [name for name, success in results if not success]
    
    if failed_tests:
        print(f"\n⚠️ 失败的测试: {len(failed_tests)} 个")
        print("需要进一步调查这些问题")
    else:
        print("\n✅ 所有测试通过")
        print("问题可能在于实际使用环境与测试环境的差异")
    
    print("=" * 60)
