#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
实时追踪处理器重置问题
在主程序中插入监控代码来追踪处理器重置的根源
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def add_processor_tracking():
    """添加处理器追踪代码"""
    print("🔧 添加处理器追踪代码")
    print("="*60)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. 在类初始化中添加追踪变量
        print("1. 添加追踪变量...")
        
        init_tracking = '''        
        # 🔍 处理器重置追踪
        self._processor_reset_count = 0
        self._processor_reset_history = []
        self._last_processor_state = None
        print("🔍 处理器重置追踪已启动")
'''
        
        # 查找合适的插入位置（在父类初始化之前）
        insert_pos = content.find('        # 调用父类初始化')
        if insert_pos != -1:
            content = content[:insert_pos] + init_tracking + content[insert_pos:]
            print("  ✅ 追踪变量已添加")
        
        # 2. 修改处理器重置检测
        print("2. 修改处理器重置检测...")
        
        old_reset_code = '''            if not self.processor:
                # 🔍 详细记录处理器重置事件
                import time
                timestamp = time.strftime("%H:%M:%S", time.localtime())
                if not hasattr(self, '_processor_creation_count'):
                    self._processor_creation_count = 0
                self._processor_creation_count += 1
                
                print(f"🔍 [{timestamp}] 处理器重置事件 #{self._processor_creation_count}")
                print(f"   触发位置: _load_from_cache")
                print(f"   当前文件: {getattr(self, 'current_file', '未知')}")
                print(f"   缓存状态: {len(getattr(self, 'file_cache', {})) if hasattr(self, 'file_cache') else '未知'}")
                
                # 打印调用栈以追踪根源
                import traceback
                print(f"   调用栈:")
                for line in traceback.format_stack()[-5:-1]:
                    print(f"     {line.strip()}")
                
                print("  ⚠️ 处理器不存在，创建新的处理器")'''
        
        new_reset_code = '''            if not self.processor:
                # 🔍 详细追踪处理器重置
                self._track_processor_reset("_load_from_cache")
                print("  ⚠️ 处理器不存在，创建新的处理器")'''
        
        if old_reset_code in content:
            content = content.replace(old_reset_code, new_reset_code)
            print("  ✅ 处理器重置检测已修改")
        
        # 3. 添加追踪方法
        print("3. 添加追踪方法...")
        
        tracking_methods = '''
    def _track_processor_reset(self, location):
        """追踪处理器重置事件"""
        import time
        import traceback
        
        self._processor_reset_count += 1
        timestamp = time.strftime("%H:%M:%S.%f", time.localtime())[:-3]  # 包含毫秒
        
        # 获取调用栈
        stack = traceback.extract_stack()
        caller_info = []
        for frame in stack[-6:-2]:  # 获取最近几层调用
            caller_info.append(f"{frame.filename}:{frame.lineno} in {frame.name}")
        
        # 记录重置事件
        reset_event = {
            'count': self._processor_reset_count,
            'timestamp': timestamp,
            'location': location,
            'current_file': getattr(self, 'current_file', None),
            'display_file': getattr(self, 'display_file', None),
            'cache_size': len(getattr(self, 'file_cache', {})),
            'caller_stack': caller_info
        }
        
        self._processor_reset_history.append(reset_event)
        
        # 打印详细信息
        print(f"🔍 [{timestamp}] 处理器重置 #{self._processor_reset_count}")
        print(f"   📍 触发位置: {location}")
        print(f"   📁 当前文件: {reset_event['current_file']}")
        print(f"   🖥️ 显示文件: {reset_event['display_file']}")
        print(f"   💾 缓存大小: {reset_event['cache_size']}")
        print(f"   📞 调用路径:")
        for i, caller in enumerate(caller_info):
            print(f"     {i+1}. {caller}")
        
        # 分析重置模式
        if len(self._processor_reset_history) > 1:
            self._analyze_reset_pattern()
    
    def _analyze_reset_pattern(self):
        """分析处理器重置模式"""
        if len(self._processor_reset_history) < 2:
            return
        
        current = self._processor_reset_history[-1]
        previous = self._processor_reset_history[-2]
        
        # 计算时间间隔
        try:
            current_time = time.strptime(current['timestamp'], "%H:%M:%S.%f")
            previous_time = time.strptime(previous['timestamp'], "%H:%M:%S.%f")
            time_diff = (current_time.tm_hour * 3600 + current_time.tm_min * 60 + current_time.tm_sec) - \\
                       (previous_time.tm_hour * 3600 + previous_time.tm_min * 60 + previous_time.tm_sec)
        except:
            time_diff = 0
        
        print(f"   ⏱️ 距离上次重置: {time_diff}秒")
        
        # 检查是否是相同位置触发
        if current['location'] == previous['location']:
            print(f"   🔄 重复位置触发: {current['location']}")
        
        # 检查文件切换
        if current['current_file'] != previous['current_file']:
            print(f"   📂 文件切换: {previous['current_file']} -> {current['current_file']}")
        
        # 检查频繁重置
        if time_diff < 5:
            print(f"   ⚠️ 频繁重置警告: {time_diff}秒内重置")
        
        # 检查调用栈变化
        if current['caller_stack'] != previous['caller_stack']:
            print(f"   📞 调用路径变化:")
            print(f"     上次: {previous['caller_stack'][-1] if previous['caller_stack'] else '无'}")
            print(f"     本次: {current['caller_stack'][-1] if current['caller_stack'] else '无'}")

    def _get_processor_reset_summary(self):
        """获取处理器重置摘要"""
        if not hasattr(self, '_processor_reset_history'):
            return "无重置记录"
        
        total_resets = len(self._processor_reset_history)
        if total_resets == 0:
            return "无重置记录"
        
        # 统计触发位置
        locations = {}
        for event in self._processor_reset_history:
            loc = event['location']
            locations[loc] = locations.get(loc, 0) + 1
        
        # 统计文件切换
        file_switches = 0
        for i in range(1, len(self._processor_reset_history)):
            if self._processor_reset_history[i]['current_file'] != self._processor_reset_history[i-1]['current_file']:
                file_switches += 1
        
        summary = f"总重置次数: {total_resets}, 文件切换: {file_switches}, 主要触发位置: {max(locations, key=locations.get) if locations else '无'}"
        return summary

'''
        
        # 在类的末尾添加追踪方法
        class_end = content.rfind('if __name__ == "__main__":')
        if class_end != -1:
            content = content[:class_end] + tracking_methods + '\n' + content[class_end:]
            print("  ✅ 追踪方法已添加")
        
        # 4. 在其他处理器检查位置添加追踪
        print("4. 添加其他位置的追踪...")
        
        # 在组列表更新中添加追踪
        other_checks = [
            ('update_group_list', '组列表更新'),
            ('_update_group_list_enhanced', '增强组列表更新'),
            ('_update_group_list_enhanced_v2', '增强组列表更新V2')
        ]
        
        for method_name, description in other_checks:
            old_check = f'''        if not self.processor:
            print("⚠️ 没有处理器，'''
            
            new_check = f'''        if not self.processor:
            self._track_processor_reset("{method_name}")
            print("⚠️ 没有处理器，'''
            
            if old_check in content:
                content = content.replace(old_check, new_check)
                print(f"  ✅ 已添加 {description} 的追踪")
        
        # 保存修改后的文件
        with open('main_enhanced_with_v2_fill.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 处理器追踪代码添加完成")
        return True
        
    except Exception as e:
        print(f"❌ 添加追踪代码失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_scenario():
    """创建测试场景"""
    print(f"\n🧪 创建测试场景")
    print("="*60)
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试处理器重置场景
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_processor_reset_scenarios():
    """测试各种可能导致处理器重置的场景"""
    print("🧪 测试处理器重置场景")
    print("="*60)
    
    scenarios = [
        "1. 启动程序后立即检查处理器状态",
        "2. 选择文件后检查处理器状态", 
        "3. 切换文件时检查处理器状态",
        "4. 更新组列表时检查处理器状态",
        "5. 点击组项目时检查处理器状态"
    ]
    
    for scenario in scenarios:
        print(f"📋 {scenario}")
    
    print(f"\\n💡 测试方法:")
    print(f"   1. 运行主程序: python main_enhanced_with_v2_fill.py")
    print(f"   2. 按照上述场景操作")
    print(f"   3. 观察控制台输出的详细追踪信息")
    print(f"   4. 记录每次重置的触发条件和调用栈")
    print(f"   5. 分析重置模式，找出根本原因")

if __name__ == "__main__":
    test_processor_reset_scenarios()
'''
    
    with open('test_processor_reset_scenarios.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 测试场景脚本已创建: test_processor_reset_scenarios.py")

def main():
    """主函数"""
    print("🚀 开始添加处理器重置追踪")
    
    try:
        # 1. 添加处理器追踪代码
        tracking_success = add_processor_tracking()
        
        # 2. 创建测试场景
        create_test_scenario()
        
        print(f"\n" + "="*60)
        print(f"📊 追踪代码添加结果:")
        print(f"  处理器追踪代码: {'✅ 成功' if tracking_success else '❌ 失败'}")
        print(f"  测试场景脚本: ✅ 已创建")
        
        if tracking_success:
            print(f"\n🎯 下一步操作:")
            print(f"   1. 运行主程序: python main_enhanced_with_v2_fill.py")
            print(f"   2. 进行正常操作（选择文件、查看组等）")
            print(f"   3. 观察控制台的详细追踪信息")
            print(f"   4. 记录处理器重置的具体触发条件")
            print(f"   5. 根据追踪结果分析根本原因")
            
            print(f"\n🔍 关键追踪信息:")
            print(f"   - 🔍 [时间戳] 处理器重置 #次数")
            print(f"   - 📍 触发位置: 具体方法名")
            print(f"   - 📁 当前文件: 文件路径")
            print(f"   - 📞 调用路径: 完整调用栈")
            print(f"   - ⏱️ 距离上次重置: 时间间隔")
            print(f"   - 🔄 重复位置触发: 是否同一位置")
            
            print(f"\n⚠️ 注意观察:")
            print(f"   - 处理器重置是否与文件切换相关")
            print(f"   - 是否在特定操作后频繁重置")
            print(f"   - 调用栈中是否有异常的方法调用")
            print(f"   - 重置时机是否与界面更新相关")
        else:
            print(f"\n❌ 追踪代码添加失败，请检查错误信息")
        
    except Exception as e:
        print(f"❌ 追踪添加过程失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
