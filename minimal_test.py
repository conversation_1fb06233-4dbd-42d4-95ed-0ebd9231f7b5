#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("开始测试...")

try:
    # 直接测试哈希函数
    entity = {
        'type': 'LINE',
        'layer': 'A-WALL',
        'start_x': 100.0,
        'start_y': 200.0,
        'end_x': 300.0,
        'end_y': 400.0
    }
    
    # 手动实现哈希逻辑
    start_point = (float(entity['start_x']), float(entity['start_y']))
    end_point = (float(entity['end_x']), float(entity['end_y']))
    sorted_points = sorted([start_point, end_point])
    hash_value = f"LINE_{entity['layer']}_{sorted_points}"
    
    print(f"手动哈希: {hash_value}")
    
    # 测试导入
    from cad_data_processor import CADDataProcessor
    processor = CADDataProcessor()
    
    actual_hash = processor._get_entity_hash(entity)
    print(f"实际哈希: {actual_hash}")
    
    if hash_value == actual_hash:
        print("✅ 哈希修复成功！")
    else:
        print("❌ 哈希不匹配")
        
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()

print("测试完成")
