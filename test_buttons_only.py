#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试三阶段按钮界面（简化版）
"""

import tkinter as tk
from tkinter import Frame, Label, Button

class TestThreeStageUI:
    """测试三阶段处理界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("三阶段处理测试")
        self.root.geometry("600x400")
        
        # 处理阶段状态
        self.processing_stage = "none"  # none, basic, line, group, complete
        
        self.create_widgets()
    
    def create_widgets(self):
        """创建UI组件"""
        # 主框架
        main_frame = Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 处理控制区域
        self._create_process_control(main_frame)
        
        # 状态显示
        self.status_label = Label(main_frame, text="准备就绪", font=('Arial', 12))
        self.status_label.pack(pady=20)
    
    def _create_process_control(self, parent):
        """创建处理控制区域"""
        frame = Frame(parent)
        frame.pack(fill='x', pady=(0, 15))
        
        Label(frame, text="处理控制:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        # 第一行：三阶段处理按钮
        stage_frame = Frame(frame)
        stage_frame.pack(fill='x', pady=(5, 0))
        
        self.start_btn = Button(stage_frame, text="开始处理", command=self.start_processing,
                               bg='#2196F3', fg='white', font=('Arial', 9))
        self.start_btn.pack(side='left', fill='x', expand=True, padx=(0, 2))
        
        self.line_process_btn = Button(stage_frame, text="线条处理", command=self.start_line_processing,
                                     bg='#4CAF50', fg='white', font=('Arial', 9), state='disabled')
        self.line_process_btn.pack(side='left', fill='x', expand=True, padx=(2, 2))
        
        self.group_process_btn = Button(stage_frame, text="识别分组", command=self.start_group_processing,
                                      bg='#FF9800', fg='white', font=('Arial', 9), state='disabled')
        self.group_process_btn.pack(side='left', fill='x', expand=True, padx=(2, 0))

        # 第二行：控制按钮
        control_frame = Frame(frame)
        control_frame.pack(fill='x', pady=(5, 0))

        self.stop_btn = Button(control_frame, text="停止", command=self.stop_processing, state='disabled',
                              bg='#F44336', fg='white', font=('Arial', 9))
        self.stop_btn.pack(side='left', fill='x', expand=True, padx=(0, 2))

        # 重置按钮
        self.reset_btn = Button(control_frame, text="重置", command=self.reset_processing,
                               bg='#9C27B0', fg='white', font=('Arial', 9))
        self.reset_btn.pack(side='left', fill='x', expand=True, padx=(2, 0))
    
    def start_processing(self):
        """开始处理 - 第一阶段"""
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.processing_stage = "basic"
        self.status_label.config(text="阶段1：基础数据加载完成")
        
        # 模拟处理完成，启用下一阶段
        self.root.after(1000, self._complete_basic_stage)
    
    def _complete_basic_stage(self):
        """完成基础阶段"""
        self.line_process_btn.config(state='normal')
        self.status_label.config(text="基础数据加载完成，可进行线条处理")
    
    def start_line_processing(self):
        """开始线条处理 - 第二阶段"""
        if self.processing_stage != "basic":
            self.status_label.config(text="请先完成基础数据加载")
            return
        
        self.line_process_btn.config(state='disabled')
        self.processing_stage = "line"
        self.status_label.config(text="阶段2：线条处理完成")
        
        # 模拟处理完成，启用下一阶段
        self.root.after(1000, self._complete_line_stage)
    
    def _complete_line_stage(self):
        """完成线条处理阶段"""
        self.group_process_btn.config(state='normal')
        self.status_label.config(text="线条处理完成，可进行识别分组")
    
    def start_group_processing(self):
        """开始识别分组 - 第三阶段"""
        if self.processing_stage != "line":
            self.status_label.config(text="请先完成线条处理")
            return
        
        self.group_process_btn.config(state='disabled')
        self.processing_stage = "group"
        self.status_label.config(text="阶段3：识别分组完成")
        
        # 模拟处理完成
        self.root.after(1000, self._complete_group_stage)
    
    def _complete_group_stage(self):
        """完成分组阶段"""
        self.processing_stage = "complete"
        self.status_label.config(text="识别分组完成，可进行填充、房间识别等操作")
        self.stop_btn.config(state='disabled')
    
    def stop_processing(self):
        """停止处理"""
        self.reset_processing()
        self.status_label.config(text="处理已停止")
    
    def reset_processing(self):
        """重置处理状态"""
        self.start_btn.config(state='normal')
        self.line_process_btn.config(state='disabled')
        self.group_process_btn.config(state='disabled')
        self.stop_btn.config(state='disabled')
        self.processing_stage = "none"
        self.status_label.config(text="准备就绪")

def main():
    """主函数"""
    root = tk.Tk()
    app = TestThreeStageUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
