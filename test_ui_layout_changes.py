#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试界面布局修改效果
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class UILayoutTest:
    def __init__(self, root):
        self.root = root
        self.root.title("界面布局修改测试")
        self.root.geometry("1200x800")
        
        # 创建主框架
        main_frame = tk.Frame(root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 标题
        title_label = tk.Label(main_frame, text="界面布局修改效果预览", 
                              font=('Arial', 14, 'bold'), bg='#f0f0f0')
        title_label.pack(pady=(0, 10))
        
        # 创建三个测试区域
        self._create_test_areas(main_frame)
        
    def _create_test_areas(self, parent):
        """创建三个测试区域"""
        
        # 1. 图层控制区域测试
        layer_frame = tk.LabelFrame(parent, text="1. 图层控制区域（修改后）", 
                                   font=('Arial', 10, 'bold'), bg='#E6F3FF')
        layer_frame.pack(fill='x', pady=(0, 10))
        
        self._create_layer_control_test(layer_frame)
        
        # 2. 配色系统测试
        color_frame = tk.LabelFrame(parent, text="2. 配色系统（修改后）", 
                                   font=('Arial', 10, 'bold'), bg='#FFF8DC')
        color_frame.pack(fill='x', pady=(0, 10))
        
        self._create_color_system_test(color_frame)
        
        # 3. 房间识别区域测试
        room_frame = tk.LabelFrame(parent, text="3. 房间识别区域（修改后）", 
                                  font=('Arial', 10, 'bold'), bg='#F0FFF0')
        room_frame.pack(fill='both', expand=True)
        
        self._create_room_recognition_test(room_frame)
        
    def _create_layer_control_test(self, parent):
        """测试图层控制区域"""
        # 模拟图层控制区域布局
        container = tk.Frame(parent, bg='#E6F3FF')
        container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 图层列表（增加高度）
        layer_list_frame = tk.Frame(container)
        layer_list_frame.pack(fill='both', expand=True, padx=3, pady=5)
        
        # 四个图层项，增加高度
        layer_items = [
            ('CAD线条', '#2196F3'),
            ('墙体填充', '#4CAF50'),
            ('家具填充', '#FF9800'),
            ('房间填充', '#9C27B0')
        ]
        
        for layer_name, color in layer_items:
            # 图层项容器 - 增加高度
            item_frame = tk.Frame(layer_list_frame, relief='ridge', bd=1, bg='#F5F5F5')
            item_frame.pack(fill='x', pady=3)  # 增加垂直间距
            
            # 单行水平布局 - 增加内边距
            main_row = tk.Frame(item_frame)
            main_row.pack(fill='x', padx=5, pady=8)  # 增加内边距让文字更清楚
            
            # 圆形按钮
            canvas = tk.Canvas(main_row, width=20, height=20, highlightthickness=0)
            canvas.pack(side='left', padx=(0, 5))
            canvas.create_oval(2, 2, 18, 18, fill='#4CAF50', outline='#333333', width=1)
            
            # 颜色指示器
            color_label = tk.Label(main_row, text="●", fg=color, font=('Arial', 8, 'bold'))
            color_label.pack(side='left', padx=(3, 2))
            
            # 图层名称
            name_label = tk.Label(main_row, text=layer_name, font=('Arial', 7),
                                anchor='w', width=8)
            name_label.pack(side='left', padx=(0, 3))
            
            # 上下移动按钮
            up_btn = tk.Button(main_row, text="↑", font=('Arial', 6), width=2, height=1)
            up_btn.pack(side='left', padx=1)
            
            down_btn = tk.Button(main_row, text="↓", font=('Arial', 6), width=2, height=1)
            down_btn.pack(side='left', padx=1)
        
        # 应用按钮放在底部，与缩放查看按钮等大
        apply_frame = tk.Frame(container)
        apply_frame.pack(side='bottom', fill='x', padx=3, pady=5)
        
        apply_btn = tk.Button(apply_frame, text="应用图层设置",
                            bg='#FF5722', fg='white', 
                            font=('Arial', 9, 'bold'),
                            width=8, height=3,
                            relief='raised', bd=2)
        apply_btn.pack()
        
    def _create_color_system_test(self, parent):
        """测试配色系统"""
        container = tk.Frame(parent, bg='#FFF8DC')
        container.pack(fill='x', padx=10, pady=10)
        
        # 第一行：配色方案选择
        scheme_frame = tk.Frame(container, bg='#FFF8DC')
        scheme_frame.pack(fill='x', pady=(0, 5))
        
        tk.Label(scheme_frame, text="配色方案:", font=('Arial', 8), bg='#FFF8DC').pack(side='left')
        
        scheme_combo = ttk.Combobox(scheme_frame, width=12, state='readonly')
        scheme_combo.pack(side='left', padx=(5, 2))
        
        apply_color_btn = tk.Button(scheme_frame, text="应用配色",
                                  bg='#4CAF50', fg='white', font=('Arial', 8))
        apply_color_btn.pack(side='left', padx=2)
        
        # 第二行：四个配色管理按钮 - 与缩放查看按钮等大并平齐
        btn_frame = tk.Frame(container, bg='#FFF8DC')
        btn_frame.pack(fill='x', pady=10)  # 增加垂直间距与缩放按钮平齐
        
        buttons = [
            ("配色设置", '#9C27B0'),
            ("保存配色", '#607D8B'),
            ("导出文件", '#FF9800'),
            ("加载配色", '#795548')
        ]
        
        for text, color in buttons:
            btn = tk.Button(btn_frame, text=text, bg=color, fg='white',
                          font=('Arial', 9, 'bold'),
                          width=8, height=3, relief='raised', bd=2)
            btn.pack(side='left', fill='x', expand=True, padx=1)
            
    def _create_room_recognition_test(self, parent):
        """测试房间识别区域"""
        container = tk.Frame(parent, bg='#F0FFF0')
        container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 第一行：自动房间识别按钮（红框，占满宽度）
        auto_frame = tk.Frame(container, relief='ridge', bd=2, bg='#FFE4B5')
        auto_frame.pack(fill='x', pady=(0, 5))
        
        auto_btn = tk.Button(auto_frame, text="🤖 自动房间识别",
                           bg='#FFE4B5', font=('Arial', 9, 'bold'),
                           relief='raised', bd=2)
        auto_btn.pack(fill='x', padx=5, pady=5)
        
        # 第二行：三个功能按钮水平排列（红框）
        func_frame = tk.Frame(container, relief='ridge', bd=2, bg='#E6F3FF')
        func_frame.pack(fill='x', pady=(0, 5))
        
        func_buttons = [
            ("识别外轮廓", '#E6F3FF'),
            ("识别房间", '#F0FFF0'),
            ("房间切分", '#FFF8DC')
        ]
        
        for text, color in func_buttons:
            btn = tk.Button(func_frame, text=text, bg=color, font=('Arial', 8))
            btn.pack(side='left', fill='x', expand=True, padx=2, pady=5)
        
        # 第三行：房间类型修改区（红框）
        type_frame = tk.Frame(container, relief='ridge', bd=2, bg='#FFF0F5')
        type_frame.pack(fill='x', pady=(0, 5))
        
        type_content = tk.Frame(type_frame, bg='#FFF0F5')
        type_content.pack(fill='x', padx=5, pady=5)
        
        # 标签
        type_label = tk.Label(type_content, text="房间类型修改:",
                            font=('Arial', 9, 'bold'), bg='#FFF0F5')
        type_label.pack(anchor='w', pady=(0, 2))
        
        # 下拉框和按钮的水平布局
        type_control_frame = tk.Frame(type_content, bg='#FFF0F5')
        type_control_frame.pack(fill='x')
        
        # 下拉框（左侧红框）
        combo_frame = tk.Frame(type_control_frame, relief='ridge', bd=1, bg='white')
        combo_frame.pack(side='left', padx=(0, 10))
        
        room_combo = ttk.Combobox(combo_frame, width=15, state='readonly')
        room_combo.pack(padx=5, pady=5)
        
        # 应用按钮（右侧红框）
        apply_frame = tk.Frame(type_control_frame, relief='ridge', bd=1, bg='#FFE4E1')
        apply_frame.pack(side='left')
        
        apply_btn = tk.Button(apply_frame, text="应用修改",
                            bg='#FFE4E1', font=('Arial', 8))
        apply_btn.pack(padx=5, pady=5)
        
        # 第四行：两个空的红框（占位）
        empty_frame = tk.Frame(container)
        empty_frame.pack(fill='x', pady=(0, 5))
        
        # 左侧空红框
        empty_left = tk.Frame(empty_frame, relief='ridge', bd=2, bg='#F5F5F5', height=40)
        empty_left.pack(side='left', fill='both', expand=True, padx=(0, 2))
        empty_left.pack_propagate(False)
        
        left_label = tk.Label(empty_left, text="预留区域1", bg='#F5F5F5', fg='gray')
        left_label.pack(expand=True)
        
        # 右侧空红框
        empty_right = tk.Frame(empty_frame, relief='ridge', bd=2, bg='#F5F5F5', height=40)
        empty_right.pack(side='right', fill='both', expand=True, padx=(2, 0))
        empty_right.pack_propagate(False)
        
        right_label = tk.Label(empty_right, text="预留区域2", bg='#F5F5F5', fg='gray')
        right_label.pack(expand=True)

def main():
    root = tk.Tk()
    app = UILayoutTest(root)
    root.mainloop()

if __name__ == "__main__":
    main()
