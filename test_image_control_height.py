#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试图像控制界面高度调整
验证所有按钮是否能正确显示
"""

import os
import sys
import tkinter as tk
from tkinter import ttk

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_image_control_height():
    """测试图像控制界面高度"""
    print("🧪 测试图像控制界面高度调整")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("图像控制界面高度测试")
        root.geometry("1200x800")  # 设置较大的窗口尺寸
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        # 等待界面完全加载
        root.update()
        
        # 检查图像控制区域是否存在
        if not hasattr(app, 'zoom_frame'):
            print("❌ 图像控制区域不存在")
            return False
        
        # 检查图像控制区域的高度
        zoom_frame_height = app.zoom_frame.winfo_height()
        print(f"📏 图像控制区域高度: {zoom_frame_height}px")
        
        # 检查左侧图层控制容器
        if hasattr(app, 'layer_control_container'):
            layer_height = app.layer_control_container.winfo_height()
            print(f"📏 图层控制容器高度: {layer_height}px")
        
        # 检查右侧缩放按钮容器
        if hasattr(app, 'zoom_buttons_container'):
            buttons_height = app.zoom_buttons_container.winfo_height()
            print(f"📏 缩放按钮容器高度: {buttons_height}px")
        
        # 检查图层列表框架
        if hasattr(app, 'layer_list_frame'):
            list_height = app.layer_list_frame.winfo_height()
            print(f"📏 图层列表框架高度: {list_height}px")
        
        # 检查是否有足够的高度显示所有内容
        min_required_height = 250  # 最小需要的高度
        
        if zoom_frame_height >= min_required_height:
            print(f"✅ 图像控制区域高度足够 ({zoom_frame_height}px >= {min_required_height}px)")
            success = True
        else:
            print(f"❌ 图像控制区域高度不足 ({zoom_frame_height}px < {min_required_height}px)")
            success = False
        
        # 检查图层项是否都能显示
        if hasattr(app, 'layer_list_frame'):
            layer_items = app.layer_list_frame.winfo_children()
            print(f"📋 图层项数量: {len(layer_items)}")
            
            for i, item in enumerate(layer_items):
                item_height = item.winfo_height()
                item_y = item.winfo_y()
                print(f"  图层项{i+1}: 高度={item_height}px, Y位置={item_y}px")
        
        # 保持窗口打开一段时间以便观察
        print("🔍 窗口将保持打开5秒以便观察...")
        root.after(5000, root.quit)  # 5秒后自动关闭
        root.mainloop()
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_height_demo():
    """创建高度调整演示"""
    print("🎨 创建图像控制高度调整演示")
    
    root = tk.Tk()
    root.title("图像控制界面高度调整演示")
    root.geometry("1000x700")
    
    # 创建主框架
    main_frame = tk.Frame(root)
    main_frame.pack(fill='both', expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(main_frame, text="图像控制界面高度调整演示", 
                          font=('Arial', 14, 'bold'))
    title_label.pack(pady=(0, 10))
    
    # 创建模拟的四区域布局
    container = tk.Frame(main_frame)
    container.pack(fill='both', expand=True)
    
    # 配置网格权重 - 模拟调整后的权重
    container.grid_rowconfigure(0, weight=1)  # 上排权重1
    container.grid_rowconfigure(1, weight=4)  # 下排权重4（增加）
    container.grid_columnconfigure(0, weight=1)
    container.grid_columnconfigure(1, weight=1)
    
    # 区域1：图像预览（左上）
    frame1 = tk.Frame(container, relief='ridge', bd=2, bg='lightblue')
    frame1.grid(row=0, column=0, sticky='nsew', padx=(0, 2), pady=(0, 2))
    tk.Label(frame1, text="1. 图像预览", font=('Arial', 10, 'bold'), bg='lightblue').pack()
    
    # 区域2：填充控制（右上）
    frame2 = tk.Frame(container, relief='ridge', bd=2, bg='lightgreen')
    frame2.grid(row=0, column=1, sticky='nsew', padx=(2, 0), pady=(0, 2))
    tk.Label(frame2, text="2. 填充控制", font=('Arial', 10, 'bold'), bg='lightgreen').pack()
    
    # 区域3：图像控制（左下）- 增加高度
    frame3 = tk.Frame(container, relief='ridge', bd=2, bg='lightcoral')
    frame3.grid(row=1, column=0, sticky='nsew', padx=(0, 2), pady=(2, 0))
    
    # 图像控制区域内容
    tk.Label(frame3, text="3. 图像控制（高度已增加）", 
            font=('Arial', 10, 'bold'), bg='lightcoral').pack(pady=5)
    
    # 左右分割
    control_container = tk.Frame(frame3)
    control_container.pack(fill='both', expand=True, padx=5, pady=5)
    
    # 左侧：图层控制
    left_frame = tk.Frame(control_container, relief='ridge', bd=1, bg='#F0F8FF')
    left_frame.pack(side='left', fill='both', expand=True, padx=(0, 2))
    
    tk.Label(left_frame, text="图层控制", font=('Arial', 9, 'bold'), bg='#F0F8FF').pack()
    
    # 模拟图层项
    layers = ['CAD线条', '墙体填充', '家具填充', '房间填充']
    for layer in layers:
        layer_frame = tk.Frame(left_frame, bg='white', relief='ridge', bd=1)
        layer_frame.pack(fill='x', padx=3, pady=2)
        
        # 图层行
        row = tk.Frame(layer_frame, bg='white')
        row.pack(fill='x', padx=5, pady=5)
        
        # 颜色指示器
        color_canvas = tk.Canvas(row, width=16, height=16, bg='white', highlightthickness=0)
        color_canvas.pack(side='left', padx=(0, 5))
        color_canvas.create_oval(2, 2, 14, 14, fill='blue', outline='black')
        
        # 图层名称
        tk.Label(row, text=layer, font=('Arial', 9), bg='white').pack(side='left', padx=(0, 5))
        
        # 下拉菜单
        combo = ttk.Combobox(row, width=5, values=['显示', '隐藏'], state='readonly')
        combo.set('显示')
        combo.pack(side='left', padx=(0, 5))
        
        # 按钮组
        buttons = ['设置', '编辑', '复制', '删除']
        for btn_text in buttons:
            btn = tk.Button(row, text=btn_text, font=('Arial', 7), width=3, height=1)
            btn.pack(side='left', padx=1)
    
    # 应用按钮
    apply_btn = tk.Button(left_frame, text="⚙️ 应用设置", 
                         bg='#FF5722', fg='white', font=('Arial', 9, 'bold'))
    apply_btn.pack(fill='x', padx=3, pady=5)
    
    # 右侧：视图控制
    right_frame = tk.Frame(control_container, relief='ridge', bd=1, bg='#FFF8DC')
    right_frame.pack(side='right', fill='both', expand=True, padx=(2, 0))
    
    tk.Label(right_frame, text="视图控制", font=('Arial', 9, 'bold'), bg='#FFF8DC').pack()
    
    # 按钮容器
    btn_container = tk.Frame(right_frame, bg='#FFF8DC')
    btn_container.pack(expand=True, fill='both')
    
    # 居中按钮框架
    center_frame = tk.Frame(btn_container, bg='#FFF8DC')
    center_frame.place(relx=0.5, rely=0.5, anchor='center')
    
    # 三个按钮
    buttons_data = [
        ("🔍\n缩放查看", '#FF9800'),
        ("📐\n适应窗口", '#4CAF50'),
        ("🔄\n重置视图", '#2196F3')
    ]
    
    for btn_text, btn_color in buttons_data:
        btn = tk.Button(center_frame, text=btn_text, 
                       font=('Arial', 8, 'bold'), bg=btn_color, fg='white',
                       width=8, height=3, relief='raised', bd=2)
        btn.pack(pady=2)
    
    # 区域4：配色系统（右下）
    frame4 = tk.Frame(container, relief='ridge', bd=2, bg='lightyellow')
    frame4.grid(row=1, column=1, sticky='nsew', padx=(2, 0), pady=(2, 0))
    tk.Label(frame4, text="4. 配色系统", font=('Arial', 10, 'bold'), bg='lightyellow').pack()
    
    # 说明文本
    info_frame = tk.Frame(main_frame)
    info_frame.pack(fill='x', pady=10)
    
    info_text = """
📋 高度调整说明：
• 下排权重从3增加到4，给图像控制区域分配更多高度
• 图层控制容器设置最小高度300px，确保所有按钮可见
• 图层列表框架设置高度250px，确保所有图层项正常显示
• 使用grid_propagate(False)防止子组件影响容器大小
    """
    
    tk.Label(info_frame, text=info_text, font=('Arial', 9), 
            justify='left', bg='#ecf0f1', fg='#2c3e50').pack(fill='x', padx=10, pady=5)
    
    root.mainloop()

def main():
    """主函数"""
    print("🧪 图像控制界面高度调整测试")
    print("=" * 50)
    
    # 选择测试模式
    print("请选择测试模式：")
    print("1. 实际应用测试")
    print("2. 高度调整演示")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            success = test_image_control_height()
            if success:
                print("\n✅ 图像控制界面高度测试通过")
            else:
                print("\n❌ 图像控制界面高度测试失败")
        elif choice == "2":
            create_height_demo()
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
    finally:
        print("\n🏁 测试结束")

if __name__ == "__main__":
    main()
