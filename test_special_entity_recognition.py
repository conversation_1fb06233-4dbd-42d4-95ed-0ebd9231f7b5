#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试特殊实体识别和自动标记功能
验证修复后的分组处理是否正确识别墙体、门窗等特殊图层
"""

import os
import sys

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_special_entity_recognition():
    """测试特殊实体识别功能"""
    print("🧪 测试特殊实体识别和自动标记...")
    
    try:
        # 导入必要的模块
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        from cad_data_processor import CADDataProcessor
        
        print("✅ 模块导入成功")
        
        # 创建测试用的处理器
        processor = CADDataProcessor()
        
        # 创建测试实体，包含各种特殊图层
        test_entities = [
            # 墙体实体
            {
                'type': 'LINE',
                'layer': 'WALL',
                'points': [[0, 0], [100, 0]],
                'start': [0, 0],
                'end': [100, 0]
            },
            {
                'type': 'LINE',
                'layer': '墙体',
                'points': [[100, 0], [100, 100]],
                'start': [100, 0],
                'end': [100, 100]
            },
            # 门窗实体
            {
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'points': [[50, 0], [50, 20]],
                'start': [50, 0],
                'end': [50, 20]
            },
            {
                'type': 'LINE',
                'layer': 'DOOR',
                'points': [[25, 0], [25, 15]],
                'start': [25, 0],
                'end': [25, 15]
            },
            # 栏杆实体
            {
                'type': 'LINE',
                'layer': 'RAILING',
                'points': [[0, 100], [100, 100]],
                'start': [0, 100],
                'end': [100, 100]
            },
            # 文字实体
            {
                'type': 'TEXT',
                'layer': 'TEXT',
                'text': '房间1',
                'position': [50, 50]
            },
            # 标注实体
            {
                'type': 'DIMENSION',
                'layer': 'DIM',
                'points': [[0, -10], [100, -10]]
            },
            # 其他实体
            {
                'type': 'CIRCLE',
                'layer': 'FURNITURE',
                'center': [30, 30],
                'radius': 10
            }
        ]
        
        print(f"✅ 创建了 {len(test_entities)} 个测试实体")
        
        # 测试特殊图层识别
        print("\n🔍 测试特殊图层识别...")
        
        wall_layers = processor._detect_special_layers(
            test_entities, processor.wall_layer_patterns, debug=False, layer_type="墙体"
        )
        door_window_layers = processor._detect_special_layers(
            test_entities, processor.door_window_layer_patterns, debug=False, layer_type="门窗"
        )
        railing_layers = processor._detect_special_layers(
            test_entities, processor.railing_layer_patterns, debug=False, layer_type="栏杆"
        )
        
        print(f"  墙体图层识别: {wall_layers}")
        print(f"  门窗图层识别: {door_window_layers}")
        print(f"  栏杆图层识别: {railing_layers}")
        
        # 验证识别结果
        expected_wall_layers = {'WALL', '墙体'}
        expected_door_window_layers = {'A-WINDOW', 'DOOR'}
        expected_railing_layers = {'RAILING'}
        
        wall_success = wall_layers == expected_wall_layers
        door_window_success = door_window_layers == expected_door_window_layers
        railing_success = railing_layers == expected_railing_layers
        
        print(f"  墙体识别正确: {wall_success}")
        print(f"  门窗识别正确: {door_window_success}")
        print(f"  栏杆识别正确: {railing_success}")
        
        return wall_success and door_window_success and railing_success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_grouping_with_auto_labeling():
    """测试分组处理中的自动标记功能"""
    print("\n🧪 测试分组处理中的自动标记...")
    
    try:
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        # 创建测试处理器
        from cad_data_processor import CADDataProcessor
        app.processor = type('MockProcessor', (), {})()
        app.processor.processor = CADDataProcessor()
        app.processor.auto_labeled_entities = []
        
        # 设置测试数据
        test_entities = [
            # 墙体实体
            {
                'type': 'LINE',
                'layer': 'WALL',
                'points': [[0, 0], [100, 0]],
                'start': [0, 0],
                'end': [100, 0]
            },
            # 门窗实体
            {
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'points': [[50, 0], [50, 20]],
                'start': [50, 0],
                'end': [50, 20]
            },
            # 其他实体
            {
                'type': 'CIRCLE',
                'layer': 'FURNITURE',
                'center': [30, 30],
                'radius': 10
            }
        ]
        
        app.processor.merged_entities = test_entities
        
        # 测试自动处理特殊实体
        try:
            auto_groups = app._auto_process_special_entities_v2(test_entities)
            print(f"✅ 自动处理特殊实体成功: {len(auto_groups)} 个组")
            print(f"✅ 自动标注实体数: {len(app.processor.auto_labeled_entities)}")
            
            # 检查自动标注的实体
            labeled_count = 0
            for entity in app.processor.auto_labeled_entities:
                if entity.get('auto_labeled', False):
                    labeled_count += 1
                    print(f"  自动标注: {entity.get('label', 'unknown')} - {entity.get('layer', 'unknown')}")
            
            print(f"✅ 实际自动标注数: {labeled_count}")
            
        except Exception as e:
            print(f"❌ 自动处理特殊实体失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试完整的分组处理
        try:
            success = app._process_grouping()
            print(f"✅ 完整分组处理成功: {success}")
            
            if success and hasattr(app.processor, 'groups_info'):
                auto_labeled_groups = sum(1 for info in app.processor.groups_info if info['status'] == 'auto_labeled')
                unlabeled_groups = sum(1 for info in app.processor.groups_info if info['status'] == 'unlabeled')
                
                print(f"✅ 分组统计:")
                print(f"  自动标注组: {auto_labeled_groups}")
                print(f"  未标注组: {unlabeled_groups}")
                print(f"  总组数: {len(app.processor.groups_info)}")
                
        except Exception as e:
            print(f"❌ 完整分组处理失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 清理
        root.destroy()
        
        print("✅ 分组自动标记测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 分组自动标记测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始特殊实体识别测试...")
    
    # 运行特殊实体识别测试
    success1 = test_special_entity_recognition()
    
    # 运行分组自动标记测试
    success2 = test_grouping_with_auto_labeling()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！特殊实体识别和自动标记功能正常")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
