#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
深度分析分组问题并提供解决方案
"""

import sys
import os
import math
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test_grouping_comprehensive import create_test_entities
from main_enhanced import EnhancedCADProcessor

def analyze_wall_grouping_issues():
    """分析墙体分组问题"""
    print("🔍 深度分析墙体分组问题")
    print("="*60)
    
    # 创建测试数据
    test_entities = create_test_entities()
    
    # 创建处理器
    processor = EnhancedCADProcessor()
    
    # 获取墙体实体
    wall_layers = processor.processor._detect_special_layers(
        test_entities, processor.processor.wall_layer_patterns, debug=False, layer_type="墙体"
    )
    wall_entities = [e for e in test_entities if e['layer'] in wall_layers]
    
    print(f"墙体实体总数: {len(wall_entities)}")
    
    # 分析每个墙体实体的坐标
    print(f"\n📐 墙体实体详细分析:")
    for i, entity in enumerate(wall_entities):
        points = entity.get('points', [])
        if len(points) >= 2:
            start, end = points[0], points[1]
            length = math.sqrt((end[0] - start[0])**2 + (end[1] - start[1])**2)
            print(f"  墙体 {i+1}: {start} -> {end} (长度: {length:.1f})")
    
    # 测试不同的连接阈值
    print(f"\n🔧 测试不同连接阈值的分组效果:")
    
    thresholds = [5, 10, 20, 50, 100]
    for threshold in thresholds:
        groups = processor.processor._group_special_entities_by_layer(
            wall_entities, connection_threshold=threshold, entity_type="wall"
        )
        print(f"  阈值 {threshold}: {len(groups)} 个组")
        
        # 显示每组的实体数
        for j, group in enumerate(groups):
            if isinstance(group, dict):
                entities = group.get('entities', [])
                print(f"    组 {j+1}: {len(entities)} 个实体")
    
    # 分析连接性问题
    print(f"\n🔗 分析墙体连接性问题:")
    analyze_wall_connectivity(wall_entities)
    
    return wall_entities

def analyze_wall_connectivity(wall_entities):
    """分析墙体连接性"""
    print(f"分析 {len(wall_entities)} 个墙体实体的连接性:")
    
    # 计算所有实体间的端点距离
    connections = []
    
    for i in range(len(wall_entities)):
        for j in range(i + 1, len(wall_entities)):
            entity1 = wall_entities[i]
            entity2 = wall_entities[j]
            
            points1 = entity1.get('points', [])
            points2 = entity2.get('points', [])
            
            if len(points1) >= 2 and len(points2) >= 2:
                start1, end1 = points1[0], points1[1]
                start2, end2 = points2[0], points2[1]
                
                # 计算所有端点间距离
                distances = [
                    math.sqrt((start1[0] - start2[0])**2 + (start1[1] - start2[1])**2),
                    math.sqrt((start1[0] - end2[0])**2 + (start1[1] - end2[1])**2),
                    math.sqrt((end1[0] - start2[0])**2 + (end1[1] - start2[1])**2),
                    math.sqrt((end1[0] - end2[0])**2 + (end1[1] - end2[1])**2)
                ]
                
                min_distance = min(distances)
                connections.append((i, j, min_distance))
    
    # 按距离排序
    connections.sort(key=lambda x: x[2])
    
    print(f"  前10个最近的连接:")
    for i, (idx1, idx2, distance) in enumerate(connections[:10]):
        print(f"    墙体{idx1+1} <-> 墙体{idx2+1}: 距离 {distance:.1f}")
        if distance <= 10:
            print(f"      ✅ 应该连接 (阈值≤10)")
        elif distance <= 50:
            print(f"      ⚠️ 可能连接 (阈值≤50)")
        else:
            print(f"      ❌ 不应连接 (距离过大)")

def analyze_door_window_grouping():
    """分析门窗分组问题"""
    print(f"\n🚪 深度分析门窗分组问题")
    print("="*60)
    
    # 创建测试数据
    test_entities = create_test_entities()
    
    # 创建处理器
    processor = EnhancedCADProcessor()
    
    # 获取门窗实体
    door_window_layers = processor.processor._detect_special_layers(
        test_entities, processor.processor.door_window_layer_patterns, debug=False, layer_type="门窗"
    )
    door_window_entities = [e for e in test_entities if e['layer'] in door_window_layers]
    
    print(f"门窗实体总数: {len(door_window_entities)}")
    
    # 按图层分析
    window_entities = [e for e in door_window_entities if e['layer'] == 'A-WINDOW']
    door_entities = [e for e in door_window_entities if e['layer'] == 'A-DOOR']
    
    print(f"  A-WINDOW图层: {len(window_entities)} 个实体")
    print(f"  A-DOOR图层: {len(door_entities)} 个实体")
    
    # 分析窗户实体的分组
    print(f"\n📐 窗户实体详细分析:")
    for i, entity in enumerate(window_entities):
        points = entity.get('points', [])
        if len(points) >= 2:
            start, end = points[0], points[1]
            print(f"  窗户 {i+1}: {start} -> {end}")
    
    # 分析门实体的分组
    print(f"\n🚪 门实体详细分析:")
    for i, entity in enumerate(door_entities):
        entity_type = entity.get('type')
        if entity_type == 'LINE':
            points = entity.get('points', [])
            if len(points) >= 2:
                start, end = points[0], points[1]
                print(f"  门线条 {i+1}: {start} -> {end}")
        elif entity_type == 'ARC':
            center = entity.get('center', (0, 0))
            radius = entity.get('radius', 0)
            print(f"  门弧线 {i+1}: 中心{center}, 半径{radius}")
    
    # 测试不同的连接阈值
    print(f"\n🔧 测试门窗分组的不同阈值:")
    
    thresholds = [20, 50, 100, 200, 500]
    for threshold in thresholds:
        groups = processor.processor._group_special_entities_by_layer(
            door_window_entities, connection_threshold=threshold, entity_type="door_window"
        )
        print(f"  阈值 {threshold}: {len(groups)} 个组")

def propose_grouping_improvements():
    """提出分组改进方案"""
    print(f"\n💡 分组改进方案")
    print("="*60)
    
    print("1. 墙体分组改进:")
    print("   - 问题: 连接阈值过小，导致连接的墙体被分割")
    print("   - 解决方案: 增加墙体连接阈值到50-100")
    print("   - 实现: 修改 connection_threshold 参数")
    
    print("\n2. 门窗分组改进:")
    print("   - 问题: 相关门窗实体没有被识别为一组")
    print("   - 解决方案: 增加门窗连接阈值，并考虑空间邻近性")
    print("   - 实现: 使用更大的 proximity_threshold")
    
    print("\n3. 其他实体分组改进:")
    print("   - 问题: 同类型实体被过度分割")
    print("   - 解决方案: 按图层和功能进行更智能的分组")
    print("   - 实现: 改进 _group_other_entities_by_layer 方法")

def test_improved_grouping():
    """测试改进的分组方案"""
    print(f"\n🧪 测试改进的分组方案")
    print("="*60)
    
    # 创建测试数据
    test_entities = create_test_entities()
    
    # 创建处理器
    processor = EnhancedCADProcessor()
    
    # 获取各类实体
    wall_layers = processor.processor._detect_special_layers(
        test_entities, processor.processor.wall_layer_patterns, debug=False, layer_type="墙体"
    )
    door_window_layers = processor.processor._detect_special_layers(
        test_entities, processor.processor.door_window_layer_patterns, debug=False, layer_type="门窗"
    )
    
    wall_entities = [e for e in test_entities if e['layer'] in wall_layers]
    door_window_entities = [e for e in test_entities if e['layer'] in door_window_layers]
    
    print("测试改进的参数:")
    
    # 测试改进的墙体分组
    print(f"\n1. 改进的墙体分组 (阈值: 100):")
    improved_wall_groups = processor.processor._group_special_entities_by_layer(
        wall_entities, connection_threshold=100, entity_type="wall"
    )
    print(f"   结果: {len(improved_wall_groups)} 个组 (期望: 3个)")
    
    for i, group in enumerate(improved_wall_groups):
        if isinstance(group, dict):
            entities = group.get('entities', [])
            print(f"   组 {i+1}: {len(entities)} 个实体")
    
    # 测试改进的门窗分组
    print(f"\n2. 改进的门窗分组 (阈值: 200):")
    improved_door_window_groups = processor.processor._group_special_entities_by_layer(
        door_window_entities, connection_threshold=200, entity_type="door_window"
    )
    print(f"   结果: {len(improved_door_window_groups)} 个组 (期望: 3个)")
    
    for i, group in enumerate(improved_door_window_groups):
        if isinstance(group, dict):
            entities = group.get('entities', [])
            layer = group.get('layer', 'unknown')
            print(f"   组 {i+1}: {len(entities)} 个实体 (图层: {layer})")
    
    return improved_wall_groups, improved_door_window_groups

def main():
    """主函数"""
    print("🚀 开始深度分析分组问题")
    
    # 1. 分析墙体分组问题
    wall_entities = analyze_wall_grouping_issues()
    
    # 2. 分析门窗分组问题
    analyze_door_window_grouping()
    
    # 3. 提出改进方案
    propose_grouping_improvements()
    
    # 4. 测试改进方案
    improved_wall_groups, improved_door_window_groups = test_improved_grouping()
    
    print(f"\n✅ 分组问题分析完成")
    print(f"📊 改进效果:")
    print(f"  墙体分组: {len(improved_wall_groups)} 个组")
    print(f"  门窗分组: {len(improved_door_window_groups)} 个组")

if __name__ == "__main__":
    main()
