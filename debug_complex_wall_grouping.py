#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试复杂墙体数据的分组问题
"""

import sys
import os
import math
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test_grouping_comprehensive import create_test_entities
from cad_data_processor import CADDataProcessor

def analyze_complex_wall_data():
    """分析复杂墙体数据的分组问题"""
    print("🔍 分析复杂墙体数据的分组问题")
    print("="*60)
    
    # 创建测试数据
    test_entities = create_test_entities()
    
    # 创建处理器
    processor = CADDataProcessor()
    
    # 获取墙体实体
    wall_layers = processor._detect_special_layers(
        test_entities, processor.wall_layer_patterns, debug=False, layer_type="墙体"
    )
    wall_entities = [e for e in test_entities if e['layer'] in wall_layers]
    
    print(f"复杂墙体数据: {len(wall_entities)} 个实体")
    
    # 按设计意图分析墙体
    print(f"\n📐 按设计意图分析墙体:")
    
    # 外墙矩形 (应该是1组)
    outer_walls = wall_entities[0:4]  # 前4个是外墙
    print(f"外墙组 (期望1组): {len(outer_walls)} 个实体")
    for i, entity in enumerate(outer_walls):
        points = entity['points']
        print(f"  外墙 {i+1}: {points[0]} -> {points[1]}")
    
    # 内墙 (应该是1组)
    inner_walls = wall_entities[4:6]  # 第5-6个是内墙
    print(f"\n内墙组 (期望1组): {len(inner_walls)} 个实体")
    for i, entity in enumerate(inner_walls):
        points = entity['points']
        print(f"  内墙 {i+1}: {points[0]} -> {points[1]}")
    
    # 独立墙段1 (应该是1组)
    independent_walls1 = wall_entities[6:8]  # 第7-8个
    print(f"\n独立墙段1 (期望1组): {len(independent_walls1)} 个实体")
    for i, entity in enumerate(independent_walls1):
        points = entity['points']
        print(f"  独立墙1-{i+1}: {points[0]} -> {points[1]}")
    
    # 独立墙段2 (应该是1组)
    independent_walls2 = wall_entities[8:11]  # 第9-11个
    print(f"\n独立墙段2 (期望1组): {len(independent_walls2)} 个实体")
    for i, entity in enumerate(independent_walls2):
        points = entity['points']
        print(f"  独立墙2-{i+1}: {points[0]} -> {points[1]}")
    
    # 测试每个预期组的连接性
    print(f"\n🔧 测试每个预期组的连接性:")
    
    test_groups = [
        ("外墙组", outer_walls),
        ("内墙组", inner_walls),
        ("独立墙段1", independent_walls1),
        ("独立墙段2", independent_walls2)
    ]
    
    for group_name, group_entities in test_groups:
        print(f"\n{group_name}:")
        
        # 测试组内连接性
        connected_pairs = []
        for i in range(len(group_entities)):
            for j in range(i + 1, len(group_entities)):
                entity1 = group_entities[i]
                entity2 = group_entities[j]
                
                is_connected = processor._are_entities_connected_by_endpoints(entity1, entity2, 10)
                
                if is_connected:
                    connected_pairs.append((i, j))
                    print(f"  ✅ 实体{i+1} 连接到 实体{j+1}")
                else:
                    # 计算距离
                    points1 = entity1['points']
                    points2 = entity2['points']
                    start1, end1 = points1[0], points1[1]
                    start2, end2 = points2[0], points2[1]
                    
                    distances = [
                        math.sqrt((start1[0] - start2[0])**2 + (start1[1] - start2[1])**2),
                        math.sqrt((start1[0] - end2[0])**2 + (start1[1] - end2[1])**2),
                        math.sqrt((end1[0] - start2[0])**2 + (end1[1] - start2[1])**2),
                        math.sqrt((end1[0] - end2[0])**2 + (end1[1] - end2[1])**2)
                    ]
                    min_distance = min(distances)
                    print(f"  ❌ 实体{i+1} 不连接 实体{j+1} (距离: {min_distance:.1f})")
        
        # 检查连通性
        if len(group_entities) > 1:
            # 构建连接图
            connections = {i: [] for i in range(len(group_entities))}
            for i, j in connected_pairs:
                connections[i].append(j)
                connections[j].append(i)
            
            # 查找连通分量
            visited = set()
            components = []
            
            def dfs(node, component):
                if node in visited:
                    return
                visited.add(node)
                component.append(node)
                for neighbor in connections[node]:
                    dfs(neighbor, component)
            
            for i in range(len(group_entities)):
                if i not in visited:
                    component = []
                    dfs(i, component)
                    components.append(component)
            
            print(f"  连通分量数: {len(components)} (期望: 1)")
            for k, component in enumerate(components):
                print(f"    分量 {k+1}: 实体 {[x+1 for x in component]}")
    
    # 测试实际分组结果
    print(f"\n🔧 测试实际分组结果:")
    
    actual_groups = processor._group_special_entities_by_layer(
        wall_entities, connection_threshold=10, entity_type="wall"
    )
    
    print(f"实际分组数: {len(actual_groups)}")
    
    for i, group in enumerate(actual_groups):
        if isinstance(group, dict):
            entities = group.get('entities', [])
            print(f"  实际组 {i+1}: {len(entities)} 个实体")
            for j, entity in enumerate(entities):
                points = entity['points']
                print(f"    实体 {j+1}: {points[0]} -> {points[1]}")

def test_step_by_step_grouping():
    """逐步测试分组过程"""
    print(f"\n🔧 逐步测试分组过程")
    print("="*60)
    
    # 创建处理器
    processor = CADDataProcessor()
    
    # 测试外墙组 (应该连接的4条线)
    outer_walls = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (6000, 0)], 'color': 7},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(6000, 0), (6000, 4000)], 'color': 7},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(6000, 4000), (0, 4000)], 'color': 7},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 4000), (0, 0)], 'color': 7},
    ]
    
    print(f"测试外墙组分组:")
    groups = processor._group_entities_by_precise_connection(outer_walls, threshold=10)
    print(f"  结果: {len(groups)} 个组 (期望: 1)")
    
    # 测试内墙组 (不连接的2条线)
    inner_walls = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(3000, 0), (3000, 4000)], 'color': 7},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 2000), (3000, 2000)], 'color': 7},
    ]
    
    print(f"\n测试内墙组分组:")
    groups = processor._group_entities_by_precise_connection(inner_walls, threshold=10)
    print(f"  结果: {len(groups)} 个组 (期望: 2，因为不连接)")
    
    # 测试混合数据
    print(f"\n测试混合数据分组:")
    mixed_walls = outer_walls + inner_walls
    groups = processor._group_entities_by_precise_connection(mixed_walls, threshold=10)
    print(f"  结果: {len(groups)} 个组 (期望: 3)")
    
    for i, group in enumerate(groups):
        print(f"    组 {i+1}: {len(group)} 个实体")

def main():
    """主函数"""
    print("🚀 开始调试复杂墙体分组问题")
    
    # 1. 分析复杂墙体数据
    analyze_complex_wall_data()
    
    # 2. 逐步测试分组过程
    test_step_by_step_grouping()
    
    print(f"\n✅ 复杂墙体分组问题调试完成")

if __name__ == "__main__":
    main()
