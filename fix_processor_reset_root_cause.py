#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复处理器重置根本原因
基于追踪结果：处理器在 _load_file_data → _load_from_cache 流程中被重置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_processor_reset_flow():
    """分析处理器重置流程"""
    print("🔍 分析处理器重置流程")
    print("="*60)
    
    print("📊 根据追踪结果分析:")
    print("   触发位置: _load_from_cache")
    print("   调用者: _load_file_data (第1316行)")
    print("   调用流程: 文件选择 → _load_file_data → _load_from_cache → 处理器检查")
    
    print(f"\n🎯 问题分析:")
    print("   1. 用户选择文件时触发 _load_file_data")
    print("   2. _load_file_data 调用 _load_from_cache 加载缓存")
    print("   3. _load_from_cache 检查处理器时发现为空")
    print("   4. 创建新处理器，导致数据丢失")
    
    print(f"\n❓ 关键问题:")
    print("   为什么在文件加载时处理器会为空？")
    print("   可能原因:")
    print("   - 文件切换时处理器被意外清空")
    print("   - 初始化顺序问题")
    print("   - 异常处理中处理器被重置")
    print("   - 多线程访问冲突")

def find_processor_clearing_points():
    """查找可能清空处理器的位置"""
    print(f"\n🔍 查找可能清空处理器的位置")
    print("="*60)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找所有可能清空处理器的位置
        clearing_patterns = [
            'self.processor = None',
            'processor = None',
            'del self.processor',
            'self.processor.clear()',
            'self.processor.reset()'
        ]
        
        lines = content.split('\n')
        clearing_points = []
        
        for i, line in enumerate(lines, 1):
            for pattern in clearing_patterns:
                if pattern in line:
                    # 获取方法名
                    method_name = "未知方法"
                    for j in range(i-1, max(0, i-20), -1):
                        if lines[j].strip().startswith('def '):
                            method_name = lines[j].strip()
                            break
                    
                    clearing_points.append({
                        'line': i,
                        'method': method_name,
                        'code': line.strip(),
                        'pattern': pattern
                    })
        
        print(f"发现 {len(clearing_points)} 个可能的处理器清空点:")
        for i, point in enumerate(clearing_points, 1):
            print(f"\n{i}. 第{point['line']}行 - {point['method']}")
            print(f"   代码: {point['code']}")
            print(f"   模式: {point['pattern']}")
        
        return clearing_points
        
    except Exception as e:
        print(f"❌ 查找失败: {e}")
        return []

def fix_processor_persistence():
    """修复处理器持久性问题"""
    print(f"\n🔧 修复处理器持久性问题")
    print("="*60)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复1：改进 _load_file_data 方法，确保处理器持久性
        print("1. 改进 _load_file_data 方法...")
        
        old_load_file_data = '''    def _load_file_data(self, file_path):
        """加载文件数据（增强版：支持完整的文件切换）"""
        file_name = os.path.basename(file_path)
        print(f"📁 开始加载文件数据: {file_name}")

        # 更新当前文件
        self.current_file = file_path'''
        
        new_load_file_data = '''    def _load_file_data(self, file_path):
        """加载文件数据（增强版：支持完整的文件切换）"""
        file_name = os.path.basename(file_path)
        print(f"📁 开始加载文件数据: {file_name}")

        # 🔧 修复：保存当前处理器状态
        previous_processor = self.processor
        
        # 更新当前文件
        self.current_file = file_path'''
        
        if old_load_file_data in content:
            content = content.replace(old_load_file_data, new_load_file_data)
            print("  ✅ 已保存处理器状态")
        
        # 修复2：改进 _load_from_cache 方法，避免不必要的重置
        print("2. 改进 _load_from_cache 方法...")
        
        old_cache_check = '''            # 确保处理器存在（关键修复）
            if not self.processor:
                # 🔍 详细追踪处理器重置（安全检查）
                if hasattr(self, '_track_processor_reset'):
                    self._track_processor_reset("_load_from_cache")
                else:
                    print("🔍 [追踪未初始化] 处理器重置 - _load_from_cache")
                print("  ⚠️ 处理器不存在，创建新的处理器")'''
        
        new_cache_check = '''            # 🔧 修复：智能处理器检查和恢复
            if not self.processor:
                # 🔍 详细追踪处理器重置（安全检查）
                if hasattr(self, '_track_processor_reset'):
                    self._track_processor_reset("_load_from_cache")
                else:
                    print("🔍 [追踪未初始化] 处理器重置 - _load_from_cache")
                
                # 🔧 修复：尝试从之前保存的状态恢复
                if hasattr(self, 'previous_processor') and self.previous_processor:
                    print("  🔄 从保存的状态恢复处理器")
                    self.processor = self.previous_processor
                    self.previous_processor = None
                else:
                    print("  ⚠️ 处理器不存在，创建新的处理器")'''
        
        if old_cache_check in content:
            content = content.replace(old_cache_check, new_cache_check)
            print("  ✅ 已改进处理器检查逻辑")
        
        # 修复3：在文件加载完成后清理临时状态
        print("3. 添加文件加载完成后的清理...")
        
        # 查找 _load_from_cache 方法的结尾
        cache_method_end = content.find('        except Exception as e:\n            print(f"❌ 从缓存加载数据失败: {e}")')
        if cache_method_end != -1:
            # 在方法结尾前添加清理代码
            cleanup_code = '''
        # 🔧 修复：清理临时状态
        if hasattr(self, 'previous_processor'):
            self.previous_processor = None
'''
            insert_pos = cache_method_end
            content = content[:insert_pos] + cleanup_code + content[insert_pos:]
            print("  ✅ 已添加状态清理代码")
        
        # 修复4：添加处理器状态验证
        print("4. 添加处理器状态验证...")
        
        validation_code = '''
    def _ensure_processor_exists(self, context="未知"):
        """确保处理器存在，如果不存在则尝试恢复或创建"""
        if not self.processor:
            print(f"🔧 处理器不存在 ({context})，尝试恢复...")
            
            # 尝试从缓存恢复
            if hasattr(self, 'current_file') and self.current_file:
                success = self._restore_processor_from_current_file()
                if success:
                    print(f"  ✅ 从缓存恢复处理器成功")
                    return True
            
            # 创建新处理器
            print(f"  🔄 创建新处理器")
            from main_enhanced import EnhancedCADProcessor
            self.processor = EnhancedCADProcessor(self.visualizer, self.canvas)
            
            # 设置回调
            if hasattr(self, 'on_status_update') and hasattr(self, 'on_progress_update'):
                self.processor.set_callbacks(self.on_status_update, self.on_progress_update)
            
            # 初始化类别映射
            if not hasattr(self.processor, 'category_mapping') or not self.processor.category_mapping:
                self.processor.category_mapping = {
                    'wall': '墙体',
                    'door_window': '门窗', 
                    'other': '其他'
                }
            
            return True
        
        return True

'''
        
        # 在类的末尾添加验证方法
        class_end = content.rfind('def main_v2():')
        if class_end != -1:
            content = content[:class_end] + validation_code + content[class_end:]
            print("  ✅ 已添加处理器状态验证方法")
        
        # 保存修改后的文件
        with open('main_enhanced_with_v2_fill.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 处理器持久性修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_scenario():
    """创建测试场景"""
    print(f"\n🧪 创建测试场景")
    print("="*60)
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试处理器持久性修复效果
"""

import sys
import os
import tkinter as tk
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_processor_persistence():
    """测试处理器持久性"""
    print("🧪 测试处理器持久性修复")
    print("="*50)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        print("1. 导入并创建应用...")
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        app = EnhancedCADAppV2(root)
        print("  ✅ 应用创建成功")
        
        print("2. 检查初始处理器状态...")
        initial_processor = app.processor
        print(f"  初始处理器: {'存在' if initial_processor else '不存在'}")
        
        print("3. 模拟文件加载...")
        # 创建测试文件路径
        test_file = "test_file.dxf"
        
        # 模拟文件数据
        app.file_data[test_file] = {
            'entities': [{'type': 'LINE', 'layer': 'A-WALL'}],
            'all_groups': [[{'type': 'LINE', 'layer': 'A-WALL'}]],
            'auto_labeled_entities': [],
            'labeled_entities': []
        }
        
        # 调用文件加载
        app._load_file_data(test_file)
        
        print("4. 检查加载后处理器状态...")
        after_load_processor = app.processor
        print(f"  加载后处理器: {'存在' if after_load_processor else '不存在'}")
        
        if initial_processor and after_load_processor:
            if initial_processor is after_load_processor:
                print("  ✅ 处理器保持一致，未被重置")
            else:
                print("  ⚠️ 处理器被替换，但仍然存在")
        elif after_load_processor:
            print("  ✅ 处理器存在（可能是新创建的）")
        else:
            print("  ❌ 处理器丢失")
        
        print("5. 检查重置计数...")
        if hasattr(app, '_processor_reset_count'):
            print(f"  重置次数: {app._processor_reset_count}")
            if app._processor_reset_count == 0:
                print("  ✅ 没有发生重置")
            else:
                print(f"  ⚠️ 发生了 {app._processor_reset_count} 次重置")
        
        # 清理
        root.destroy()
        
        print("\\n🎉 处理器持久性测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_processor_persistence()
'''
    
    with open('test_processor_persistence.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 测试场景已创建: test_processor_persistence.py")

def main():
    """主修复函数"""
    print("🚀 开始修复处理器重置根本原因")
    
    try:
        # 1. 分析处理器重置流程
        analyze_processor_reset_flow()
        
        # 2. 查找可能的清空点
        clearing_points = find_processor_clearing_points()
        
        # 3. 修复处理器持久性
        fix_success = fix_processor_persistence()
        
        # 4. 创建测试场景
        create_test_scenario()
        
        print(f"\n" + "="*60)
        print(f"📊 修复结果总结:")
        print(f"  发现清空点: {len(clearing_points)} 个")
        print(f"  持久性修复: {'✅ 成功' if fix_success else '❌ 失败'}")
        print(f"  测试场景: ✅ 已创建")
        
        if fix_success:
            print(f"\n🎯 修复内容:")
            print(f"   1. 在文件加载前保存处理器状态")
            print(f"   2. 改进处理器检查逻辑，优先恢复而非重建")
            print(f"   3. 添加状态清理机制")
            print(f"   4. 添加处理器状态验证方法")
            
            print(f"\n🧪 测试步骤:")
            print(f"   1. 运行测试: python test_processor_persistence.py")
            print(f"   2. 运行主程序: python main_enhanced_with_v2_fill.py")
            print(f"   3. 观察重置次数是否减少")
            print(f"   4. 验证图像显示是否正常")
            
            print(f"\n💡 预期效果:")
            print(f"   - 处理器重置次数显著减少")
            print(f"   - 图像预览正常显示")
            print(f"   - 组列表正确显示类型信息")
            print(f"   - 文件切换时数据不丢失")
        else:
            print(f"\n❌ 修复失败，请检查错误信息")
        
    except Exception as e:
        print(f"❌ 修复过程失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
