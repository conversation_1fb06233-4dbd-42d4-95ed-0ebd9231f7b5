#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试坐标修复
验证可视化器是否能正确处理新的数据格式
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_entity_drawing():
    """测试实体绘制"""
    print("🧪 测试实体绘制...")
    
    try:
        from cad_visualizer import CADVisualizer
        import matplotlib.pyplot as plt
        
        visualizer = CADVisualizer()
        
        # 创建测试实体（使用真实的数据格式）
        test_entities = [
            # 格式1：start_x, start_y, end_x, end_y（真实DXF格式）
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 342727.5,
                'start_y': 25051.0,
                'end_x': 342727.5,
                'end_y': 15031.0,
                'color': 256,
                'linetype': 'Continuous',
                'merged': True
            },
            # 格式2：points数组（旧格式）
            {
                'type': 'LINE',
                'layer': 'A-DOOR',
                'points': [(342700.0, 20000.0), (342750.0, 20000.0)],
                'color': 3
            },
            # 格式3：start, end数组
            {
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start': [342600.0, 22000.0],
                'end': [342800.0, 22000.0],
                'color': 4
            }
        ]
        
        print(f"📋 测试实体: {len(test_entities)} 个")
        for i, entity in enumerate(test_entities):
            print(f"  实体{i+1}: {entity['type']} - {entity['layer']}")
            if 'start_x' in entity:
                print(f"    坐标: ({entity['start_x']}, {entity['start_y']}) -> ({entity['end_x']}, {entity['end_y']})")
            elif 'points' in entity:
                print(f"    坐标: {entity['points']}")
            elif 'start' in entity:
                print(f"    坐标: {entity['start']} -> {entity['end']}")
        
        # 测试实体组可视化
        print("\n🎨 测试实体组可视化...")
        visualizer.visualize_entity_group(test_entities, {'wall': '墙体', 'door': '门', 'window': '窗'})
        
        # 检查详细视图
        if hasattr(visualizer, 'ax_detail') and visualizer.ax_detail:
            children = visualizer.ax_detail.get_children()
            line_objects = [child for child in children if hasattr(child, 'get_data')]
            print(f"  详细视图线条对象: {len(line_objects)} 个")
            
            # 检查坐标范围
            xlim = visualizer.ax_detail.get_xlim()
            ylim = visualizer.ax_detail.get_ylim()
            print(f"  详细视图坐标范围: X={xlim[0]:.1f}~{xlim[1]:.1f}, Y={ylim[0]:.1f}~{ylim[1]:.1f}")
            
            if len(line_objects) == len(test_entities):
                print("  ✅ 实体组可视化成功")
                return True
            else:
                print("  ❌ 实体组可视化失败")
                return False
        else:
            print("  ❌ 详细视图轴对象不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_overview_visualization():
    """测试全图概览可视化"""
    print("\n🌍 测试全图概览可视化...")
    
    try:
        from cad_visualizer import CADVisualizer
        
        visualizer = CADVisualizer()
        
        # 创建真实格式的测试数据
        all_entities = [
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 342727.5,
                'start_y': 25051.0,
                'end_x': 342727.5,
                'end_y': 15031.0,
                'color': 256
            },
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 342727.5,
                'start_y': 15031.0,
                'end_x': 339439.5,
                'end_y': 15031.0,
                'color': 256
            },
            {
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start_x': 342000.0,
                'start_y': 20000.0,
                'end_x': 342500.0,
                'end_y': 20000.0,
                'color': 4
            }
        ]
        
        current_group = [all_entities[0]]  # 第一个实体作为当前组
        labeled_entities = []
        
        print(f"📋 测试数据:")
        print(f"  所有实体: {len(all_entities)} 个")
        print(f"  当前组: {len(current_group)} 个")
        
        # 测试全图概览
        print("\n🌍 执行全图概览可视化...")
        visualizer.visualize_overview(all_entities, current_group, labeled_entities)
        
        # 检查概览视图
        if hasattr(visualizer, 'ax_overview') and visualizer.ax_overview:
            children = visualizer.ax_overview.get_children()
            line_objects = [child for child in children if hasattr(child, 'get_data')]
            print(f"  概览视图线条对象: {len(line_objects)} 个")
            
            # 检查坐标范围
            xlim = visualizer.ax_overview.get_xlim()
            ylim = visualizer.ax_overview.get_ylim()
            print(f"  概览视图坐标范围: X={xlim[0]:.1f}~{xlim[1]:.1f}, Y={ylim[0]:.1f}~{ylim[1]:.1f}")
            
            if len(line_objects) >= len(all_entities):
                print("  ✅ 全图概览可视化成功")
                return True
            else:
                print("  ❌ 全图概览可视化失败")
                print(f"    期望: {len(all_entities)} 个实体, 实际: {len(line_objects)} 个线条")
                return False
        else:
            print("  ❌ 概览视图轴对象不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coordinate_formats():
    """测试不同坐标格式的支持"""
    print("\n📐 测试不同坐标格式的支持...")
    
    try:
        from cad_visualizer import CADVisualizer
        import matplotlib.pyplot as plt
        
        visualizer = CADVisualizer()
        
        # 创建测试画布
        fig, ax = plt.subplots(1, 1, figsize=(8, 6))
        
        # 测试不同格式的实体
        test_cases = [
            {
                'name': 'start_x/start_y/end_x/end_y格式',
                'entity': {
                    'type': 'LINE',
                    'layer': 'A-WALL',
                    'start_x': 0,
                    'start_y': 0,
                    'end_x': 100,
                    'end_y': 0
                }
            },
            {
                'name': 'points数组格式',
                'entity': {
                    'type': 'LINE',
                    'layer': 'A-DOOR',
                    'points': [(0, 50), (100, 50)]
                }
            },
            {
                'name': 'start/end数组格式',
                'entity': {
                    'type': 'LINE',
                    'layer': 'A-WINDOW',
                    'start': [0, 100],
                    'end': [100, 100]
                }
            }
        ]
        
        print(f"📋 测试用例: {len(test_cases)} 个")
        
        success_count = 0
        for i, test_case in enumerate(test_cases):
            name = test_case['name']
            entity = test_case['entity']
            
            print(f"\n  测试{i+1}: {name}")
            print(f"    实体: {entity}")
            
            try:
                # 测试绘制
                visualizer._draw_entity(entity, 'red', 2, 1.0, ax)
                visualizer._draw_entity_enhanced(entity, 'blue', 2, 0.5, ax)
                print(f"    结果: ✅ 绘制成功")
                success_count += 1
            except Exception as e:
                print(f"    结果: ❌ 绘制失败 - {e}")
        
        plt.close(fig)
        
        print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 个成功")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CAD分类标注工具 - 坐标修复测试")
    print("=" * 60)
    
    tests = [
        ("实体绘制测试", test_entity_drawing),
        ("全图概览可视化测试", test_overview_visualization),
        ("坐标格式支持测试", test_coordinate_formats)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    failed_tests = [name for name, success in results if not success]
    
    if failed_tests:
        print(f"\n⚠️ 失败的测试: {len(failed_tests)} 个")
        print("需要进一步调查这些问题")
    else:
        print("\n🎉 所有测试通过！")
        print("坐标格式修复成功，现在应该能正确显示实体了")
    
    print("=" * 60)
