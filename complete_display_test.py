#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整的显示测试
测试文件处理 -> 数据存储 -> 数据读取 -> 视图显示的完整流程
"""

import sys
import os
import tkinter as tk
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_dxf_data():
    """创建测试DXF数据，模拟真实的DXF文件处理结果"""
    print("📝 创建测试DXF数据")
    print("="*50)
    
    # 模拟真实的DXF实体数据
    test_entities = [
        # 墙体实体 - 外墙
        {
            'type': 'LINE',
            'layer': 'A-WALL',
            'start': [0, 0],
            'end': [1000, 0],
            'points': [(0, 0), (1000, 0)],
            'color': 7,
            'handle': '1A0'
        },
        {
            'type': 'LINE', 
            'layer': 'A-WALL',
            'start': [1000, 0],
            'end': [1000, 800],
            'points': [(1000, 0), (1000, 800)],
            'color': 7,
            'handle': '1A1'
        },
        {
            'type': 'LINE',
            'layer': 'A-WALL', 
            'start': [1000, 800],
            'end': [0, 800],
            'points': [(1000, 800), (0, 800)],
            'color': 7,
            'handle': '1A2'
        },
        {
            'type': 'LINE',
            'layer': 'A-WALL',
            'start': [0, 800], 
            'end': [0, 0],
            'points': [(0, 800), (0, 0)],
            'color': 7,
            'handle': '1A3'
        },
        
        # 内墙
        {
            'type': 'LINE',
            'layer': 'A-WALL',
            'start': [500, 0],
            'end': [500, 800], 
            'points': [(500, 0), (500, 800)],
            'color': 7,
            'handle': '1A4'
        },
        
        # 门窗实体
        {
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start': [200, -10],
            'end': [200, 10],
            'points': [(200, -10), (200, 10)],
            'color': 3,
            'handle': '2B0'
        },
        {
            'type': 'LINE',
            'layer': 'A-WINDOW',
            'start': [800, -10],
            'end': [800, 10], 
            'points': [(800, -10), (800, 10)],
            'color': 4,
            'handle': '2B1'
        },
        {
            'type': 'LINE',
            'layer': 'A-WINDOW',
            'start': [490, 400],
            'end': [510, 400],
            'points': [(490, 400), (510, 400)],
            'color': 4,
            'handle': '2B2'
        }
    ]
    
    # 按类型分组
    wall_entities = [e for e in test_entities if e['layer'] == 'A-WALL']
    door_window_entities = [e for e in test_entities if e['layer'] in ['A-DOOR', 'A-WINDOW']]
    
    test_groups = [
        wall_entities,      # 组1: 墙体组
        door_window_entities # 组2: 门窗组
    ]
    
    # 创建组信息
    groups_info = [
        {
            'group_index': 0,
            'entity_count': len(wall_entities),
            'group_type': 'wall',
            'status': 'auto_labeled',
            'layer_info': {'A-WALL': len(wall_entities)}
        },
        {
            'group_index': 1, 
            'entity_count': len(door_window_entities),
            'group_type': 'door_window',
            'status': 'auto_labeled',
            'layer_info': {'A-DOOR': 1, 'A-WINDOW': 2}
        }
    ]
    
    print(f"  ✅ 创建了 {len(test_entities)} 个实体")
    print(f"  ✅ 创建了 {len(test_groups)} 个组")
    print(f"    - 墙体组: {len(wall_entities)} 个实体")
    print(f"    - 门窗组: {len(door_window_entities)} 个实体")
    
    return test_entities, test_groups, groups_info

def simulate_file_processing():
    """模拟文件处理过程"""
    print(f"\n🔄 模拟文件处理过程")
    print("="*50)
    
    # 创建测试数据
    entities, groups, groups_info = create_test_dxf_data()
    
    # 模拟处理后的数据结构（与实际程序一致）
    processed_data = {
        'entities': entities,
        'all_groups': groups,
        'auto_labeled_entities': entities,  # 假设所有实体都被自动标注
        'labeled_entities': [],
        'groups_info': groups_info,
        'dataset': {
            'entities': entities,
            'groups': groups
        },
        'group_fill_status': {},
        'processing_status': 'completed',
        'file_path': 'test_complete.dxf'
    }
    
    print(f"  ✅ 模拟处理完成")
    print(f"    - 总实体: {len(processed_data['entities'])}")
    print(f"    - 总组数: {len(processed_data['all_groups'])}")
    print(f"    - 自动标注: {len(processed_data['auto_labeled_entities'])}")
    
    return processed_data

def test_data_storage_and_retrieval():
    """测试数据存储和读取"""
    print(f"\n💾 测试数据存储和读取")
    print("="*50)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        print("1. 创建应用实例...")
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        app = EnhancedCADAppV2(root)
        print("  ✅ 应用创建成功")
        
        # 模拟文件处理
        print("2. 模拟文件处理...")
        processed_data = simulate_file_processing()
        
        # 存储数据到应用缓存
        print("3. 存储数据到应用缓存...")
        test_file = "test_complete.dxf"
        app.file_data[test_file] = processed_data
        print(f"  ✅ 数据已存储到缓存: {test_file}")
        
        # 验证存储的数据
        print("4. 验证存储的数据...")
        stored_data = app.file_data[test_file]
        print(f"  - 存储的实体数: {len(stored_data['entities'])}")
        print(f"  - 存储的组数: {len(stored_data['all_groups'])}")
        
        # 详细检查每个组
        for i, group in enumerate(stored_data['all_groups']):
            valid_entities = [e for e in group if isinstance(e, dict)]
            print(f"  - 组{i+1}: {len(valid_entities)} 个有效实体")
            if valid_entities:
                layers = set(e.get('layer', 'unknown') for e in valid_entities)
                types = set(e.get('type', 'unknown') for e in valid_entities)
                print(f"    图层: {layers}")
                print(f"    类型: {types}")
        
        # 测试数据读取
        print("5. 测试数据读取...")
        app._load_file_data(test_file)
        print("  ✅ 数据读取完成")
        
        # 验证处理器中的数据
        print("6. 验证处理器中的数据...")
        if app.processor:
            current_entities = getattr(app.processor, 'current_file_entities', [])
            all_groups = getattr(app.processor, 'all_groups', [])
            
            print(f"  - 处理器中的实体数: {len(current_entities)}")
            print(f"  - 处理器中的组数: {len(all_groups)}")
            
            # 检查实体完整性
            entity_check = True
            for i, entity in enumerate(current_entities):
                if not isinstance(entity, dict):
                    print(f"    ⚠️ 实体{i+1}不是字典类型: {type(entity)}")
                    entity_check = False
                elif 'type' not in entity or 'layer' not in entity:
                    print(f"    ⚠️ 实体{i+1}缺少必要字段: {entity}")
                    entity_check = False
            
            if entity_check:
                print("  ✅ 所有实体数据完整")
            else:
                print("  ❌ 实体数据有问题")
            
            # 检查组完整性
            group_check = True
            for i, group in enumerate(all_groups):
                if not isinstance(group, list):
                    print(f"    ⚠️ 组{i+1}不是列表类型: {type(group)}")
                    group_check = False
                else:
                    valid_entities = [e for e in group if isinstance(e, dict)]
                    if len(valid_entities) == 0:
                        print(f"    ⚠️ 组{i+1}没有有效实体")
                        group_check = False
            
            if group_check:
                print("  ✅ 所有组数据完整")
            else:
                print("  ❌ 组数据有问题")
        
        # 清理
        root.destroy()
        
        return app.processor if app.processor else None
        
    except Exception as e:
        print(f"❌ 数据存储和读取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_visualization_display(processor):
    """测试可视化显示"""
    print(f"\n🎨 测试可视化显示")
    print("="*50)
    
    if not processor:
        print("❌ 处理器不存在，无法测试显示")
        return False
    
    try:
        # 创建新的根窗口用于显示测试
        root = tk.Tk()
        root.withdraw()
        
        # 重新创建应用以测试显示
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        app = EnhancedCADAppV2(root)
        
        # 复制处理器数据
        app.processor.current_file_entities = getattr(processor, 'current_file_entities', [])
        app.processor.all_groups = getattr(processor, 'all_groups', [])
        app.processor.auto_labeled_entities = getattr(processor, 'auto_labeled_entities', [])
        app.processor.labeled_entities = getattr(processor, 'labeled_entities', [])
        
        print("1. 测试完整视图刷新...")
        if hasattr(app, '_refresh_complete_view'):
            success = app._refresh_complete_view()
            print(f"  完整视图刷新: {'✅ 成功' if success else '❌ 失败'}")
        else:
            print("  ⚠️ _refresh_complete_view 方法不存在")
        
        print("2. 测试各组显示...")
        all_groups = getattr(app.processor, 'all_groups', [])
        
        for i, group in enumerate(all_groups):
            print(f"  测试组{i+1}显示...")
            try:
                # 检查组数据
                valid_entities = [e for e in group if isinstance(e, dict)]
                print(f"    组{i+1}有效实体数: {len(valid_entities)}")
                
                if valid_entities:
                    # 显示组
                    app._show_group(group, i + 1)
                    print(f"    组{i+1}显示: ✅ 成功")
                    
                    # 检查可视化器状态
                    if hasattr(app.processor, 'visualizer') and app.processor.visualizer:
                        # 检查详细视图
                        if hasattr(app.processor.visualizer, 'ax_detail'):
                            detail_children = len(app.processor.visualizer.ax_detail.get_children())
                            print(f"    详细视图元素数: {detail_children}")
                        
                        # 检查概览视图
                        if hasattr(app.processor.visualizer, 'ax_overview'):
                            overview_children = len(app.processor.visualizer.ax_overview.get_children())
                            print(f"    概览视图元素数: {overview_children}")
                    
                else:
                    print(f"    组{i+1}显示: ⚠️ 跳过（无有效实体）")
                    
            except Exception as e:
                print(f"    组{i+1}显示: ❌ 失败 - {e}")
        
        print("3. 测试全图概览显示...")
        try:
            current_entities = getattr(app.processor, 'current_file_entities', [])
            labeled_entities = getattr(app.processor, 'auto_labeled_entities', []) + getattr(app.processor, 'labeled_entities', [])
            
            if current_entities and hasattr(app.processor, 'visualizer'):
                app.processor.visualizer.visualize_overview(
                    current_entities,
                    None,  # 不高亮特定组
                    labeled_entities,
                    processor=app.processor
                )
                
                if hasattr(app, 'canvas'):
                    app.processor.visualizer.update_canvas(app.canvas)
                
                print("  ✅ 全图概览显示成功")
            else:
                print("  ⚠️ 缺少必要数据或可视化器")
                
        except Exception as e:
            print(f"  ❌ 全图概览显示失败: {e}")
        
        # 清理
        root.destroy()
        
        print("✅ 可视化显示测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 可视化显示测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_display_pipeline():
    """分析显示管道，找出问题所在"""
    print(f"\n🔍 分析显示管道")
    print("="*50)
    
    print("显示管道流程:")
    print("  1. 文件处理 -> 生成实体和组数据")
    print("  2. 数据存储 -> 保存到 file_data 缓存")
    print("  3. 数据读取 -> _load_file_data -> _load_from_cache")
    print("  4. 数据赋值 -> 赋值给 processor 的各个属性")
    print("  5. 界面更新 -> update_group_list, _show_group")
    print("  6. 可视化显示 -> visualize_overview, visualize_entity_group")
    
    print(f"\n可能的问题点:")
    print("  ❓ 数据存储时格式不正确")
    print("  ❓ 数据读取时丢失或损坏")
    print("  ❓ 处理器赋值时引用错误")
    print("  ❓ 可视化器调用时参数错误")
    print("  ❓ 画布更新时显示不正确")

def create_entity_status_fix():
    """创建实体状态修复脚本"""
    print(f"\n🔧 创建实体状态修复脚本")
    print("="*50)

    fix_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复实体状态识别问题
确保已标注的实体能正确显示颜色
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_entity_status_recognition():
    """修复实体状态识别"""
    print("🔧 修复实体状态识别")
    print("="*50)

    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # 修复1：确保auto_labeled_entities正确传递给可视化器
        print("1. 修复auto_labeled_entities传递...")

        old_show_group = """                if current_file_entities:
                    # 🔑 关键修复：显示所有组，高亮当前组
                    print(f"  🌍 显示全图概览: 总实体={len(current_file_entities)}, 当前组实体={len(cleaned_group)}")

                    # 获取所有组信息用于完整显示
                    all_groups = getattr(self.processor, 'all_groups', [])
                    print(f"  📊 所有组数: {len(all_groups)}")

                    self.processor.visualizer.visualize_overview(
                        current_file_entities,  # 显示所有实体
                        cleaned_group,  # 高亮当前组
                        auto_labeled_entities + labeled_entities,  # 已标注实体
                        processor=self.processor,  # 传递处理器以获取组信息
                        current_group_index=group_index  # 当前组索引用于高亮
                    )
                    print(f"  ✅ 全图概览更新完成")
                else:
                    print("  ⚠️ 没有实体数据，跳过全图概览更新")"""

        new_show_group = """                if current_file_entities:
                    # 🔑 关键修复：显示所有组，高亮当前组
                    print(f"  🌍 显示全图概览: 总实体={len(current_file_entities)}, 当前组实体={len(cleaned_group)}")

                    # 获取所有组信息用于完整显示
                    all_groups = getattr(self.processor, 'all_groups', [])
                    print(f"  📊 所有组数: {len(all_groups)}")

                    # 🔧 修复：确保已标注实体正确识别
                    all_labeled = auto_labeled_entities + labeled_entities
                    print(f"  📊 已标注实体数: {len(all_labeled)} (自动:{len(auto_labeled_entities)}, 手动:{len(labeled_entities)})")

                    # 检查实体标注状态
                    for i, entity in enumerate(current_file_entities[:3]):  # 只检查前3个
                        is_labeled = any(self.processor._is_entity_in_group_safe(entity, [labeled]) for labeled in all_labeled)
                        print(f"    实体{i+1}: {'已标注' if is_labeled else '未标注'} - {entity.get('layer', 'unknown')}")

                    self.processor.visualizer.visualize_overview(
                        current_file_entities,  # 显示所有实体
                        cleaned_group,  # 高亮当前组
                        all_labeled,  # 已标注实体
                        processor=self.processor,  # 传递处理器以获取组信息
                        current_group_index=group_index  # 当前组索引用于高亮
                    )
                    print(f"  ✅ 全图概览更新完成")
                else:
                    print("  ⚠️ 没有实体数据，跳过全图概览更新")"""

        if old_show_group in content:
            content = content.replace(old_show_group, new_show_group)
            print("  ✅ 已修复auto_labeled_entities传递")

        # 修复2：改进_refresh_complete_view方法
        print("2. 修复_refresh_complete_view方法...")

        old_refresh = """            # 使用可视化器显示完整视图
            if (hasattr(self.processor, 'visualizer') and self.processor.visualizer and
                hasattr(self, 'canvas') and self.canvas):

                # 显示全图概览（不高亮任何特定组）
                self.processor.visualizer.visualize_overview(
                    current_file_entities,  # 所有实体
                    None,  # 不高亮特定组
                    auto_labeled_entities + labeled_entities,  # 已标注实体
                    processor=self.processor  # 处理器信息
                )"""

        new_refresh = """            # 使用可视化器显示完整视图
            if (hasattr(self.processor, 'visualizer') and self.processor.visualizer and
                hasattr(self, 'canvas') and self.canvas):

                # 🔧 修复：确保已标注实体正确识别
                all_labeled = auto_labeled_entities + labeled_entities
                print(f"    完整视图已标注实体: {len(all_labeled)}")

                # 显示全图概览（不高亮任何特定组）
                self.processor.visualizer.visualize_overview(
                    current_file_entities,  # 所有实体
                    None,  # 不高亮特定组
                    all_labeled,  # 已标注实体
                    processor=self.processor  # 处理器信息
                )"""

        if old_refresh in content:
            content = content.replace(old_refresh, new_refresh)
            print("  ✅ 已修复_refresh_complete_view方法")

        # 保存修改后的文件
        with open('main_enhanced_with_v2_fill.py', 'w', encoding='utf-8') as f:
            f.write(content)

        print("✅ 实体状态识别修复完成")
        return True

    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

if __name__ == "__main__":
    fix_entity_status_recognition()
'''

    with open('fix_entity_status.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)

    print("✅ 实体状态修复脚本已创建: fix_entity_status.py")

def main():
    """主测试函数"""
    print("🚀 开始完整显示测试")
    print("="*60)

    try:
        # 1. 分析显示管道
        analyze_display_pipeline()

        # 2. 测试数据存储和读取
        processor = test_data_storage_and_retrieval()

        # 3. 测试可视化显示
        if processor:
            test_visualization_display(processor)

        print(f"\n" + "="*60)
        print("📊 完整测试结果总结:")

        if processor:
            print("  ✅ 数据处理和存储: 成功")
            print("  ✅ 数据读取和赋值: 成功")
            print("  ✅ 可视化显示测试: 已执行")

            print(f"\n🎯 关键发现:")
            current_entities = getattr(processor, 'current_file_entities', [])
            all_groups = getattr(processor, 'all_groups', [])
            auto_labeled = getattr(processor, 'auto_labeled_entities', [])

            print(f"  - 处理器中有 {len(current_entities)} 个实体")
            print(f"  - 处理器中有 {len(all_groups)} 个组")
            print(f"  - 自动标注实体: {len(auto_labeled)} 个")

            if current_entities and all_groups:
                print("  ✅ 数据完整")
                if len(auto_labeled) == 0:
                    print("  ❌ 关键问题: 所有实体显示为'未标注'状态")
                    print("  🎯 问题根源: 实体状态识别错误")
                    print("  💡 解决方案: 修复实体状态识别逻辑")
                else:
                    print("  ✅ 实体状态正常")
            else:
                print("  ❌ 数据不完整，问题在数据处理环节")
        else:
            print("  ❌ 数据处理失败")

        # 4. 创建修复脚本
        create_entity_status_fix()

        print(f"\n💡 下一步操作:")
        print("  1. 运行修复脚本: python fix_entity_status.py")
        print("  2. 运行主程序: python main_enhanced_with_v2_fill.py")
        print("  3. 观察实体是否有正确的颜色显示")
        print("  4. 检查墙体和门窗是否用不同颜色区分")

    except Exception as e:
        print(f"❌ 完整测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
