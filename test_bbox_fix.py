#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试边界框修复
验证修复后的边界框计算是否正确工作
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_entity_bbox_calculation():
    """测试实体边界框计算"""
    print("🧪 测试实体边界框计算...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 测试不同格式的LINE实体
        test_entities = [
            {
                'name': 'start_x/start_y/end_x/end_y格式',
                'entity': {
                    'type': 'LINE',
                    'layer': 'A-WALL',
                    'start_x': 10.0,
                    'start_y': 20.0,
                    'end_x': 30.0,
                    'end_y': 40.0
                },
                'expected': (10.0, 20.0, 30.0, 40.0)
            },
            {
                'name': 'points格式',
                'entity': {
                    'type': 'LINE',
                    'layer': 'A-WALL',
                    'points': [(10.0, 20.0), (30.0, 40.0)]
                },
                'expected': (10.0, 20.0, 30.0, 40.0)
            },
            {
                'name': 'start/end格式',
                'entity': {
                    'type': 'LINE',
                    'layer': 'A-WALL',
                    'start': (10.0, 20.0),
                    'end': (30.0, 40.0)
                },
                'expected': (10.0, 20.0, 30.0, 40.0)
            },
            {
                'name': '反向线段',
                'entity': {
                    'type': 'LINE',
                    'layer': 'A-WALL',
                    'start_x': 30.0,
                    'start_y': 40.0,
                    'end_x': 10.0,
                    'end_y': 20.0
                },
                'expected': (10.0, 20.0, 30.0, 40.0)  # 边界框应该是标准化的
            }
        ]
        
        print(f"📋 测试实体: {len(test_entities)} 个")
        
        all_correct = True
        for test_case in test_entities:
            name = test_case['name']
            entity = test_case['entity']
            expected = test_case['expected']
            
            bbox = processor._get_entity_bbox(entity)
            
            print(f"  {name}:")
            print(f"    实体: {entity}")
            print(f"    期望边界框: {expected}")
            print(f"    实际边界框: {bbox}")
            
            if bbox == expected:
                print(f"    ✅ 正确")
            elif bbox is None:
                print(f"    ❌ 返回None")
                all_correct = False
            else:
                print(f"    ❌ 边界框不匹配")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_group_bbox_calculation():
    """测试组边界框计算"""
    print("\n🧪 测试组边界框计算...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 创建测试组
        test_group = [
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 0.0,
                'start_y': 0.0,
                'end_x': 10.0,
                'end_y': 10.0
            },
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 5.0,
                'start_y': 5.0,
                'end_x': 15.0,
                'end_y': 15.0
            }
        ]
        
        print(f"📋 测试组: {len(test_group)} 个实体")
        
        bbox = processor._get_group_bbox(test_group)
        expected = (0.0, 0.0, 15.0, 15.0)  # 应该包含所有实体的边界框
        
        print(f"  期望组边界框: {expected}")
        print(f"  实际组边界框: {bbox}")
        
        if bbox == expected:
            print(f"  ✅ 组边界框计算正确")
            return True
        elif bbox is None:
            print(f"  ❌ 组边界框返回None")
            return False
        else:
            print(f"  ❌ 组边界框不匹配")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_wall_merge_fix():
    """测试墙体合并修复"""
    print("\n🧪 测试墙体合并修复...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 创建测试墙体组
        wall_groups = []
        
        # 创建5个不重叠的墙体组
        for i in range(5):
            entity = {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': float(i * 100),
                'start_y': 0.0,
                'end_x': float(i * 100 + 50),
                'end_y': 50.0,
                'label': 'wall',
                'auto_labeled': True
            }
            wall_groups.append([entity])
        
        print(f"📋 创建测试墙体组: {len(wall_groups)} 个")
        
        # 检查边界框计算
        print(f"\n🔧 检查边界框计算:")
        bbox_count = 0
        for i, group in enumerate(wall_groups):
            bbox = processor._get_group_bbox(group)
            print(f"  组{i}: {bbox}")
            if bbox is not None:
                bbox_count += 1
        
        print(f"  有效边界框数量: {bbox_count}/{len(wall_groups)}")
        
        # 执行合并
        print(f"\n🔧 执行墙体组合并...")
        result = processor.merge_contained_wall_groups(wall_groups)
        
        print(f"\n📊 合并结果:")
        print(f"  输入: {len(wall_groups)} 个组")
        print(f"  输出: {len(result)} 个组")
        
        # 由于这些组不重叠，应该保留所有组
        if len(result) == len(wall_groups):
            print(f"  ✅ 墙体合并修复成功！")
            return True
        elif len(result) == 0:
            print(f"  ❌ 仍然存在问题：所有组都被合并掉了")
            return False
        else:
            print(f"  ⚠️ 部分组被合并，需要进一步检查")
            return True  # 部分成功也算修复
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_large_scale_scenario():
    """测试大规模场景（模拟230个组）"""
    print("\n🎯 测试大规模场景（模拟230个组）...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 创建230个墙体组
        wall_groups = []
        
        for i in range(230):
            entity = {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': float(i),
                'start_y': 0.0,
                'end_x': float(i + 1),
                'end_y': 1.0,
                'label': 'wall',
                'auto_labeled': True
            }
            wall_groups.append([entity])
        
        print(f"📋 创建大规模测试数据: {len(wall_groups)} 个墙体组")
        
        # 检查前几个组的边界框
        print(f"\n🔧 检查前5个组的边界框:")
        valid_bbox_count = 0
        for i in range(min(5, len(wall_groups))):
            bbox = processor._get_group_bbox(wall_groups[i])
            print(f"  组{i}: {bbox}")
            if bbox is not None:
                valid_bbox_count += 1
        
        print(f"  前5个组中有效边界框: {valid_bbox_count}/5")
        
        # 执行合并
        print(f"\n🔧 执行大规模合并...")
        result = processor.merge_contained_wall_groups(wall_groups)
        
        print(f"\n📊 大规模合并结果:")
        print(f"  输入: {len(wall_groups)} 个组")
        print(f"  输出: {len(result)} 个组")
        
        if len(result) > 0:
            print(f"  ✅ 大规模场景修复成功！保留了 {len(result)} 个组")
            return True
        else:
            print(f"  ❌ 大规模场景仍有问题：所有组都被合并掉了")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CAD分类标注工具 - 边界框修复测试")
    print("=" * 60)
    
    tests = [
        ("实体边界框计算测试", test_entity_bbox_calculation),
        ("组边界框计算测试", test_group_bbox_calculation),
        ("墙体合并修复测试", test_wall_merge_fix),
        ("大规模场景测试", test_large_scale_scenario)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    all_passed = True
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not success:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 所有测试通过！")
        print(f"✅ 边界框计算修复成功")
        print(f"✅ 墙体组合并问题已解决")
        print(f"✅ 现在应该不会出现 230 -> 0 的问题了")
    else:
        print(f"\n⚠️ 部分测试失败")
        print(f"需要进一步调查")
    
    print("=" * 60)
