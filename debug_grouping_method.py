#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试分组方法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cad_data_processor import CADDataProcessor

def debug_grouping_method():
    """调试分组方法"""
    print("🔍 调试分组方法")
    print("="*60)
    
    # 创建处理器
    processor = CADDataProcessor()
    
    # 创建简单的测试墙体数据
    wall_entities = [
        # 连接的墙体线条
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (1000, 0)], 'color': 7, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(1000, 0), (1000, 1000)], 'color': 7, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(1000, 1000), (0, 1000)], 'color': 7, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 1000), (0, 0)], 'color': 7, 'linetype': 'CONTINUOUS'},
        
        # 独立的墙体线条
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(2000, 0), (3000, 0)], 'color': 7, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(3000, 0), (3000, 1000)], 'color': 7, 'linetype': 'CONTINUOUS'},
    ]
    
    print(f"测试数据: {len(wall_entities)} 个墙体实体")
    for i, entity in enumerate(wall_entities):
        points = entity.get('points', [])
        print(f"  实体 {i+1}: {points[0]} -> {points[1]}")
    
    # 测试分组方法
    print(f"\n🔧 调用 _group_special_entities_by_layer...")
    
    try:
        groups = processor._group_special_entities_by_layer(
            wall_entities, connection_threshold=10, entity_type="wall"
        )
        
        print(f"✅ 分组成功，返回 {len(groups)} 个组")
        
        for i, group in enumerate(groups):
            print(f"\n  组 {i+1}:")
            if isinstance(group, dict):
                entities = group.get('entities', [])
                print(f"    格式: 字典格式")
                print(f"    类型: {group.get('group_type', 'unknown')}")
                print(f"    图层: {group.get('layer', 'unknown')}")
                print(f"    实体数: {len(entities)}")
                for j, entity in enumerate(entities):
                    if isinstance(entity, dict) and 'points' in entity:
                        points = entity['points']
                        print(f"      实体 {j+1}: {points[0]} -> {points[1]}")
            elif isinstance(group, list):
                print(f"    格式: 列表格式")
                print(f"    实体数: {len(group)}")
                for j, entity in enumerate(group):
                    if isinstance(entity, dict) and 'points' in entity:
                        points = entity['points']
                        print(f"      实体 {j+1}: {points[0]} -> {points[1]}")
            else:
                print(f"    格式: 未知格式 ({type(group)})")
        
        return groups
        
    except Exception as e:
        print(f"❌ 分组失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def debug_precise_connection():
    """调试精确连接方法"""
    print(f"\n🔍 调试精确连接方法")
    print("="*60)
    
    # 创建处理器
    processor = CADDataProcessor()
    
    # 创建简单的连接线条
    entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'color': 7},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'color': 7},
    ]
    
    print(f"测试数据: {len(entities)} 个实体")
    for i, entity in enumerate(entities):
        points = entity.get('points', [])
        print(f"  实体 {i+1}: {points[0]} -> {points[1]}")
    
    # 测试精确连接方法
    print(f"\n🔧 调用 _group_entities_by_precise_connection...")
    
    try:
        groups = processor._group_entities_by_precise_connection(entities, threshold=10)
        
        print(f"✅ 精确连接分组成功，返回 {len(groups)} 个组")
        
        for i, group in enumerate(groups):
            print(f"  组 {i+1}: {len(group)} 个实体")
            for j, entity in enumerate(group):
                if isinstance(entity, dict) and 'points' in entity:
                    points = entity['points']
                    print(f"    实体 {j+1}: {points[0]} -> {points[1]}")
        
        return groups
        
    except Exception as e:
        print(f"❌ 精确连接分组失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def debug_connection_detection():
    """调试连接检测"""
    print(f"\n🔍 调试连接检测")
    print("="*60)
    
    # 创建处理器
    processor = CADDataProcessor()
    
    # 创建两个应该连接的实体
    entity1 = {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'color': 7}
    entity2 = {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'color': 7}
    
    print(f"实体1: {entity1['points'][0]} -> {entity1['points'][1]}")
    print(f"实体2: {entity2['points'][0]} -> {entity2['points'][1]}")
    
    # 测试连接检测
    print(f"\n🔧 测试连接检测方法...")
    
    try:
        # 测试端点连接
        is_connected_endpoints = processor._are_entities_connected_by_endpoints(entity1, entity2, threshold=10)
        print(f"端点连接检测: {is_connected_endpoints}")
        
        # 测试一般连接
        is_connected_general = processor._are_entities_connected(entity1, entity2, threshold=10)
        print(f"一般连接检测: {is_connected_general}")
        
        # 手动计算端点距离
        p1_start, p1_end = entity1['points']
        p2_start, p2_end = entity2['points']
        
        import math
        distances = [
            math.sqrt((p1_start[0] - p2_start[0])**2 + (p1_start[1] - p2_start[1])**2),
            math.sqrt((p1_start[0] - p2_end[0])**2 + (p1_start[1] - p2_end[1])**2),
            math.sqrt((p1_end[0] - p2_start[0])**2 + (p1_end[1] - p2_start[1])**2),
            math.sqrt((p1_end[0] - p2_end[0])**2 + (p1_end[1] - p2_end[1])**2),
        ]
        
        min_distance = min(distances)
        print(f"手动计算最小端点距离: {min_distance}")
        print(f"阈值: 10")
        print(f"应该连接: {min_distance <= 10}")
        
    except Exception as e:
        print(f"❌ 连接检测失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始调试分组方法")
    
    # 1. 调试连接检测
    debug_connection_detection()
    
    # 2. 调试精确连接方法
    debug_precise_connection()
    
    # 3. 调试分组方法
    debug_grouping_method()
    
    print(f"\n✅ 调试完成")

if __name__ == "__main__":
    main()
