#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的图层控制界面
验证：1. 应用按钮与图层列表并列 2. 删除下拉菜单 3. 上下箭头移动 4. 圆形显示/隐藏按钮
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

def test_layer_control_ui():
    """测试图层控制界面的新布局"""
    print("🧪 测试新的图层控制界面...")
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("图层控制界面测试")
        root.geometry("400x500")
        
        # 模拟图层控制区域
        test_frame = tk.Frame(root, relief='ridge', bd=2)
        test_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 创建测试用的图层控制实例
        layer_control = LayerControlTest(test_frame)
        
        print("✅ 图层控制界面测试窗口已创建")
        print("📋 测试要点:")
        print("  1. 标题和应用按钮是否水平并列")
        print("  2. 是否删除了下拉菜单")
        print("  3. 每个图层是否有上下箭头按钮")
        print("  4. 显示/隐藏按钮是否为圆形")
        print("  5. 圆形按钮颜色：绿色=显示，灰色=隐藏")
        
        # 启动测试窗口
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

class LayerControlTest:
    """图层控制测试类"""
    
    def __init__(self, parent):
        self.parent = parent
        self._create_test_layer_control()
    
    def _create_test_layer_control(self):
        """创建测试用的图层控制界面"""
        try:
            # 标题和应用按钮水平并列
            header_frame = tk.Frame(self.parent)
            header_frame.pack(fill='x', pady=(2, 5), padx=5)
            
            # 左侧标题
            layer_title = tk.Label(header_frame, text="图层控制",
                                 font=('Arial', 9, 'bold'), bg='#E6F3FF')
            layer_title.pack(side='left')
            
            # 右侧应用按钮
            apply_btn = tk.Button(header_frame, text="应用图层设置",
                                command=self._test_apply_settings,
                                bg='#FF5722', fg='white', font=('Arial', 8, 'bold'))
            apply_btn.pack(side='right')

            # 创建滚动容器
            canvas = tk.Canvas(self.parent)
            scrollbar = tk.Scrollbar(self.parent, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # 初始化测试数据
            self.layer_states = {
                'cad_lines': tk.BooleanVar(value=True),
                'wall_fill': tk.BooleanVar(value=True),
                'furniture_fill': tk.BooleanVar(value=False),
                'room_fill': tk.BooleanVar(value=True)
            }
            
            self.layer_order = ['cad_lines', 'wall_fill', 'furniture_fill', 'room_fill']
            self.layer_items_data = [
                ('cad_lines', 'CAD线条', '#2196F3', '显示/隐藏CAD原始线条'),
                ('wall_fill', '墙体填充', '#4CAF50', '显示/隐藏墙体填充效果'),
                ('furniture_fill', '家具填充', '#FF9800', '显示/隐藏家具填充效果'),
                ('room_fill', '房间填充', '#9C27B0', '显示/隐藏房间填充效果')
            ]

            # 创建图层列表容器
            self.layer_list_frame = tk.Frame(scrollable_frame)
            self.layer_list_frame.pack(fill='both', expand=True, padx=5, pady=5)
            
            # 创建图层控制项
            self._create_test_layer_items()

            # 布局滚动组件
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")
            
            print("✅ 测试图层控制界面创建完成")

        except Exception as e:
            print(f"❌ 创建测试界面失败: {e}")
    
    def _create_test_layer_items(self):
        """创建测试图层项"""
        try:
            # 清空现有项目
            for widget in self.layer_list_frame.winfo_children():
                widget.destroy()
            
            # 按当前顺序创建图层项
            for i, layer_key in enumerate(self.layer_order):
                # 找到对应的图层数据
                layer_data = None
                for data in self.layer_items_data:
                    if data[0] == layer_key:
                        layer_data = data
                        break
                
                if layer_data:
                    self._create_test_single_layer_item(i, layer_data)
                    
        except Exception as e:
            print(f"❌ 创建测试图层项失败: {e}")

    def _create_test_single_layer_item(self, index, layer_data):
        """创建单个测试图层项"""
        try:
            layer_key, layer_name, color, tooltip = layer_data
            
            # 图层项容器（方框）
            item_frame = tk.Frame(self.layer_list_frame, relief='ridge', bd=1, bg='#F5F5F5')
            item_frame.pack(fill='x', pady=2)
            
            # 主要内容行
            main_row = tk.Frame(item_frame)
            main_row.pack(fill='x', padx=5, pady=3)

            # 左侧：圆形显示/隐藏按钮
            self._create_test_circular_button(main_row, layer_key)

            # 中间：颜色指示器和图层名称
            middle_frame = tk.Frame(main_row)
            middle_frame.pack(side='left', fill='x', expand=True, padx=(5, 5))
            
            # 颜色指示器
            color_label = tk.Label(middle_frame, text="●", fg=color, font=('Arial', 10, 'bold'))
            color_label.pack(side='left', padx=(0, 5))
            
            # 图层名称
            name_label = tk.Label(middle_frame, text=layer_name,
                                font=('Arial', 9), anchor='w')
            name_label.pack(side='left', fill='x', expand=True)

            # 右侧：上下箭头按钮
            arrows_frame = tk.Frame(main_row)
            arrows_frame.pack(side='right')
            
            # 上箭头按钮
            up_btn = tk.Button(arrows_frame, text="▲", font=('Arial', 8),
                             width=2, height=1,
                             command=lambda: self._test_move_layer_up(index),
                             state='normal' if index > 0 else 'disabled')
            up_btn.pack(side='top', pady=(0, 1))
            
            # 下箭头按钮
            down_btn = tk.Button(arrows_frame, text="▼", font=('Arial', 8),
                               width=2, height=1,
                               command=lambda: self._test_move_layer_down(index),
                               state='normal' if index < len(self.layer_order) - 1 else 'disabled')
            down_btn.pack(side='top')

        except Exception as e:
            print(f"❌ 创建测试图层项失败: {e}")
    
    def _create_test_circular_button(self, parent, layer_key):
        """创建测试圆形按钮"""
        try:
            # 创建画布用于绘制圆形按钮
            canvas = tk.Canvas(parent, width=20, height=20, highlightthickness=0)
            canvas.pack(side='left', padx=(0, 5))
            
            # 根据当前状态确定颜色
            is_visible = self.layer_states[layer_key].get()
            fill_color = '#4CAF50' if is_visible else '#9E9E9E'  # 绿色或灰色
            
            # 绘制圆形
            circle = canvas.create_oval(2, 2, 18, 18, fill=fill_color, outline='#333333', width=1)
            
            # 点击事件处理
            def on_click(event):
                self._test_toggle_visibility(layer_key, canvas, circle)
            
            canvas.bind("<Button-1>", on_click)
            
        except Exception as e:
            print(f"❌ 创建测试圆形按钮失败: {e}")
    
    def _test_toggle_visibility(self, layer_key, canvas, circle):
        """测试切换可见性"""
        try:
            # 切换状态
            current_state = self.layer_states[layer_key].get()
            new_state = not current_state
            self.layer_states[layer_key].set(new_state)
            
            # 更新按钮颜色
            fill_color = '#4CAF50' if new_state else '#9E9E9E'  # 绿色或灰色
            canvas.itemconfig(circle, fill=fill_color)
            
            print(f"🔄 图层 {layer_key} 可见性: {'显示' if new_state else '隐藏'}")
            
        except Exception as e:
            print(f"❌ 测试切换可见性失败: {e}")
    
    def _test_move_layer_up(self, index):
        """测试向上移动图层"""
        try:
            if index > 0:
                # 交换顺序
                self.layer_order[index], self.layer_order[index - 1] = \
                    self.layer_order[index - 1], self.layer_order[index]
                
                # 重新创建图层项
                self._create_test_layer_items()
                
                print(f"📈 图层已向上移动: {self.layer_order}")
                
        except Exception as e:
            print(f"❌ 测试向上移动失败: {e}")
    
    def _test_move_layer_down(self, index):
        """测试向下移动图层"""
        try:
            if index < len(self.layer_order) - 1:
                # 交换顺序
                self.layer_order[index], self.layer_order[index + 1] = \
                    self.layer_order[index + 1], self.layer_order[index]
                
                # 重新创建图层项
                self._create_test_layer_items()
                
                print(f"📉 图层已向下移动: {self.layer_order}")
                
        except Exception as e:
            print(f"❌ 测试向下移动失败: {e}")
    
    def _test_apply_settings(self):
        """测试应用设置"""
        try:
            print("🔧 测试应用图层设置...")
            
            # 显示当前状态
            for layer_key, state_var in self.layer_states.items():
                is_visible = state_var.get()
                print(f"  - {layer_key}: {'显示' if is_visible else '隐藏'}")
            
            print(f"  - 图层顺序: {' → '.join(self.layer_order)}")
            
            tk.messagebox.showinfo("测试", "图层设置已应用（测试模式）")
            
        except Exception as e:
            print(f"❌ 测试应用设置失败: {e}")

if __name__ == "__main__":
    print("🚀 开始图层控制界面测试\n")
    
    # 导入messagebox
    from tkinter import messagebox
    
    # 运行测试
    success = test_layer_control_ui()
    
    if success:
        print("\n🎉 图层控制界面测试完成")
    else:
        print("\n⚠️ 图层控制界面测试失败")
