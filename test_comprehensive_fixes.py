#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试全面修复效果
验证所有文件中的数据清理修复是否生效
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_clean_group_data_methods():
    """测试所有文件是否都有_clean_group_data方法"""
    print("🧪 测试_clean_group_data方法存在性...")
    
    files_to_test = [
        'main_enhanced.py',
        'main_enhanced_merged.py',
        'main_enhanced_with_v2_fill.py'
    ]
    
    results = {}
    
    for filename in files_to_test:
        print(f"\n📁 测试文件: {filename}")
        
        try:
            # 动态导入模块
            module_name = filename.replace('.py', '')
            if module_name == 'main_enhanced':
                from main_enhanced import EnhancedCADProcessor
                processor = EnhancedCADProcessor(None, None)
            elif module_name == 'main_enhanced_merged':
                from main_enhanced_merged import EnhancedCADProcessor
                processor = EnhancedCADProcessor(None, None)
            elif module_name == 'main_enhanced_with_v2_fill':
                from main_enhanced_with_v2_fill import EnhancedCADAppV2
                # 创建应用实例来测试方法
                import tkinter as tk
                root = tk.Tk()
                root.withdraw()  # 隐藏窗口
                app = EnhancedCADAppV2(root)
                processor = app  # 使用应用实例
                root.destroy()
            else:
                continue
            
            # 检查是否有_clean_group_data方法
            if hasattr(processor, '_clean_group_data'):
                print(f"  ✅ 有_clean_group_data方法")
                
                # 测试方法功能
                test_group = [
                    {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
                    'invalid_string',
                    {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)]},
                    123,
                    None
                ]
                
                cleaned = processor._clean_group_data(test_group)
                if len(cleaned) == 2:
                    print(f"  ✅ 方法功能正常，清理了{len(test_group) - len(cleaned)}个无效实体")
                    results[filename] = 'success'
                else:
                    print(f"  ❌ 方法功能异常，应该保留2个实体，实际保留{len(cleaned)}个")
                    results[filename] = 'method_error'
            else:
                print(f"  ❌ 缺少_clean_group_data方法")
                results[filename] = 'missing_method'
                
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
            results[filename] = 'test_error'
    
    return results

def test_visualization_calls_with_cleaning():
    """测试可视化调用是否使用了数据清理"""
    print("\n🎨 测试可视化调用数据清理...")
    
    files_to_check = [
        'main_enhanced.py',
        'main_enhanced_merged.py'
    ]
    
    for filename in files_to_check:
        print(f"\n📁 检查文件: {filename}")
        
        if not os.path.exists(filename):
            print(f"  ⚠️ 文件不存在")
            continue
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有清理后的可视化调用
            cleaned_calls = content.count('visualize_entity_group(cleaned_group')
            total_entity_calls = content.count('visualize_entity_group(')
            
            cleaned_overview_calls = content.count('cleaned_group,  # 使用清理后的当前组')
            
            print(f"  📊 实体组可视化调用: {cleaned_calls}/{total_entity_calls} 使用了清理数据")
            print(f"  📊 全图概览调用: {cleaned_overview_calls} 个使用了清理数据")
            
            if cleaned_calls > 0:
                print(f"  ✅ 发现使用清理数据的可视化调用")
            else:
                print(f"  ⚠️ 未发现使用清理数据的可视化调用")
                
        except Exception as e:
            print(f"  ❌ 检查失败: {e}")

def test_problematic_data_handling():
    """测试问题数据处理"""
    print("\n🔧 测试问题数据处理...")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor(None, None)
        
        # 创建包含原始问题的测试数据
        problematic_data = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
            'index',  # 原始问题：字符串
            'total',  # 原始问题：字符串
            'entity_count',  # 原始问题：字符串
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)]},
            {'layer': 'A-WALL'},  # 缺少type
            {},  # 空字典
            None,  # None值
            123  # 数字
        ]
        
        print(f"  📋 原始问题数据: {len(problematic_data)} 个项目")
        
        # 测试清理
        cleaned_data = processor._clean_group_data(problematic_data)
        
        print(f"  ✨ 清理后数据: {len(cleaned_data)} 个有效实体")
        
        # 验证清理结果
        all_valid = all(
            isinstance(entity, dict) and 
            entity.get('type') and 
            entity.get('layer')
            for entity in cleaned_data
        )
        
        if all_valid and len(cleaned_data) == 2:
            print(f"  ✅ 问题数据处理正确")
            return True
        else:
            print(f"  ❌ 问题数据处理有误")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def simulate_original_error_scenario():
    """模拟原始错误场景"""
    print("\n🎭 模拟原始错误场景...")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        from cad_visualizer import CADVisualizer
        
        processor = EnhancedCADProcessor(None, None)
        visualizer = CADVisualizer()
        
        # 模拟原始错误：包含字符串的组数据
        error_group = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
            'index',  # 这会导致原始错误
            'total',
            'entity_count',
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)]}
        ]
        
        print(f"  📋 模拟错误组数据: {len(error_group)} 个项目")
        
        # 测试修复后的处理流程
        print("  🔧 使用修复后的处理流程...")
        
        # 1. 清理数据
        cleaned_group = processor._clean_group_data(error_group)
        print(f"  ✨ 清理后: {len(cleaned_group)} 个有效实体")
        
        # 2. 传递给可视化器（应该不会出现字符串实体警告）
        print("  🎨 测试可视化器...")
        visualizer.visualize_entity_group(cleaned_group, {'wall': '墙体'})
        
        print("  ✅ 原始错误场景修复成功，无字符串实体警告")
        return True
        
    except Exception as e:
        print(f"  ❌ 模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CAD分类标注工具 - 全面修复效果测试")
    print("=" * 60)
    
    # 1. 测试清理方法
    method_results = test_clean_group_data_methods()
    
    # 2. 测试可视化调用
    test_visualization_calls_with_cleaning()
    
    # 3. 测试问题数据处理
    data_handling_ok = test_problematic_data_handling()
    
    # 4. 模拟原始错误场景
    scenario_ok = simulate_original_error_scenario()
    
    print("\n" + "=" * 60)
    print("修复效果总结:")
    print("=" * 60)
    
    # 统计结果
    success_count = sum(1 for result in method_results.values() if result == 'success')
    total_files = len(method_results)
    
    print(f"📊 数据清理方法: {success_count}/{total_files} 个文件修复成功")
    
    for filename, result in method_results.items():
        status = "✅" if result == 'success' else "❌"
        print(f"  {status} {filename}: {result}")
    
    if data_handling_ok:
        print("✅ 问题数据处理测试通过")
    else:
        print("❌ 问题数据处理测试失败")
    
    if scenario_ok:
        print("✅ 原始错误场景修复验证通过")
    else:
        print("❌ 原始错误场景修复验证失败")
    
    # 总体评估
    all_success = (success_count == total_files and data_handling_ok and scenario_ok)
    
    if all_success:
        print("\n🎉 全面修复成功！")
        print("原始问题 '⚠️ 跳过非字典实体: <class 'str'> - index' 已在所有相关文件中修复。")
    else:
        print("\n⚠️ 部分修复完成，可能还需要进一步处理。")
    
    print("=" * 60)
