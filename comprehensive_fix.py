#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面修复图层识别和分组问题
确保所有分组方法返回统一的字典格式，并修复组信息更新问题
"""

import os
import sys

def fix_group_entities_method():
    """修复group_entities方法，确保返回统一的字典格式"""
    print("🔧 修复group_entities方法...")
    
    try:
        # 读取cad_data_processor.py文件
        with open('cad_data_processor.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 找到group_entities方法的return语句
        method_start = content.find('def group_entities(self, entities, distance_threshold=20, debug=False):')
        if method_start != -1:
            # 找到方法的最后一个return语句
            return_pos = content.find('return all_groups', method_start)
            if return_pos != -1:
                # 在return之前添加数据结构统一化代码
                insert_pos = content.rfind('\n', method_start, return_pos)
                if insert_pos != -1:
                    unification_code = '''
        # 统一化所有分组的数据结构为字典格式
        unified_groups = []
        for i, group in enumerate(all_groups):
            if isinstance(group, list):
                # 如果是列表，转换为字典格式
                dict_group = {
                    'entities': group,
                    'label': f'group_{i}',
                    'group_type': 'general',
                    'status': 'pending',
                    'confidence': 0.5
                }
                unified_groups.append(dict_group)
            elif isinstance(group, dict):
                # 如果已经是字典，确保有必要的字段
                if 'entities' not in group:
                    group['entities'] = []
                if 'label' not in group:
                    group['label'] = f'group_{i}'
                if 'group_type' not in group:
                    group['group_type'] = 'general'
                if 'status' not in group:
                    group['status'] = 'pending'
                if 'confidence' not in group:
                    group['confidence'] = 0.5
                unified_groups.append(group)
            else:
                print(f"  警告：跳过无效的组类型: {type(group)}")
        
        all_groups = unified_groups
        print(f"  数据结构统一化完成: {len(all_groups)}个字典格式的组")
'''
                    content = content[:insert_pos] + unification_code + content[insert_pos:]
        
        # 保存修改后的文件
        with open('cad_data_processor.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("  ✅ group_entities方法修复完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复group_entities方法失败: {e}")
        return False

def fix_update_groups_info_method():
    """修复_update_groups_info方法"""
    print("🔧 修复_update_groups_info方法...")
    
    try:
        # 读取main_enhanced.py文件
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找_update_groups_info方法
        method_start = content.find('def _update_groups_info(self):')
        if method_start != -1:
            # 找到方法结束位置
            method_end = content.find('\n    def ', method_start + 1)
            if method_end == -1:
                method_end = content.find('\nclass ', method_start + 1)
            if method_end == -1:
                method_end = len(content)
            
            # 替换方法内容
            new_method = '''def _update_groups_info(self):
        """更新组信息"""
        try:
            self.groups_info = []
            
            if not hasattr(self, 'all_groups') or not self.all_groups:
                print("  ⚠️ 没有分组数据，无法更新组信息")
                return
            
            print(f"  开始更新组信息，共 {len(self.all_groups)} 个组")
            
            for i, group in enumerate(self.all_groups):
                if isinstance(group, dict):
                    entities = group.get('entities', [])
                    group_info = {
                        'index': i,
                        'label': group.get('label', f'group_{i}'),
                        'entity_count': len(entities),
                        'status': group.get('status', 'pending'),
                        'confidence': group.get('confidence', 0.0),
                        'group_type': group.get('group_type', 'unknown'),
                        'layer': group.get('layer', 'unknown')
                    }
                    self.groups_info.append(group_info)
                else:
                    print(f"    警告：组 {i} 不是字典类型: {type(group)}")
                    # 尝试处理非字典类型的组
                    if isinstance(group, list):
                        group_info = {
                            'index': i,
                            'label': f'group_{i}',
                            'entity_count': len(group),
                            'status': 'pending',
                            'confidence': 0.0,
                            'group_type': 'unknown',
                            'layer': 'unknown'
                        }
                        self.groups_info.append(group_info)
            
            print(f"  ✅ 组信息更新完成: {len(self.groups_info)}个组")
            
        except Exception as e:
            print(f"  ❌ 更新组信息失败: {e}")
            import traceback
            traceback.print_exc()
            self.groups_info = []

    '''
            content = content[:method_start] + new_method + content[method_end:]
        else:
            # 如果方法不存在，添加它
            print("  添加缺失的_update_groups_info方法...")
            
            # 在类的末尾添加方法
            class_end = content.rfind('class EnhancedCADProcessor')
            if class_end != -1:
                # 找到类的结束位置
                next_class = content.find('\nclass ', class_end + 1)
                if next_class == -1:
                    next_class = len(content)
                
                # 在类结束前插入方法
                content = content[:next_class] + new_method + content[next_class:]
        
        # 保存修改后的文件
        with open('main_enhanced.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("  ✅ _update_groups_info方法修复完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复_update_groups_info方法失败: {e}")
        return False

def fix_process_single_file_method():
    """修复process_single_file方法中的组信息更新调用"""
    print("🔧 修复process_single_file方法...")
    
    try:
        # 读取main_enhanced.py文件
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找process_single_file方法中的组信息更新部分
        update_call = content.find('self._update_groups_info()')
        if update_call == -1:
            # 如果没有调用，在适当位置添加
            success_return = content.find('return True', content.find('def process_single_file'))
            if success_return != -1:
                # 在return True之前添加组信息更新
                insert_pos = content.rfind('\n', 0, success_return)
                if insert_pos != -1:
                    update_code = '''
        # 更新组信息以供UI显示
        self._update_groups_info()
'''
                    content = content[:insert_pos] + update_code + content[insert_pos:]
        
        # 保存修改后的文件
        with open('main_enhanced.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("  ✅ process_single_file方法修复完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复process_single_file方法失败: {e}")
        return False

def test_comprehensive_fix():
    """测试全面修复效果"""
    print("🧪 测试全面修复效果...")
    
    try:
        # 重新导入模块
        import importlib
        
        # 清除模块缓存
        modules_to_reload = ['main_enhanced', 'cad_data_processor']
        for module_name in modules_to_reload:
            if module_name in sys.modules:
                importlib.reload(sys.modules[module_name])
        
        # 测试CAD数据处理器
        from cad_data_processor import CADDataProcessor
        cad_processor = CADDataProcessor()
        
        # 创建测试数据
        test_entities = [
            {'type': 'LINE', 'layer': '0', 'start': (0, 0), 'end': (10, 0)},
            {'type': 'LINE', 'layer': '0', 'start': (10, 0), 'end': (20, 0)},
            {'type': 'CIRCLE', 'layer': '0', 'center': (5, 5), 'radius': 2}
        ]
        
        # 测试分组
        groups = cad_processor.group_entities(test_entities, distance_threshold=20, debug=False)
        
        print(f"  测试分组结果: {len(groups)}个组")
        
        # 检查数据结构
        all_dict = True
        for i, group in enumerate(groups):
            if not isinstance(group, dict):
                print(f"    ❌ 组 {i} 不是字典类型: {type(group)}")
                all_dict = False
            else:
                required_keys = ['entities', 'label', 'group_type']
                missing_keys = [key for key in required_keys if key not in group]
                if missing_keys:
                    print(f"    ⚠️ 组 {i} 缺少键: {missing_keys}")
                else:
                    print(f"    ✅ 组 {i} 数据结构正确: {group['label']}")
        
        # 测试增强处理器
        from main_enhanced import EnhancedCADProcessor
        enhanced_processor = EnhancedCADProcessor(None, None)
        
        # 模拟设置分组数据
        enhanced_processor.all_groups = groups
        
        # 测试组信息更新
        enhanced_processor._update_groups_info()
        
        if hasattr(enhanced_processor, 'groups_info') and enhanced_processor.groups_info:
            print(f"  ✅ 组信息更新成功: {len(enhanced_processor.groups_info)}个组信息")
            for info in enhanced_processor.groups_info:
                print(f"    组信息: {info['label']} ({info['entity_count']}个实体)")
        else:
            print(f"  ❌ 组信息更新失败")
            all_dict = False
        
        return all_dict
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主修复函数"""
    print("🚀 开始全面修复图层识别和分组问题")
    print("="*60)
    
    fixes = [
        ("修复group_entities方法", fix_group_entities_method),
        ("修复_update_groups_info方法", fix_update_groups_info_method),
        ("修复process_single_file方法", fix_process_single_file_method),
        ("测试全面修复效果", test_comprehensive_fix)
    ]
    
    success_count = 0
    
    for fix_name, fix_func in fixes:
        print(f"\n{fix_name}...")
        try:
            if fix_func():
                success_count += 1
                print(f"✅ {fix_name}成功")
            else:
                print(f"❌ {fix_name}失败")
        except Exception as e:
            print(f"❌ {fix_name}异常: {e}")
    
    print(f"\n" + "="*60)
    print("📋 全面修复总结:")
    print("="*60)
    print(f"成功修复: {success_count}/{len(fixes)}")
    
    if success_count == len(fixes):
        print("🎉 全面修复完成！")
        print("\n💡 修复内容:")
        print("  1. 统一所有分组方法的返回数据结构为字典格式")
        print("  2. 修复组信息更新方法，支持各种数据类型")
        print("  3. 确保process_single_file正确调用组信息更新")
        print("  4. 添加详细的错误处理和调试信息")
        
        print("\n🔄 现在可以重新运行应用，图层识别和分组应该正常工作")
    else:
        print("⚠️ 部分修复失败，请检查错误信息")
    
    return success_count == len(fixes)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
