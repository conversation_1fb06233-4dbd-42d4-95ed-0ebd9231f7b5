#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试界面布局修复
验证左侧控制面板滚动和图像控制区域布局优化是否正常工作
"""

import os
import sys
import tkinter as tk
from tkinter import ttk

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_left_panel_scrolling():
    """测试左侧控制面板滚动功能"""
    print("🧪 测试左侧控制面板滚动功能...")
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("测试左侧面板滚动")
        root.geometry("800x600")
        
        # 创建主框架
        main_frame = tk.Frame(root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 创建带滚动功能的左侧控制面板
        scroll_container = tk.Frame(main_frame, width=400)
        scroll_container.pack(side='left', fill='y', padx=(0, 10))
        
        # 创建Canvas和Scrollbar
        canvas = tk.Canvas(scroll_container, highlightthickness=0, width=380)
        scrollbar = tk.Scrollbar(scroll_container, orient="vertical", command=canvas.yview)
        
        # 创建可滚动的框架
        scrollable_frame = tk.Frame(canvas)
        
        # 配置滚动
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 添加多个测试控件（模拟实际的控制面板内容）
        test_sections = [
            "1. 数据文件夹",
            "2. 处理控制", 
            "3. 处理状态",
            "4. 实体组列表",
            "5. 选择类别",
            "6. 操作控制",
            "7. 保存选项"
        ]
        
        for i, section in enumerate(test_sections):
            # 创建区域框架
            section_frame = tk.LabelFrame(scrollable_frame, text=section, font=('Arial', 10, 'bold'))
            section_frame.pack(fill='x', pady=5, padx=5)
            
            # 添加一些测试控件
            for j in range(3):
                if section == "4. 实体组列表":
                    # 模拟组列表
                    tree = ttk.Treeview(section_frame, height=8)
                    tree.pack(fill='x', padx=5, pady=2)
                    for k in range(10):
                        tree.insert('', 'end', text=f'组 {k+1}', values=(f'状态{k%3}', f'类型{k%2}', f'{k+5}'))
                else:
                    # 普通按钮
                    btn = tk.Button(section_frame, text=f"{section} - 按钮 {j+1}")
                    btn.pack(fill='x', padx=5, pady=2)
        
        # 布局滚动组件
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        canvas.bind("<MouseWheel>", _on_mousewheel)
        scrollable_frame.bind("<MouseWheel>", _on_mousewheel)
        
        # 右侧测试区域
        right_frame = tk.Frame(main_frame, bg='lightgray')
        right_frame.pack(side='right', fill='both', expand=True)
        
        test_label = tk.Label(right_frame, text="右侧测试区域\n左侧面板应该可以滚动", 
                             font=('Arial', 12), bg='lightgray')
        test_label.pack(expand=True)
        
        print("✅ 左侧控制面板滚动测试窗口创建成功")
        print("   请手动测试滚动功能，然后关闭窗口")
        
        # 运行测试窗口
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 左侧控制面板滚动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_image_control_layout():
    """测试图像控制区域布局优化"""
    print("\n🧪 测试图像控制区域布局优化...")
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("测试图像控制区域布局")
        root.geometry("800x600")
        
        # 创建主容器
        main_container = tk.Frame(root)
        main_container.pack(fill='both', expand=True, padx=5, pady=5)
        
        # 配置grid权重 - 优化布局：优先保证图像控制区域完整显示
        main_container.grid_rowconfigure(0, weight=3)  # 上排权重3（图像预览区域）
        main_container.grid_rowconfigure(1, weight=0, minsize=450)  # 下排固定最小高度450px
        main_container.grid_columnconfigure(0, weight=1)  # 左列权重1
        main_container.grid_columnconfigure(1, weight=1)  # 右列权重1
        
        # 区域1：图像预览（左上）
        detail_frame = tk.Frame(main_container, relief='ridge', bd=2, bg='lightblue')
        detail_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 2), pady=(0, 2))
        
        detail_label = tk.Label(detail_frame, text="图像预览区域\n（可变高度）", 
                               font=('Arial', 12), bg='lightblue')
        detail_label.pack(expand=True)
        
        # 区域2：填充控制（右上）
        overview_frame = tk.Frame(main_container, relief='ridge', bd=2, bg='lightgreen')
        overview_frame.grid(row=0, column=1, sticky='nsew', padx=(2, 0), pady=(0, 2))
        
        overview_label = tk.Label(overview_frame, text="填充控制区域\n（可变高度）", 
                                 font=('Arial', 12), bg='lightgreen')
        overview_label.pack(expand=True)
        
        # 区域3：图像控制（左下）
        zoom_frame = tk.Frame(main_container, relief='ridge', bd=2, bg='lightcoral')
        zoom_frame.grid(row=1, column=0, sticky='nsew', padx=(0, 2), pady=(2, 0))
        
        # 创建图像控制内容
        control_container = tk.Frame(zoom_frame)
        control_container.pack(fill='both', expand=True, padx=2, pady=2)
        
        # 配置图像控制内部布局
        control_container.grid_rowconfigure(0, weight=0, minsize=120)  # 按钮区域优先
        control_container.grid_rowconfigure(1, weight=1)  # 图层控制区域
        control_container.grid_columnconfigure(0, weight=3)  # 左侧更宽
        control_container.grid_columnconfigure(1, weight=1)  # 右侧
        
        # 按钮控制区域（优先显示）
        buttons_frame = tk.Frame(control_container, relief='ridge', bd=1, bg='orange')
        buttons_frame.grid(row=0, column=1, sticky='nsew', padx=(1, 0), pady=(0, 1))
        
        buttons_label = tk.Label(buttons_frame, text="按钮控制区域\n（优先显示，固定高度120px）", 
                                font=('Arial', 10), bg='orange')
        buttons_label.pack(expand=True)
        
        # 图层控制区域
        layers_frame = tk.Frame(control_container, relief='ridge', bd=1, bg='yellow')
        layers_frame.grid(row=1, column=0, columnspan=2, sticky='nsew', pady=(1, 0))
        
        layers_label = tk.Label(layers_frame, text="图层控制区域\n（使用剩余空间）", 
                               font=('Arial', 10), bg='yellow')
        layers_label.pack(expand=True)
        
        # 区域4：配色系统（右下）
        color_frame = tk.Frame(main_container, relief='ridge', bd=2, bg='lightyellow')
        color_frame.grid(row=1, column=1, sticky='nsew', padx=(2, 0), pady=(2, 0))
        
        color_label = tk.Label(color_frame, text="配色系统区域\n（固定高度450px）", 
                              font=('Arial', 12), bg='lightyellow')
        color_label.pack(expand=True)
        
        print("✅ 图像控制区域布局测试窗口创建成功")
        print("   请检查布局是否符合要求：")
        print("   - 下排区域固定最小高度450px")
        print("   - 按钮控制区域优先显示（固定120px高度）")
        print("   - 图层控制区域使用剩余空间")
        
        # 运行测试窗口
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 图像控制区域布局测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始界面布局修复测试...")
    
    # 测试左侧控制面板滚动
    success1 = test_left_panel_scrolling()
    
    # 测试图像控制区域布局
    success2 = test_image_control_layout()
    
    if success1 and success2:
        print("\n🎉 界面布局修复测试完成！")
        print("✅ 左侧控制面板滚动功能正常")
        print("✅ 图像控制区域布局优化正常")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
