#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复视图显示所有组的问题
确保在文件加载后，视图中显示所有组（墙体、门窗等），而不只是当前选中的组
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_view_display_issue():
    """分析视图显示问题"""
    print("🔍 分析视图显示问题")
    print("="*60)
    
    print("📊 问题分析:")
    print("   现象: 视图中只显示门窗组，不显示墙体组")
    print("   原因分析:")
    print("   1. _show_group 方法只显示当前选中的组")
    print("   2. visualize_overview 的 current_group_entities 参数只传递了当前组")
    print("   3. 可能缺少显示所有组的逻辑")
    
    print(f"\n🎯 解决方案:")
    print("   1. 修改 _show_group 方法，确保显示所有组")
    print("   2. 改进 visualize_overview 调用，传递完整数据")
    print("   3. 添加文件加载后的完整视图刷新")
    print("   4. 确保所有组都能在全图概览中显示")

def fix_show_group_method():
    """修复 _show_group 方法，确保显示所有组"""
    print(f"\n🔧 修复 _show_group 方法")
    print("="*60)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复1：改进全图概览调用，确保显示所有组
        print("1. 改进全图概览调用...")
        
        old_overview_call = '''                if current_file_entities:
                    # 🔑 关键修复：传递组索引参数，使用清理后的组数据
                    self.processor.visualizer.visualize_overview(
                        current_file_entities,
                        cleaned_group,  # 使用清理后的当前组
                        auto_labeled_entities + labeled_entities,
                        processor=self.processor,
                        current_group_index=group_index  # 🔑 传递组索引以显示高亮
                    )
                else:
                    print("  ⚠️ 没有实体数据，跳过全图概览更新")'''
        
        new_overview_call = '''                if current_file_entities:
                    # 🔑 关键修复：显示所有组，高亮当前组
                    print(f"  🌍 显示全图概览: 总实体={len(current_file_entities)}, 当前组实体={len(cleaned_group)}")
                    
                    # 获取所有组信息用于完整显示
                    all_groups = getattr(self.processor, 'all_groups', [])
                    print(f"  📊 所有组数: {len(all_groups)}")
                    
                    self.processor.visualizer.visualize_overview(
                        current_file_entities,  # 显示所有实体
                        cleaned_group,  # 高亮当前组
                        auto_labeled_entities + labeled_entities,  # 已标注实体
                        processor=self.processor,  # 传递处理器以获取组信息
                        current_group_index=group_index  # 当前组索引用于高亮
                    )
                    print(f"  ✅ 全图概览更新完成")
                else:
                    print("  ⚠️ 没有实体数据，跳过全图概览更新")'''
        
        if old_overview_call in content:
            content = content.replace(old_overview_call, new_overview_call)
            print("  ✅ 已改进全图概览调用")
        
        # 修复2：添加调试信息，帮助诊断问题
        print("2. 添加调试信息...")
        
        old_debug_info = '''                print(f"  数据检查: 总实体={len(current_file_entities)}, 自动标注={len(auto_labeled_entities)}, 已标注={len(labeled_entities)}")'''
        
        new_debug_info = '''                print(f"  数据检查: 总实体={len(current_file_entities)}, 自动标注={len(auto_labeled_entities)}, 已标注={len(labeled_entities)}")
                
                # 🔍 详细调试信息
                if current_file_entities:
                    # 统计实体类型
                    entity_types = {}
                    entity_layers = {}
                    for entity in current_file_entities:
                        if isinstance(entity, dict):
                            entity_type = entity.get('type', 'unknown')
                            entity_layer = entity.get('layer', 'unknown')
                            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
                            entity_layers[entity_layer] = entity_layers.get(entity_layer, 0) + 1
                    
                    print(f"  📊 实体类型统计: {entity_types}")
                    print(f"  📊 图层统计: {entity_layers}")
                    
                    # 统计组信息
                    all_groups = getattr(self.processor, 'all_groups', [])
                    if all_groups:
                        print(f"  📊 组统计: 共{len(all_groups)}个组")
                        for i, group in enumerate(all_groups[:5]):  # 只显示前5个组
                            group_size = len([e for e in group if isinstance(e, dict)])
                            print(f"    组{i+1}: {group_size}个实体")
                    else:
                        print(f"  ⚠️ 没有组数据")'''
        
        if old_debug_info in content:
            content = content.replace(old_debug_info, new_debug_info)
            print("  ✅ 已添加详细调试信息")
        
        # 保存修改后的文件
        with open('main_enhanced_with_v2_fill.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ _show_group 方法修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def add_complete_view_refresh():
    """添加完整的视图刷新方法"""
    print(f"\n🔧 添加完整的视图刷新方法")
    print("="*60)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加完整视图刷新方法
        refresh_method = '''
    def _refresh_complete_view(self):
        """刷新完整视图，显示所有组"""
        try:
            print("🔄 刷新完整视图...")
            
            if not self.processor or not hasattr(self.processor, 'current_file_entities'):
                print("  ⚠️ 处理器或实体数据不存在")
                return False
            
            current_file_entities = getattr(self.processor, 'current_file_entities', [])
            auto_labeled_entities = getattr(self.processor, 'auto_labeled_entities', [])
            labeled_entities = getattr(self.processor, 'labeled_entities', [])
            all_groups = getattr(self.processor, 'all_groups', [])
            
            print(f"  📊 数据统计:")
            print(f"    总实体: {len(current_file_entities)}")
            print(f"    总组数: {len(all_groups)}")
            print(f"    已标注实体: {len(auto_labeled_entities + labeled_entities)}")
            
            if not current_file_entities:
                print("  ⚠️ 没有实体数据")
                return False
            
            # 使用可视化器显示完整视图
            if (hasattr(self.processor, 'visualizer') and self.processor.visualizer and
                hasattr(self, 'canvas') and self.canvas):
                
                # 显示全图概览（不高亮任何特定组）
                self.processor.visualizer.visualize_overview(
                    current_file_entities,  # 所有实体
                    None,  # 不高亮特定组
                    auto_labeled_entities + labeled_entities,  # 已标注实体
                    processor=self.processor  # 处理器信息
                )
                
                # 刷新画布
                self.processor.visualizer.update_canvas(self.canvas)
                
                print("  ✅ 完整视图刷新成功")
                return True
            else:
                print("  ⚠️ 可视化器或画布未初始化")
                return False
                
        except Exception as e:
            print(f"  ❌ 完整视图刷新失败: {e}")
            import traceback
            traceback.print_exc()
            return False

'''
        
        # 在 _show_group 方法前插入
        insertion_point = "    def _show_group(self, group, group_index=None):"
        if insertion_point in content:
            content = content.replace(insertion_point, refresh_method + insertion_point)
            print("  ✅ 已添加完整视图刷新方法")
        
        # 保存修改后的文件
        with open('main_enhanced_with_v2_fill.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 完整视图刷新方法添加完成")
        return True
        
    except Exception as e:
        print(f"❌ 添加失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_file_load_view_update():
    """修复文件加载后的视图更新"""
    print(f"\n🔧 修复文件加载后的视图更新")
    print("="*60)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复文件加载后的视图更新
        print("1. 修复文件加载后的视图更新...")
        
        old_load_complete = '''            print(f"✅ 文件 {file_name} 数据加载和界面更新完成")
            return True'''
        
        new_load_complete = '''            # 🔧 修复：文件加载完成后刷新完整视图
            print(f"🔄 文件加载完成，刷新完整视图...")
            self._refresh_complete_view()
            
            print(f"✅ 文件 {file_name} 数据加载和界面更新完成")
            return True'''
        
        if old_load_complete in content:
            content = content.replace(old_load_complete, new_load_complete)
            print("  ✅ 已修复文件加载后的视图更新")
        
        # 修复2：在没有待处理组时也显示完整视图
        print("2. 修复没有待处理组时的视图显示...")
        
        old_no_pending = '''                print("  所有组都已标注完成")

                # 🔑 关键修复：禁用手动标注模式
                if hasattr(self.processor, 'manual_grouping_mode'):
                    self.processor.manual_grouping_mode = False
                    print(f"  🔧 禁用手动标注模式")

                # 恢复可视化器状态并显示当前组
                if self.processor.visualizer and self.canvas:
                    try:
                        # 显示当前组（这会更新预览图和全图概览）
                        if (hasattr(self.processor, 'all_groups') and
                            self.processor.all_groups and
                            self.processor.current_group_index < len(self.processor.all_groups)):
                            current_group = self.processor.all_groups[self.processor.current_group_index]
                            group_index = self.processor.current_group_index + 1

                            # 调用父类的显示组方法
                            self._show_group(current_group, group_index)
                            print(f"  显示当前组: 组{group_index}")
                        else:
                            # 如果没有组，至少显示全图概览
                            if self.processor.current_file_entities:
                                self.processor.visualizer.visualize_overview(
                                    self.processor.current_file_entities,
                                    None,  # 没有当前组
                                    self.processor.labeled_entities,  # 已标注的实体
                                    processor=self.processor
                                )
                                self.processor.visualizer.update_canvas(self.canvas)
                                print(f"  显示全图概览")

                        print(f"  可视化更新完成")
                    except Exception as e:
                        print(f"  更新可视化失败: {e}")'''
        
        new_no_pending = '''                print("  所有组都已标注完成")

                # 🔑 关键修复：禁用手动标注模式
                if hasattr(self.processor, 'manual_grouping_mode'):
                    self.processor.manual_grouping_mode = False
                    print(f"  🔧 禁用手动标注模式")

                # 🔧 修复：显示完整视图而不是单个组
                print(f"  🌍 显示完整视图（所有组都已标注）")
                self._refresh_complete_view()'''
        
        if old_no_pending in content:
            content = content.replace(old_no_pending, new_no_pending)
            print("  ✅ 已修复没有待处理组时的视图显示")
        
        # 保存修改后的文件
        with open('main_enhanced_with_v2_fill.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 文件加载后的视图更新修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_view_display_test():
    """创建视图显示测试"""
    print(f"\n🧪 创建视图显示测试")
    print("="*60)
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试视图显示所有组
验证修复后是否能正确显示所有组（墙体、门窗等）
"""

import sys
import os
import tkinter as tk
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_view_display_all_groups():
    """测试视图显示所有组"""
    print("🧪 测试视图显示所有组")
    print("="*50)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        print("1. 导入并创建应用...")
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        app = EnhancedCADAppV2(root)
        print("  ✅ 应用创建成功")
        
        print("2. 创建测试数据...")
        # 创建包含墙体和门窗的测试数据
        test_entities = [
            # 墙体实体
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': 'wall'},
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'label': 'wall'},
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 100), (0, 100)], 'label': 'wall'},
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 100), (0, 0)], 'label': 'wall'},
            
            # 门窗实体
            {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(20, -5), (20, 5)], 'label': 'door_window'},
            {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(80, -5), (80, 5)], 'label': 'door_window'},
        ]
        
        test_groups = [
            # 墙体组
            [test_entities[0], test_entities[1], test_entities[2], test_entities[3]],
            # 门窗组
            [test_entities[4], test_entities[5]]
        ]
        
        # 设置测试数据
        test_file = "test_display.dxf"
        app.file_data[test_file] = {
            'entities': test_entities,
            'all_groups': test_groups,
            'auto_labeled_entities': test_entities,
            'labeled_entities': [],
            'groups_info': [
                {'status': 'auto_labeled', 'group_type': 'wall', 'entity_count': 4},
                {'status': 'auto_labeled', 'group_type': 'door_window', 'entity_count': 2}
            ]
        }
        
        print("3. 加载测试文件...")
        app._load_file_data(test_file)
        
        print("4. 检查处理器数据...")
        if app.processor:
            current_entities = getattr(app.processor, 'current_file_entities', [])
            all_groups = getattr(app.processor, 'all_groups', [])
            
            print(f"  总实体数: {len(current_entities)}")
            print(f"  总组数: {len(all_groups)}")
            
            # 统计实体类型
            entity_types = {}
            for entity in current_entities:
                if isinstance(entity, dict):
                    entity_type = entity.get('type', 'unknown')
                    entity_layer = entity.get('layer', 'unknown')
                    key = f"{entity_type}({entity_layer})"
                    entity_types[key] = entity_types.get(key, 0) + 1
            
            print(f"  实体类型分布: {entity_types}")
            
            # 统计组内容
            for i, group in enumerate(all_groups):
                group_entities = [e for e in group if isinstance(e, dict)]
                group_layers = set(e.get('layer', 'unknown') for e in group_entities)
                print(f"  组{i+1}: {len(group_entities)}个实体, 图层: {group_layers}")
        
        print("5. 测试完整视图刷新...")
        if hasattr(app, '_refresh_complete_view'):
            success = app._refresh_complete_view()
            print(f"  完整视图刷新: {'✅ 成功' if success else '❌ 失败'}")
        else:
            print("  ⚠️ _refresh_complete_view 方法不存在")
        
        print("6. 测试组显示...")
        if app.processor and hasattr(app.processor, 'all_groups') and app.processor.all_groups:
            for i, group in enumerate(app.processor.all_groups):
                try:
                    app._show_group(group, i + 1)
                    print(f"  组{i+1} 显示: ✅ 成功")
                except Exception as e:
                    print(f"  组{i+1} 显示: ❌ 失败 - {e}")
        
        # 清理
        root.destroy()
        
        print("\\n🎉 视图显示测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_view_display_all_groups()
'''
    
    with open('test_view_display_all_groups.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 视图显示测试已创建: test_view_display_all_groups.py")

def main():
    """主修复函数"""
    print("🚀 开始修复视图显示所有组的问题")
    
    try:
        # 1. 分析问题
        analyze_view_display_issue()
        
        # 2. 修复 _show_group 方法
        fix1_success = fix_show_group_method()
        
        # 3. 添加完整视图刷新方法
        fix2_success = add_complete_view_refresh()
        
        # 4. 修复文件加载后的视图更新
        fix3_success = fix_file_load_view_update()
        
        # 5. 创建测试
        create_view_display_test()
        
        print(f"\n" + "="*60)
        print(f"📊 修复结果总结:")
        print(f"  _show_group 方法修复: {'✅ 成功' if fix1_success else '❌ 失败'}")
        print(f"  完整视图刷新方法: {'✅ 成功' if fix2_success else '❌ 失败'}")
        print(f"  文件加载视图更新: {'✅ 成功' if fix3_success else '❌ 失败'}")
        print(f"  测试脚本: ✅ 已创建")
        
        if all([fix1_success, fix2_success, fix3_success]):
            print(f"\n🎯 修复内容:")
            print(f"   1. 改进了全图概览调用，确保显示所有实体")
            print(f"   2. 添加了详细的调试信息")
            print(f"   3. 添加了完整视图刷新方法")
            print(f"   4. 修复了文件加载后的视图更新逻辑")
            
            print(f"\n🧪 测试步骤:")
            print(f"   1. 运行测试: python test_view_display_all_groups.py")
            print(f"   2. 运行主程序: python main_enhanced_with_v2_fill.py")
            print(f"   3. 加载DXF文件，观察是否显示所有组")
            print(f"   4. 检查控制台调试信息")
            
            print(f"\n🎉 预期效果:")
            print(f"   - 视图中显示所有组（墙体、门窗等）")
            print(f"   - 不同类型的实体有不同的颜色")
            print(f"   - 当前选中组有高亮显示")
            print(f"   - 详细的调试信息帮助诊断问题")
        else:
            print(f"\n❌ 部分修复失败，请检查错误信息")
        
    except Exception as e:
        print(f"❌ 修复过程失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
