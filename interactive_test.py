#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交互式测试代码 - 测试三阶段处理流程
包含模拟数据和用户交互界面
"""

import os
import sys
import json
import time
import threading
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from tkinter import messagebox, ttk, Frame, Label, Button
from main_enhanced_with_v2_fill import EnhancedCADAppV2

class InteractiveTestUI:
    """交互式测试界面"""
    
    def __init__(self):
        self.test_window = None
        self.app_window = None
        self.app = None
        self.test_folder = "interactive_test_data"
        self.test_log = []
        
    def create_test_data(self):
        """创建测试数据"""
        print("🔧 创建测试数据...")
        
        if not os.path.exists(self.test_folder):
            os.makedirs(self.test_folder)
        
        # 创建模拟DXF文件
        test_file = os.path.join(self.test_folder, "test_building.dxf")
        
        # 创建包含多图层线条的测试数据
        test_entities = self.generate_multi_layer_entities()
        
        # 保存测试数据（JSON格式，模拟DXF解析结果）
        with open(test_file + ".json", 'w', encoding='utf-8') as f:
            json.dump(test_entities, f, ensure_ascii=False, indent=2)
        
        # 创建占位符DXF文件
        with open(test_file, 'w') as f:
            f.write("# Test DXF file\n")
        
        print(f"✅ 测试数据创建完成: {test_file}")
        return self.test_folder
    
    def generate_multi_layer_entities(self):
        """生成多图层实体数据"""
        return {
            "entities": [
                # 墙体图层 - 外墙矩形
                {"type": "LINE", "layer": "WALL", "points": [(0, 0), (200, 0)], "color": 1},
                {"type": "LINE", "layer": "WALL", "points": [(200, 0), (200, 150)], "color": 1},
                {"type": "LINE", "layer": "WALL", "points": [(200, 150), (0, 150)], "color": 1},
                {"type": "LINE", "layer": "WALL", "points": [(0, 150), (0, 0)], "color": 1},
                
                # 墙体图层 - 内墙
                {"type": "LINE", "layer": "WALL", "points": [(100, 0), (100, 75)], "color": 1},
                {"type": "LINE", "layer": "WALL", "points": [(0, 75), (200, 75)], "color": 1},
                
                # 门窗图层
                {"type": "ARC", "layer": "DOOR", "center": (50, 0), "radius": 25, "start_angle": 0, "end_angle": 90, "color": 2},
                {"type": "RECTANGLE", "layer": "WINDOW", "points": [(150, 148), (180, 152)], "color": 3},
                {"type": "RECTANGLE", "layer": "WINDOW", "points": [(98, 100), (102, 130)], "color": 3},
                
                # 栏杆图层
                {"type": "LWPOLYLINE", "layer": "RAILING", "points": [(30, 120), (70, 120), (70, 140), (30, 140), (30, 120)], "closed": True, "color": 4},
                
                # 家具图层
                {"type": "CIRCLE", "layer": "FURNITURE", "center": (50, 100), "radius": 15, "color": 5},
                {"type": "RECTANGLE", "layer": "FURNITURE", "points": [(120, 20), (180, 50)], "color": 5},
                {"type": "CIRCLE", "layer": "FURNITURE", "center": (150, 100), "radius": 12, "color": 5},
                
                # 尺寸标注图层
                {"type": "TEXT", "layer": "DIMENSION", "position": (100, 160), "text": "20000", "height": 3, "color": 6},
                {"type": "LINE", "layer": "DIMENSION", "points": [(0, 155), (200, 155)], "color": 6},
                {"type": "LINE", "layer": "DIMENSION", "points": [(205, 0), (205, 150)], "color": 6},
                {"type": "TEXT", "layer": "DIMENSION", "position": (210, 75), "text": "15000", "height": 3, "color": 6},
                
                # 文字标注图层
                {"type": "TEXT", "layer": "TEXT", "position": (50, 40), "text": "客厅", "height": 4, "color": 7},
                {"type": "TEXT", "layer": "TEXT", "position": (150, 40), "text": "卧室", "height": 4, "color": 7},
                {"type": "TEXT", "layer": "TEXT", "position": (50, 110), "text": "厨房", "height": 4, "color": 7},
                {"type": "TEXT", "layer": "TEXT", "position": (150, 110), "text": "卫生间", "height": 4, "color": 7},
                
                # 装饰图层
                {"type": "SPLINE", "layer": "DECORATION", "control_points": [(20, 20), (40, 30), (60, 20), (80, 30)], "color": 8},
                {"type": "CIRCLE", "layer": "DECORATION", "center": (170, 25), "radius": 8, "color": 8},
                
                # 其他图层 - 设备
                {"type": "RECTANGLE", "layer": "EQUIPMENT", "points": [(10, 10), (30, 30)], "color": 9},
                {"type": "RECTANGLE", "layer": "EQUIPMENT", "points": [(170, 10), (190, 30)], "color": 9}
            ],
            "layers": [
                {"name": "WALL", "color": 1, "description": "墙体"},
                {"name": "DOOR", "color": 2, "description": "门"},
                {"name": "WINDOW", "color": 3, "description": "窗"},
                {"name": "RAILING", "color": 4, "description": "栏杆"},
                {"name": "FURNITURE", "color": 5, "description": "家具"},
                {"name": "DIMENSION", "color": 6, "description": "尺寸标注"},
                {"name": "TEXT", "color": 7, "description": "文字标注"},
                {"name": "DECORATION", "color": 8, "description": "装饰"},
                {"name": "EQUIPMENT", "color": 9, "description": "设备"}
            ]
        }
    
    def start_test(self):
        """启动测试"""
        print("🚀 启动交互式测试")
        
        # 创建测试数据
        test_folder = self.create_test_data()
        
        # 创建测试控制界面
        self.create_test_control_ui()
        
        # 启动主应用
        self.start_main_application()
    
    def create_test_control_ui(self):
        """创建测试控制界面"""
        self.test_window = tk.Tk()
        self.test_window.title("三阶段处理测试控制台")
        self.test_window.geometry("400x600")
        
        # 标题
        title_label = Label(self.test_window, text="CAD三阶段处理测试", 
                           font=('Arial', 14, 'bold'))
        title_label.pack(pady=10)
        
        # 测试步骤框架
        steps_frame = Frame(self.test_window)
        steps_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 测试步骤
        self.create_test_steps(steps_frame)
        
        # 日志显示
        self.create_log_display()
        
        # 控制按钮
        self.create_control_buttons()
    
    def create_test_steps(self, parent):
        """创建测试步骤"""
        Label(parent, text="测试步骤:", font=('Arial', 12, 'bold')).pack(anchor='w')
        
        steps = [
            ("1. 选择测试文件夹", self.step1_select_folder),
            ("2. 阶段1：开始处理", self.step2_basic_processing),
            ("3. 验证基础数据显示", self.step3_verify_basic),
            ("4. 阶段2：线条处理", self.step4_line_processing),
            ("5. 验证线条处理结果", self.step5_verify_line),
            ("6. 阶段3：识别分组", self.step6_group_processing),
            ("7. 验证分组结果", self.step7_verify_group),
            ("8. 测试类别选择", self.step8_category_selection),
            ("9. 验证最终显示", self.step9_verify_final)
        ]
        
        self.step_buttons = []
        for step_text, step_func in steps:
            btn = Button(parent, text=step_text, command=step_func,
                        bg='#E3F2FD', font=('Arial', 10), width=30)
            btn.pack(fill='x', pady=2)
            self.step_buttons.append(btn)
    
    def create_log_display(self):
        """创建日志显示"""
        log_frame = Frame(self.test_window)
        log_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        Label(log_frame, text="测试日志:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        # 创建文本框和滚动条
        text_frame = Frame(log_frame)
        text_frame.pack(fill='both', expand=True)
        
        self.log_text = tk.Text(text_frame, height=8, font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
    
    def create_control_buttons(self):
        """创建控制按钮"""
        btn_frame = Frame(self.test_window)
        btn_frame.pack(fill='x', padx=10, pady=5)
        
        Button(btn_frame, text="清空日志", command=self.clear_log,
               bg='#FFF3E0', font=('Arial', 9)).pack(side='left', padx=5)
        
        Button(btn_frame, text="自动测试", command=self.auto_test,
               bg='#E8F5E8', font=('Arial', 9)).pack(side='left', padx=5)
        
        Button(btn_frame, text="退出", command=self.exit_test,
               bg='#FFEBEE', font=('Arial', 9)).pack(side='right', padx=5)
    
    def start_main_application(self):
        """启动主应用"""
        def run_app():
            self.app_window = tk.Tk()
            self.app = EnhancedCADAppV2(self.app_window)
            self.app_window.mainloop()
        
        app_thread = threading.Thread(target=run_app, daemon=True)
        app_thread.start()
        
        # 等待应用启动
        time.sleep(2)
        self.log("✅ 主应用已启动")
    
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        
        self.test_log.append(log_entry)
        
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, log_entry + "\n")
            self.log_text.see(tk.END)
        
        print(log_entry)
    
    def clear_log(self):
        """清空日志"""
        if hasattr(self, 'log_text'):
            self.log_text.delete(1.0, tk.END)
        self.test_log.clear()
    
    def step1_select_folder(self):
        """步骤1：选择测试文件夹"""
        if not self.app:
            self.log("❌ 主应用未启动")
            return
        
        try:
            self.app.folder_var.set(self.test_folder)
            self.log(f"✅ 已选择测试文件夹: {self.test_folder}")
        except Exception as e:
            self.log(f"❌ 选择文件夹失败: {e}")
    
    def step2_basic_processing(self):
        """步骤2：基础处理"""
        if not self.app:
            self.log("❌ 主应用未启动")
            return
        
        try:
            self.app.start_processing()
            self.log("🔄 开始基础处理...")
            
            # 等待处理完成
            self.test_window.after(3000, lambda: self.log("✅ 基础处理完成"))
        except Exception as e:
            self.log(f"❌ 基础处理失败: {e}")
    
    def step3_verify_basic(self):
        """步骤3：验证基础数据显示"""
        if not self.app or not self.app.processor:
            self.log("❌ 处理器未初始化")
            return
        
        try:
            # 检查基础数据
            has_raw_data = hasattr(self.app.processor, 'raw_entities')
            line_btn_enabled = hasattr(self.app, 'line_process_btn') and str(self.app.line_process_btn['state']) == 'normal'
            
            self.log(f"📊 基础数据存在: {'✓' if has_raw_data else '✗'}")
            self.log(f"📊 线条处理按钮启用: {'✓' if line_btn_enabled else '✗'}")
            
            if has_raw_data and line_btn_enabled:
                self.log("✅ 基础数据验证通过")
            else:
                self.log("❌ 基础数据验证失败")
                
        except Exception as e:
            self.log(f"❌ 验证基础数据异常: {e}")
    
    def step4_line_processing(self):
        """步骤4：线条处理"""
        if not self.app:
            self.log("❌ 主应用未启动")
            return
        
        try:
            self.app.start_line_processing()
            self.log("🔄 开始线条处理...")
            
            # 等待处理完成
            self.test_window.after(3000, lambda: self.log("✅ 线条处理完成"))
        except Exception as e:
            self.log(f"❌ 线条处理失败: {e}")
    
    def step5_verify_line(self):
        """步骤5：验证线条处理结果"""
        if not self.app or not self.app.processor:
            self.log("❌ 处理器未初始化")
            return
        
        try:
            # 检查线条处理结果
            has_merged_data = hasattr(self.app.processor, 'merged_entities')
            group_btn_enabled = hasattr(self.app, 'group_process_btn') and str(self.app.group_process_btn['state']) == 'normal'
            
            self.log(f"📊 合并数据存在: {'✓' if has_merged_data else '✗'}")
            self.log(f"📊 分组处理按钮启用: {'✓' if group_btn_enabled else '✗'}")
            
            if has_merged_data and group_btn_enabled:
                self.log("✅ 线条处理验证通过")
            else:
                self.log("❌ 线条处理验证失败")
                
        except Exception as e:
            self.log(f"❌ 验证线条处理异常: {e}")
    
    def step6_group_processing(self):
        """步骤6：识别分组"""
        if not self.app:
            self.log("❌ 主应用未启动")
            return
        
        try:
            self.app.start_group_processing()
            self.log("🔄 开始识别分组...")
            
            # 等待处理完成
            self.test_window.after(3000, lambda: self.log("✅ 识别分组完成"))
        except Exception as e:
            self.log(f"❌ 识别分组失败: {e}")
    
    def step7_verify_group(self):
        """步骤7：验证分组结果"""
        if not self.app or not self.app.processor:
            self.log("❌ 处理器未初始化")
            return
        
        try:
            # 检查分组结果
            has_groups = hasattr(self.app.processor, 'all_groups') and self.app.processor.all_groups
            has_groups_info = hasattr(self.app.processor, 'groups_info') and self.app.processor.groups_info
            has_group_list = hasattr(self.app, 'group_tree')
            
            self.log(f"📊 分组数据存在: {'✓' if has_groups else '✗'}")
            self.log(f"📊 组信息存在: {'✓' if has_groups_info else '✗'}")
            self.log(f"📊 实体组列表存在: {'✓' if has_group_list else '✗'}")
            
            if has_groups:
                self.log(f"📊 分组数量: {len(self.app.processor.all_groups)}")
            
            if has_groups and has_groups_info and has_group_list:
                self.log("✅ 分组结果验证通过")
            else:
                self.log("❌ 分组结果验证失败")
                
        except Exception as e:
            self.log(f"❌ 验证分组结果异常: {e}")
    
    def step8_category_selection(self):
        """步骤8：测试类别选择"""
        self.log("🔄 开始测试类别选择...")
        self.log("💡 请在主界面中手动选择不同组并分配类别")
        self.log("💡 可测试的类别：墙体、门窗、栏杆、家具、其他")
    
    def step9_verify_final(self):
        """步骤9：验证最终显示"""
        if not self.app:
            self.log("❌ 主应用未启动")
            return
        
        try:
            # 最终验证
            self.log("📊 进行最终验证...")
            self.log("💡 请检查以下项目：")
            self.log("  - 全图预览是否正确显示所有实体")
            self.log("  - 实体组列表是否显示所有分组")
            self.log("  - 类别选择是否正常工作")
            self.log("  - 填充功能是否可用")
            self.log("  - 房间识别功能是否可用")
            self.log("✅ 请手动验证上述功能")
            
        except Exception as e:
            self.log(f"❌ 最终验证异常: {e}")
    
    def auto_test(self):
        """自动测试"""
        self.log("🤖 开始自动测试...")
        
        auto_steps = [
            (self.step1_select_folder, 1000),
            (self.step2_basic_processing, 4000),
            (self.step3_verify_basic, 1000),
            (self.step4_line_processing, 4000),
            (self.step5_verify_line, 1000),
            (self.step6_group_processing, 4000),
            (self.step7_verify_group, 1000)
        ]
        
        def run_step(step_index):
            if step_index < len(auto_steps):
                step_func, delay = auto_steps[step_index]
                step_func()
                self.test_window.after(delay, lambda: run_step(step_index + 1))
            else:
                self.log("🤖 自动测试完成，请手动进行类别选择测试")
        
        run_step(0)
    
    def exit_test(self):
        """退出测试"""
        self.log("🏁 测试结束")
        if self.test_window:
            self.test_window.quit()
    
    def run(self):
        """运行测试"""
        self.start_test()
        self.test_window.mainloop()


def main():
    """主函数"""
    print("🧪 CAD三阶段处理 - 交互式测试")
    print("=" * 50)
    
    test_ui = InteractiveTestUI()
    test_ui.run()


if __name__ == "__main__":
    main()
