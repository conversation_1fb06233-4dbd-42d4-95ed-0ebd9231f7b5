#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试三阶段处理界面
"""

import tkinter as tk
from main_enhanced_with_v2_fill import EnhancedCADAppV2

def main():
    """主函数"""
    try:
        # 创建主窗口
        root = tk.Tk()
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        print(f"启动应用失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
