#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终处理器修复测试
验证处理器在整个生命周期中的稳定性
"""

import sys
import os
import tkinter as tk
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_final_processor_fix():
    """测试最终处理器修复"""
    print("🧪 最终处理器修复测试")
    print("="*50)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        print("1. 导入并创建应用...")
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        app = EnhancedCADAppV2(root)
        print("  ✅ 应用创建成功")
        
        print("2. 检查初始处理器状态...")
        if app.processor:
            print("  ✅ 初始处理器存在")
            initial_processor_id = id(app.processor)
        else:
            print("  ❌ 初始处理器不存在")
            initial_processor_id = None
        
        print("3. 检查处理器属性...")
        if app.processor:
            has_mapping = hasattr(app.processor, 'category_mapping') and app.processor.category_mapping
            print(f"  类别映射: {'✅ 存在' if has_mapping else '❌ 不存在'}")
            if has_mapping:
                print(f"    映射内容: {app.processor.category_mapping}")
        
        print("4. 模拟多次文件操作...")
        test_files = ["file1.dxf", "file2.dxf", "file3.dxf"]
        processor_ids = []
        
        for i, test_file in enumerate(test_files):
            print(f"  测试文件 {i+1}: {test_file}")
            
            # 创建测试数据
            app.file_data[test_file] = {
                'entities': [{'type': 'LINE', 'layer': 'A-WALL'}],
                'all_groups': [[{'type': 'LINE', 'layer': 'A-WALL'}]],
                'auto_labeled_entities': [],
                'labeled_entities': []
            }
            
            # 模拟文件加载
            try:
                app._load_file_data(test_file)
                if app.processor:
                    processor_ids.append(id(app.processor))
                    print(f"    ✅ 处理器存在 (ID: {id(app.processor)})")
                else:
                    processor_ids.append(None)
                    print(f"    ❌ 处理器不存在")
            except Exception as e:
                print(f"    ❌ 文件加载失败: {e}")
                processor_ids.append(None)
        
        print("5. 分析处理器稳定性...")
        if initial_processor_id:
            stable_count = sum(1 for pid in processor_ids if pid == initial_processor_id)
            total_count = len(processor_ids)
            stability_rate = stable_count / total_count * 100 if total_count > 0 else 0
            
            print(f"  处理器稳定性: {stability_rate:.1f}% ({stable_count}/{total_count})")
            
            if stability_rate == 100:
                print("  ✅ 处理器完全稳定，未发生重置")
            elif stability_rate >= 50:
                print("  ⚠️ 处理器基本稳定，偶有重置")
            else:
                print("  ❌ 处理器不稳定，频繁重置")
        
        print("6. 检查重置统计...")
        if hasattr(app, '_processor_reset_count'):
            reset_count = app._processor_reset_count
            print(f"  总重置次数: {reset_count}")
            
            if reset_count == 0:
                print("  🎉 完美！没有发生任何重置")
            elif reset_count <= 1:
                print("  ✅ 很好！重置次数很少")
            elif reset_count <= 3:
                print("  ⚠️ 一般，重置次数较少")
            else:
                print("  ❌ 重置次数过多，需要进一步优化")
        
        # 清理
        root.destroy()
        
        print("\n🎉 最终处理器修复测试完成")
        
        # 返回测试结果
        return {
            'initial_processor_exists': app.processor is not None,
            'reset_count': getattr(app, '_processor_reset_count', 0),
            'stability_rate': stability_rate if 'stability_rate' in locals() else 0
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = test_final_processor_fix()
    if result:
        print(f"\n📊 测试结果摘要:")
        print(f"  初始处理器存在: {'✅' if result['initial_processor_exists'] else '❌'}")
        print(f"  重置次数: {result['reset_count']}")
        print(f"  稳定性: {result['stability_rate']:.1f}%")
