#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速高度测试 - 验证房间填充栏是否显示
"""

import os
import sys
import tkinter as tk

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """快速测试"""
    print("🚀 启动快速高度测试")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建窗口
        root = tk.Tk()
        root.title("房间填充栏显示测试")
        root.geometry("1400x1000")  # 设置更大的窗口
        
        print("🔧 创建应用实例...")
        app = EnhancedCADAppV2(root)
        
        # 等待界面加载
        root.update()
        
        print("🔍 检查图层数据...")
        if hasattr(app, 'layer_items_data'):
            print(f"图层数据数量: {len(app.layer_items_data)}")
            for data in app.layer_items_data:
                print(f"  - {data[1]} ({data[0]})")
        
        print("🔍 检查图层列表框架...")
        if hasattr(app, 'layer_list_frame'):
            children = app.layer_list_frame.winfo_children()
            print(f"图层列表子组件数量: {len(children)}")
            
            # 强制更新布局
            app.layer_list_frame.update_idletasks()
            
            # 检查每个子组件
            for i, child in enumerate(children):
                try:
                    height = child.winfo_height()
                    width = child.winfo_width()
                    x = child.winfo_x()
                    y = child.winfo_y()
                    print(f"  子组件{i+1}: {width}x{height} at ({x},{y})")
                except:
                    print(f"  子组件{i+1}: 无法获取尺寸信息")
        
        print("🔍 检查容器尺寸...")
        if hasattr(app, 'layer_control_container'):
            container = app.layer_control_container
            container.update_idletasks()
            print(f"图层控制容器: {container.winfo_width()}x{container.winfo_height()}")
        
        if hasattr(app, 'layer_list_frame'):
            frame = app.layer_list_frame
            frame.update_idletasks()
            print(f"图层列表框架: {frame.winfo_width()}x{frame.winfo_height()}")
        
        # 手动重新创建图层项
        print("🔧 手动重新创建图层项...")
        if hasattr(app, '_create_layer_items'):
            app._create_layer_items()
            root.update()
            
            # 再次检查
            children = app.layer_list_frame.winfo_children()
            print(f"重新创建后子组件数量: {len(children)}")
        
        print("✅ 测试完成，窗口将保持打开以便观察")
        print("请检查图像控制区域左下角是否显示了4个图层项，包括房间填充")
        
        # 添加关闭按钮
        def close_app():
            print("🔚 关闭应用")
            root.quit()
            root.destroy()
        
        # 5秒后自动关闭
        root.after(8000, close_app)
        
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 房间填充栏显示快速测试")
    print("=" * 40)
    
    success = quick_test()
    
    if success:
        print("✅ 测试执行完成")
    else:
        print("❌ 测试执行失败")
    
    print("🏁 测试结束")
