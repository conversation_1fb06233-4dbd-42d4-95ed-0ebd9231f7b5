#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复可视化数据传递问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_show_group_method():
    """修复 _show_group 方法中的数据传递问题"""
    print("🔧 修复 _show_group 方法中的数据传递问题")
    print("="*60)
    
    file_path = 'main_enhanced_with_v2_fill.py'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找 _show_group 方法中的问题代码
        old_code = """                # 更新全图概览
                print("更新全图概览...")
                if hasattr(self.processor, 'current_file_entities') and self.processor.current_file_entities:



                    # 🔑 关键修复：传递组索引参数，使用清理后的组数据
                    self.processor.visualizer.visualize_overview(
                        self.processor.current_file_entities,
                        cleaned_group,  # 使用清理后的当前组
                        self.processor.auto_labeled_entities + self.processor.labeled_entities if hasattr(self.processor, 'auto_labeled_entities') and hasattr(self.processor, 'labeled_entities') else [],
                        processor=self.processor,
                        current_group_index=group_index  # 🔑 传递组索引以显示高亮
                    )"""
        
        new_code = """                # 更新全图概览
                print("更新全图概览...")
                
                # 🔧 修复：安全获取实体数据，确保数据不为空
                current_file_entities = getattr(self.processor, 'current_file_entities', [])
                auto_labeled_entities = getattr(self.processor, 'auto_labeled_entities', [])
                labeled_entities = getattr(self.processor, 'labeled_entities', [])
                
                print(f"  数据检查: 总实体={len(current_file_entities)}, 自动标注={len(auto_labeled_entities)}, 已标注={len(labeled_entities)}")
                
                if current_file_entities:
                    # 🔑 关键修复：传递组索引参数，使用清理后的组数据
                    self.processor.visualizer.visualize_overview(
                        current_file_entities,
                        cleaned_group,  # 使用清理后的当前组
                        auto_labeled_entities + labeled_entities,
                        processor=self.processor,
                        current_group_index=group_index  # 🔑 传递组索引以显示高亮
                    )
                else:
                    print("  ⚠️ 没有实体数据，跳过全图概览更新")"""
        
        if old_code in content:
            content = content.replace(old_code, new_code)
            print("✅ 找到并修复了 _show_group 方法中的数据传递问题")
        else:
            print("❌ 未找到目标代码，可能已经修复或代码结构已变化")
            return False
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 文件已更新")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_restore_processor_method():
    """修复处理器恢复方法，确保数据完整性"""
    print(f"\n🔧 修复处理器恢复方法")
    print("="*60)
    
    file_path = 'main_enhanced_with_v2_fill.py'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并修复处理器恢复方法
        old_code = """            # 更新组信息
            if hasattr(self.processor, '_update_groups_info'):
                self.processor._update_groups_info()
                print(f"    更新组信息: {len(self.processor.groups_info)} 个")
            
            print(f"  ✅ 处理器状态恢复完成")
            return True"""
        
        new_code = """            # 更新组信息
            if hasattr(self.processor, '_update_groups_info'):
                self.processor._update_groups_info()
                print(f"    更新组信息: {len(self.processor.groups_info)} 个")
            
            # 🔧 修复：验证恢复的数据完整性
            print(f"  📊 数据完整性验证:")
            print(f"    总实体数: {len(getattr(self.processor, 'current_file_entities', []))}")
            print(f"    总组数: {len(getattr(self.processor, 'all_groups', []))}")
            print(f"    组信息数: {len(getattr(self.processor, 'groups_info', []))}")
            print(f"    自动标注实体数: {len(getattr(self.processor, 'auto_labeled_entities', []))}")
            print(f"    已标注实体数: {len(getattr(self.processor, 'labeled_entities', []))}")
            
            # 检查关键数据是否为空
            if not getattr(self.processor, 'current_file_entities', []):
                print(f"  ⚠️ 警告: current_file_entities 为空")
            if not getattr(self.processor, 'all_groups', []):
                print(f"  ⚠️ 警告: all_groups 为空")
            
            print(f"  ✅ 处理器状态恢复完成")
            return True"""
        
        if old_code in content:
            content = content.replace(old_code, new_code)
            print("✅ 找到并修复了处理器恢复方法")
        else:
            print("❌ 未找到目标代码，可能已经修复或代码结构已变化")
            return False
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 文件已更新")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def add_data_validation():
    """添加数据验证方法"""
    print(f"\n🔧 添加数据验证方法")
    print("="*60)
    
    file_path = 'main_enhanced_with_v2_fill.py'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找合适的位置插入验证方法
        insertion_point = "    def _restore_processor_from_current_file(self):"
        
        validation_method = """
    def _validate_processor_data(self):
        \"\"\"验证处理器数据完整性\"\"\"
        try:
            if not self.processor:
                print("❌ 处理器不存在")
                return False
            
            # 检查关键属性
            required_attrs = [
                'current_file_entities', 'all_groups', 'groups_info',
                'auto_labeled_entities', 'labeled_entities'
            ]
            
            missing_attrs = []
            empty_attrs = []
            
            for attr in required_attrs:
                if not hasattr(self.processor, attr):
                    missing_attrs.append(attr)
                elif not getattr(self.processor, attr, []):
                    empty_attrs.append(attr)
            
            if missing_attrs:
                print(f"❌ 缺少属性: {missing_attrs}")
                return False
            
            if empty_attrs:
                print(f"⚠️ 空属性: {empty_attrs}")
            
            print(f"✅ 处理器数据验证通过")
            return True
            
        except Exception as e:
            print(f"❌ 数据验证失败: {e}")
            return False

"""
        
        if insertion_point in content:
            content = content.replace(insertion_point, validation_method + insertion_point)
            print("✅ 添加了数据验证方法")
        else:
            print("❌ 未找到插入点")
            return False
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 文件已更新")
        return True
        
    except Exception as e:
        print(f"❌ 添加验证方法失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fixes():
    """测试修复效果"""
    print(f"\n🧪 测试修复效果")
    print("="*60)
    
    try:
        # 模拟处理器恢复和数据传递
        class MockProcessor:
            def __init__(self):
                self.current_file_entities = []
                self.all_groups = []
                self.groups_info = []
                self.auto_labeled_entities = []
                self.labeled_entities = []
                self.category_mapping = {'wall': '墙体', 'door_window': '门窗'}
            
            def _clean_group_data(self, group):
                return [entity for entity in group if isinstance(entity, dict)]
            
            def _update_groups_info(self):
                pass
        
        class MockVisualizer:
            def visualize_overview(self, *args, **kwargs):
                print(f"  📊 可视化器调用参数:")
                print(f"    总实体数: {len(args[0]) if args else 0}")
                print(f"    当前组实体数: {len(args[1]) if len(args) > 1 and args[1] else 0}")
                print(f"    已标注实体数: {len(args[2]) if len(args) > 2 and args[2] else 0}")
                return True
            
            def visualize_entity_group(self, *args, **kwargs):
                print(f"  🎨 组预览调用参数:")
                print(f"    组实体数: {len(args[0]) if args else 0}")
                return True
        
        class MockUI:
            def __init__(self):
                self.processor = MockProcessor()
                self.processor.visualizer = MockVisualizer()
            
            def _validate_processor_data(self):
                """验证处理器数据完整性"""
                try:
                    if not self.processor:
                        print("❌ 处理器不存在")
                        return False
                    
                    # 检查关键属性
                    required_attrs = [
                        'current_file_entities', 'all_groups', 'groups_info',
                        'auto_labeled_entities', 'labeled_entities'
                    ]
                    
                    missing_attrs = []
                    empty_attrs = []
                    
                    for attr in required_attrs:
                        if not hasattr(self.processor, attr):
                            missing_attrs.append(attr)
                        elif not getattr(self.processor, attr, []):
                            empty_attrs.append(attr)
                    
                    if missing_attrs:
                        print(f"❌ 缺少属性: {missing_attrs}")
                        return False
                    
                    if empty_attrs:
                        print(f"⚠️ 空属性: {empty_attrs}")
                    
                    print(f"✅ 处理器数据验证通过")
                    return True
                    
                except Exception as e:
                    print(f"❌ 数据验证失败: {e}")
                    return False
            
            def test_show_group(self, group, group_index):
                """测试显示组方法"""
                print(f"🎨 测试显示组 {group_index}")
                
                # 验证数据
                if not self._validate_processor_data():
                    print("❌ 数据验证失败，无法显示组")
                    return False
                
                # 清理组数据
                cleaned_group = self.processor._clean_group_data(group)
                print(f"  清理后组数据: {len(cleaned_group)} 个实体")
                
                # 安全获取实体数据
                current_file_entities = getattr(self.processor, 'current_file_entities', [])
                auto_labeled_entities = getattr(self.processor, 'auto_labeled_entities', [])
                labeled_entities = getattr(self.processor, 'labeled_entities', [])
                
                print(f"  数据检查: 总实体={len(current_file_entities)}, 自动标注={len(auto_labeled_entities)}, 已标注={len(labeled_entities)}")
                
                if current_file_entities:
                    # 调用可视化器
                    self.processor.visualizer.visualize_entity_group(cleaned_group, self.processor.category_mapping)
                    self.processor.visualizer.visualize_overview(
                        current_file_entities,
                        cleaned_group,
                        auto_labeled_entities + labeled_entities,
                        processor=self.processor,
                        current_group_index=group_index
                    )
                    print("  ✅ 可视化调用成功")
                    return True
                else:
                    print("  ⚠️ 没有实体数据，跳过可视化")
                    return False
        
        # 测试空数据情况
        print("1. 测试空数据情况:")
        ui = MockUI()
        test_group = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
            'invalid_string'
        ]
        ui.test_show_group(test_group, 1)
        
        # 测试有数据情况
        print(f"\n2. 测试有数据情况:")
        ui.processor.current_file_entities = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
            {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(50, -10), (50, 10)]}
        ]
        ui.processor.auto_labeled_entities = ui.processor.current_file_entities
        ui.test_show_group(test_group, 1)
        
        print(f"\n✅ 修复测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主修复函数"""
    print("🚀 开始修复可视化数据传递问题")
    
    try:
        # 1. 修复 _show_group 方法
        fix1_success = fix_show_group_method()
        
        # 2. 修复处理器恢复方法
        fix2_success = fix_restore_processor_method()
        
        # 3. 添加数据验证方法
        fix3_success = add_data_validation()
        
        # 4. 测试修复效果
        test_success = test_fixes()
        
        print(f"\n" + "="*60)
        print(f"📊 修复结果总结:")
        print(f"  _show_group 方法修复: {'✅ 成功' if fix1_success else '❌ 失败'}")
        print(f"  处理器恢复方法修复: {'✅ 成功' if fix2_success else '❌ 失败'}")
        print(f"  数据验证方法添加: {'✅ 成功' if fix3_success else '❌ 失败'}")
        print(f"  修复效果测试: {'✅ 成功' if test_success else '❌ 失败'}")
        
        if all([fix1_success, fix2_success, fix3_success, test_success]):
            print(f"\n🎉 所有修复完成！")
            print(f"💡 修复内容:")
            print(f"   - 修复了数据传递到可视化器的问题")
            print(f"   - 添加了数据完整性验证")
            print(f"   - 改进了错误处理和日志输出")
            print(f"   - 确保处理器恢复后数据不为空")
        else:
            print(f"\n⚠️ 部分修复失败，请检查错误信息")
        
    except Exception as e:
        print(f"❌ 修复过程失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
