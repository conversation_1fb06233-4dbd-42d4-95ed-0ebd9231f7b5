#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试视图显示所有组
验证修复后是否能正确显示所有组（墙体、门窗等）
"""

import sys
import os
import tkinter as tk
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_view_display_all_groups():
    """测试视图显示所有组"""
    print("🧪 测试视图显示所有组")
    print("="*50)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        print("1. 导入并创建应用...")
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        app = EnhancedCADAppV2(root)
        print("  ✅ 应用创建成功")
        
        print("2. 创建测试数据...")
        # 创建包含墙体和门窗的测试数据
        test_entities = [
            # 墙体实体
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': 'wall'},
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'label': 'wall'},
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 100), (0, 100)], 'label': 'wall'},
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 100), (0, 0)], 'label': 'wall'},
            
            # 门窗实体
            {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(20, -5), (20, 5)], 'label': 'door_window'},
            {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(80, -5), (80, 5)], 'label': 'door_window'},
        ]
        
        test_groups = [
            # 墙体组
            [test_entities[0], test_entities[1], test_entities[2], test_entities[3]],
            # 门窗组
            [test_entities[4], test_entities[5]]
        ]
        
        # 设置测试数据
        test_file = "test_display.dxf"
        app.file_data[test_file] = {
            'entities': test_entities,
            'all_groups': test_groups,
            'auto_labeled_entities': test_entities,
            'labeled_entities': [],
            'groups_info': [
                {'status': 'auto_labeled', 'group_type': 'wall', 'entity_count': 4},
                {'status': 'auto_labeled', 'group_type': 'door_window', 'entity_count': 2}
            ]
        }
        
        print("3. 加载测试文件...")
        app._load_file_data(test_file)
        
        print("4. 检查处理器数据...")
        if app.processor:
            current_entities = getattr(app.processor, 'current_file_entities', [])
            all_groups = getattr(app.processor, 'all_groups', [])
            
            print(f"  总实体数: {len(current_entities)}")
            print(f"  总组数: {len(all_groups)}")
            
            # 统计实体类型
            entity_types = {}
            for entity in current_entities:
                if isinstance(entity, dict):
                    entity_type = entity.get('type', 'unknown')
                    entity_layer = entity.get('layer', 'unknown')
                    key = f"{entity_type}({entity_layer})"
                    entity_types[key] = entity_types.get(key, 0) + 1
            
            print(f"  实体类型分布: {entity_types}")
            
            # 统计组内容
            for i, group in enumerate(all_groups):
                group_entities = [e for e in group if isinstance(e, dict)]
                group_layers = set(e.get('layer', 'unknown') for e in group_entities)
                print(f"  组{i+1}: {len(group_entities)}个实体, 图层: {group_layers}")
        
        print("5. 测试完整视图刷新...")
        if hasattr(app, '_refresh_complete_view'):
            success = app._refresh_complete_view()
            print(f"  完整视图刷新: {'✅ 成功' if success else '❌ 失败'}")
        else:
            print("  ⚠️ _refresh_complete_view 方法不存在")
        
        print("6. 测试组显示...")
        if app.processor and hasattr(app.processor, 'all_groups') and app.processor.all_groups:
            for i, group in enumerate(app.processor.all_groups):
                try:
                    app._show_group(group, i + 1)
                    print(f"  组{i+1} 显示: ✅ 成功")
                except Exception as e:
                    print(f"  组{i+1} 显示: ❌ 失败 - {e}")
        
        # 清理
        root.destroy()
        
        print("\n🎉 视图显示测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_view_display_all_groups()
