#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试墙体线条合并功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_enhanced import EnhancedCADProcessor

def test_wall_line_merging():
    """测试墙体线条合并功能"""
    
    # 创建处理器
    processor = EnhancedCADProcessor()
    
    # 创建测试墙体线条实体 - 确保端点精确连接
    test_wall_entities = [
        # 连续的水平墙体线条（应该被合并）- 端点精确连接
        {
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_x': 0.0, 'start_y': 0.0,
            'end_x': 100.0, 'end_y': 0.0,
            'color': 7,
            'linetype': 'CONTINUOUS'
        },
        {
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_x': 100.0, 'start_y': 0.0,  # 精确连接到上一条线的终点
            'end_x': 200.0, 'end_y': 0.0,
            'color': 7,
            'linetype': 'CONTINUOUS'
        },
        {
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_x': 200.0, 'start_y': 0.0,  # 精确连接到上一条线的终点
            'end_x': 300.0, 'end_y': 0.0,
            'color': 7,
            'linetype': 'CONTINUOUS'
        },
        # 连续的垂直墙体线条（应该被合并）
        {
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_x': 0.0, 'start_y': 0.0,
            'end_x': 0.0, 'end_y': 100.0,
            'color': 7,
            'linetype': 'CONTINUOUS'
        },
        {
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_x': 0.0, 'start_y': 100.0,  # 精确连接到上一条线的终点
            'end_x': 0.0, 'end_y': 200.0,
            'color': 7,
            'linetype': 'CONTINUOUS'
        },
        # 独立的墙体线条（不应该被合并）
        {
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_x': 500.0, 'start_y': 500.0,
            'end_x': 600.0, 'end_y': 500.0,
            'color': 7,
            'linetype': 'CONTINUOUS'
        },
        # 多段线（内部线段应该被合并）
        {
            'type': 'LWPOLYLINE',
            'layer': 'A-WALL',
            'points': [(1000.0, 0.0), (1100.0, 0.0), (1200.0, 0.0)],  # 共线的点
            'color': 7,
            'linetype': 'CONTINUOUS'
        },
        # 非线条实体（不应该参与合并）
        {
            'type': 'CIRCLE',
            'layer': 'A-WALL',
            'center_x': 400,
            'center_y': 400,
            'radius': 50,
            'color': 7
        }
    ]
    
    print("开始测试墙体线条合并功能...")
    print(f"测试数据: {len(test_wall_entities)} 个墙体实体")
    
    # 统计线条实体
    line_entities = [e for e in test_wall_entities if e.get('type') in ['LINE', 'LWPOLYLINE', 'POLYLINE']]
    print(f"其中线条实体: {len(line_entities)} 个")
    
    # 调用测试方法
    processor._test_wall_line_merging(test_wall_entities)
    
    print("✅ 测试完成")

if __name__ == "__main__":
    test_wall_line_merging()
