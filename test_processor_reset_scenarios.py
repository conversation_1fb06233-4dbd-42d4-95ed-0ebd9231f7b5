#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试处理器重置场景
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_processor_reset_scenarios():
    """测试各种可能导致处理器重置的场景"""
    print("🧪 测试处理器重置场景")
    print("="*60)
    
    scenarios = [
        "1. 启动程序后立即检查处理器状态",
        "2. 选择文件后检查处理器状态", 
        "3. 切换文件时检查处理器状态",
        "4. 更新组列表时检查处理器状态",
        "5. 点击组项目时检查处理器状态"
    ]
    
    for scenario in scenarios:
        print(f"📋 {scenario}")
    
    print(f"\n💡 测试方法:")
    print(f"   1. 运行主程序: python main_enhanced_with_v2_fill.py")
    print(f"   2. 按照上述场景操作")
    print(f"   3. 观察控制台输出的详细追踪信息")
    print(f"   4. 记录每次重置的触发条件和调用栈")
    print(f"   5. 分析重置模式，找出根本原因")

if __name__ == "__main__":
    test_processor_reset_scenarios()
