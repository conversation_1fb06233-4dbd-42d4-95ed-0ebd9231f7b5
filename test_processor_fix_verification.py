#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证处理器修复效果
测试处理器是否能够正确恢复状态
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_processor_recovery():
    """测试处理器恢复功能"""
    print("🧪 测试处理器恢复功能")
    print("="*60)
    
    try:
        # 模拟UI类
        class MockUI:
            def __init__(self):
                self.processor = None
                self.visualizer = None
                self.canvas = None
                self.current_file = "test_file.dxf"
                self.file_cache = {}
                
                # 模拟缓存数据
                self.file_cache[self.current_file] = {
                    'entities': [
                        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': '墙体', 'auto_labeled': True},
                        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(50, -10), (50, 10)], 'label': '门窗', 'auto_labeled': True}
                    ],
                    'all_groups': [
                        [{'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': '墙体', 'auto_labeled': True}],
                        [{'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(50, -10), (50, 10)], 'label': '门窗', 'auto_labeled': True}]
                    ],
                    'auto_labeled_entities': [
                        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': '墙体', 'auto_labeled': True},
                        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(50, -10), (50, 10)], 'label': '门窗', 'auto_labeled': True}
                    ],
                    'labeled_entities': []
                }
            
            def _deserialize_data(self, data):
                """模拟数据反序列化"""
                return data
            
            def on_status_update(self, status):
                pass
            
            def on_progress_update(self, progress):
                pass
            
            def _restore_processor_from_current_file(self):
                """从当前文件恢复处理器状态"""
                try:
                    if not hasattr(self, 'current_file') or not self.current_file:
                        return False
                    
                    # 检查缓存中是否有当前文件的数据
                    cache_key = self.current_file
                    if cache_key not in self.file_cache:
                        print(f"  ❌ 缓存中没有文件数据: {cache_key}")
                        return False
                    
                    print(f"  🔄 从缓存恢复处理器状态: {cache_key}")
                    
                    # 创建新的处理器
                    from main_enhanced import EnhancedCADProcessor
                    self.processor = EnhancedCADProcessor(self.visualizer, self.canvas)
                    
                    # 设置回调
                    if hasattr(self, 'on_status_update') and hasattr(self, 'on_progress_update'):
                        self.processor.set_callbacks(self.on_status_update, self.on_progress_update)
                    
                    # 恢复数据
                    data = self.file_cache[cache_key]
                    
                    # 恢复基本属性
                    self.processor.current_file = self.current_file
                    
                    # 恢复实体数据
                    if 'entities' in data:
                        self.processor.entities = self._deserialize_data(data['entities'])
                        self.processor.current_file_entities = self.processor.entities
                        print(f"    恢复实体: {len(self.processor.entities)} 个")
                    
                    # 恢复组数据
                    if 'all_groups' in data or 'groups' in data:
                        self.processor.all_groups = self._deserialize_data(
                            data.get('all_groups', data.get('groups', []))
                        )
                        print(f"    恢复组: {len(self.processor.all_groups)} 个")
                    
                    # 恢复标注数据
                    if 'auto_labeled_entities' in data:
                        self.processor.auto_labeled_entities = self._deserialize_data(data['auto_labeled_entities'])
                        print(f"    恢复自动标注实体: {len(self.processor.auto_labeled_entities)} 个")
                    
                    if 'labeled_entities' in data:
                        self.processor.labeled_entities = self._deserialize_data(data['labeled_entities'])
                        print(f"    恢复已标注实体: {len(self.processor.labeled_entities)} 个")
                    
                    # 更新组信息
                    if hasattr(self.processor, '_update_groups_info'):
                        self.processor._update_groups_info()
                        print(f"    更新组信息: {len(self.processor.groups_info)} 个")
                    
                    print(f"  ✅ 处理器状态恢复完成")
                    return True
                    
                except Exception as e:
                    print(f"  ❌ 恢复处理器状态失败: {e}")
                    import traceback
                    traceback.print_exc()
                    return False
            
            def _update_group_list_enhanced(self):
                """增强版组列表更新，支持隐藏组显示"""
                if not self.processor:
                    print("⚠️ 处理器不存在，尝试恢复处理器状态")
                    # 尝试从当前文件缓存恢复处理器状态
                    if hasattr(self, 'current_file') and self.current_file:
                        success = self._restore_processor_from_current_file()
                        if success:
                            print("✅ 从当前文件恢复处理器状态成功")
                        else:
                            print("❌ 无法从当前文件恢复处理器状态，组列表将显示为空")
                            return
                    else:
                        print("❌ 没有当前文件信息，无法恢复处理器状态")
                        return
                
                # 模拟组列表更新
                if hasattr(self.processor, 'groups_info') and self.processor.groups_info:
                    print(f"📋 组列表更新成功: {len(self.processor.groups_info)} 个组")
                    
                    for i, info in enumerate(self.processor.groups_info):
                        status = info.get('status', 'unknown')
                        group_type = info.get('group_type', 'unknown')
                        entity_count = info.get('entity_count', 0)
                        
                        # 状态文本映射
                        if status == 'auto_labeled':
                            status_text = '自动标注'
                        elif status == 'pending':
                            status_text = '待处理'
                        else:
                            status_text = status
                        
                        # 类型文本映射
                        if hasattr(self.processor, 'category_mapping') and self.processor.category_mapping:
                            type_text = self.processor.category_mapping.get(group_type, group_type)
                        else:
                            type_text = group_type
                        
                        print(f"  组{i+1}: {status_text} - {type_text} ({entity_count} 个实体)")
                else:
                    print(f"❌ 处理器没有组信息")
        
        # 测试流程
        print("1. 创建模拟UI")
        ui = MockUI()
        
        print("\n2. 模拟处理器丢失场景")
        ui.processor = None  # 模拟处理器丢失
        
        print("\n3. 尝试更新组列表（应该触发恢复）")
        ui._update_group_list_enhanced()
        
        print("\n4. 验证恢复结果")
        if ui.processor:
            print(f"✅ 处理器恢复成功")
            print(f"  实体数: {len(ui.processor.entities) if hasattr(ui.processor, 'entities') else 0}")
            print(f"  组数: {len(ui.processor.all_groups) if hasattr(ui.processor, 'all_groups') else 0}")
            print(f"  组信息数: {len(ui.processor.groups_info) if hasattr(ui.processor, 'groups_info') else 0}")
            return True
        else:
            print(f"❌ 处理器恢复失败")
            return False
        
    except Exception as e:
        print(f"❌ 处理器恢复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_display_after_recovery():
    """测试恢复后的显示效果"""
    print(f"\n🧪 测试恢复后的显示效果")
    print("="*60)
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        # 创建处理器并添加数据
        processor = EnhancedCADProcessor()
        
        # 添加测试数据
        test_entities = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': '墙体', 'auto_labeled': True},
            {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(50, -10), (50, 10)], 'label': '门窗', 'auto_labeled': True}
        ]
        
        test_groups = [
            [test_entities[0]],  # 墙体组
            [test_entities[1]]   # 门窗组
        ]
        
        # 设置处理器数据
        processor.entities = test_entities
        processor.current_file_entities = test_entities
        processor.all_groups = test_groups
        processor.auto_labeled_entities = test_entities
        processor.labeled_entities = []
        
        # 更新组信息
        processor._update_groups_info()
        
        print(f"✅ 处理器数据设置完成")
        print(f"  实体数: {len(processor.entities)}")
        print(f"  组数: {len(processor.all_groups)}")
        print(f"  组信息数: {len(processor.groups_info)}")
        
        # 验证组信息
        print(f"\n📋 组信息验证:")
        for i, info in enumerate(processor.groups_info):
            status = info.get('status', 'unknown')
            group_type = info.get('group_type', 'unknown')
            entity_count = info.get('entity_count', 0)
            label = info.get('label', 'unknown')
            
            print(f"  组{i+1}:")
            print(f"    状态: {status}")
            print(f"    类型: {group_type}")
            print(f"    标签: {label}")
            print(f"    实体数: {entity_count}")
            
            # 验证类型映射
            if hasattr(processor, 'category_mapping') and processor.category_mapping:
                type_text = processor.category_mapping.get(group_type, group_type)
                print(f"    映射类型: {type_text}")
            
            # 验证预期结果
            if i == 0:  # 墙体组
                if group_type == 'wall' and status == 'auto_labeled':
                    print(f"    ✅ 墙体组验证通过")
                else:
                    print(f"    ❌ 墙体组验证失败")
                    return False
            elif i == 1:  # 门窗组
                if group_type == 'door_window' and status == 'auto_labeled':
                    print(f"    ✅ 门窗组验证通过")
                else:
                    print(f"    ❌ 门窗组验证失败")
                    return False
        
        print(f"\n✅ 恢复后显示效果测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 恢复后显示效果测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始处理器修复验证测试")
    
    try:
        # 1. 测试处理器恢复功能
        test1_success = test_processor_recovery()
        
        # 2. 测试恢复后的显示效果
        test2_success = test_display_after_recovery()
        
        print(f"\n" + "="*60)
        print(f"📊 处理器修复验证结果:")
        print(f"  处理器恢复功能: {'✅ 通过' if test1_success else '❌ 失败'}")
        print(f"  恢复后显示效果: {'✅ 通过' if test2_success else '❌ 失败'}")
        
        if test1_success and test2_success:
            print(f"\n🎉 处理器修复验证全部通过！")
            print(f"💡 修复效果:")
            print(f"   - 处理器丢失时能够自动从缓存恢复")
            print(f"   - 恢复后的处理器包含完整的组数据")
            print(f"   - 组列表能够正确显示墙体和门窗类型")
            print(f"   - 不再出现 '⚠️ 处理器不存在，创建新的处理器' 后显示空白的问题")
        else:
            print(f"\n⚠️ 部分测试失败，可能还需要进一步调整")
        
    except Exception as e:
        print(f"❌ 处理器修复验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
