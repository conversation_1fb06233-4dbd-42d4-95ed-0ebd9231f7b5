#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
诊断可视化显示问题
分析预览图无显示和全图概览只显示门窗组的问题
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_visualizer_methods():
    """分析可视化器方法"""
    print("🔍 分析可视化器方法...")
    
    try:
        from cad_visualizer import CADVisualizer
        
        visualizer = CADVisualizer()
        
        # 检查关键方法
        methods_to_check = [
            'visualize_entity_group',
            'visualize_overview', 
            '_draw_entity',
            'update_canvas',
            '_get_entity_color',
            '_get_entity_style'
        ]
        
        print("📋 可视化器方法检查:")
        for method_name in methods_to_check:
            if hasattr(visualizer, method_name):
                print(f"  ✅ {method_name}: 存在")
            else:
                print(f"  ❌ {method_name}: 缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 可视化器分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_entity_group_visualization():
    """测试实体组可视化"""
    print("\n🎨 测试实体组可视化...")
    
    try:
        from cad_visualizer import CADVisualizer
        
        visualizer = CADVisualizer()
        
        # 创建测试实体组
        test_entities = [
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'points': [(0, 0), (100, 0)],
                'color': 7
            },
            {
                'type': 'LINE', 
                'layer': 'A-DOOR',
                'points': [(50, 0), (50, 50)],
                'color': 3
            }
        ]
        
        print(f"📋 测试实体: {len(test_entities)} 个")
        for i, entity in enumerate(test_entities):
            print(f"  实体{i+1}: {entity['type']} - {entity['layer']}")
        
        # 测试可视化
        print("🎨 执行实体组可视化...")
        visualizer.visualize_entity_group(test_entities, {'wall': '墙体', 'door_window': '门窗'})
        
        # 检查是否有图形对象被创建
        if hasattr(visualizer, 'ax_detail') and visualizer.ax_detail:
            children = visualizer.ax_detail.get_children()
            print(f"  详细视图子对象数量: {len(children)}")
            
            # 检查线条对象
            line_objects = [child for child in children if hasattr(child, 'get_data')]
            print(f"  线条对象数量: {len(line_objects)}")
            
            if len(line_objects) > 0:
                print("  ✅ 实体组可视化正常创建图形对象")
                return True
            else:
                print("  ❌ 实体组可视化未创建图形对象")
                return False
        else:
            print("  ❌ 详细视图轴对象不存在")
            return False
            
    except Exception as e:
        print(f"❌ 实体组可视化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_overview_visualization():
    """测试全图概览可视化"""
    print("\n🌍 测试全图概览可视化...")
    
    try:
        from cad_visualizer import CADVisualizer
        
        visualizer = CADVisualizer()
        
        # 创建测试数据
        all_entities = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'color': 7},
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'color': 7},
            {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(50, 0), (50, 50)], 'color': 3},
            {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(0, 50), (50, 50)], 'color': 4}
        ]
        
        current_group = [
            {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(50, 0), (50, 50)], 'color': 3}
        ]
        
        labeled_entities = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'color': 7}
        ]
        
        print(f"📋 测试数据:")
        print(f"  所有实体: {len(all_entities)} 个")
        print(f"  当前组: {len(current_group)} 个")
        print(f"  已标注实体: {len(labeled_entities)} 个")
        
        # 按图层分组统计
        layer_stats = {}
        for entity in all_entities:
            layer = entity.get('layer', 'unknown')
            layer_stats[layer] = layer_stats.get(layer, 0) + 1
        
        print(f"  图层分布: {layer_stats}")
        
        # 测试全图概览
        print("🌍 执行全图概览可视化...")
        visualizer.visualize_overview(all_entities, current_group, labeled_entities)
        
        # 检查概览轴对象
        if hasattr(visualizer, 'ax_overview') and visualizer.ax_overview:
            children = visualizer.ax_overview.get_children()
            print(f"  概览视图子对象数量: {len(children)}")
            
            # 检查线条对象
            line_objects = [child for child in children if hasattr(child, 'get_data')]
            print(f"  线条对象数量: {len(line_objects)}")
            
            # 检查颜色分布
            colors_used = set()
            for line_obj in line_objects:
                if hasattr(line_obj, 'get_color'):
                    colors_used.add(str(line_obj.get_color()))
            
            print(f"  使用的颜色数量: {len(colors_used)}")
            print(f"  颜色列表: {list(colors_used)[:5]}...")  # 只显示前5个
            
            if len(line_objects) >= len(all_entities):
                print("  ✅ 全图概览可视化正常")
                return True
            else:
                print("  ❌ 全图概览可视化实体数量不足")
                print(f"    期望: {len(all_entities)}, 实际: {len(line_objects)}")
                return False
        else:
            print("  ❌ 概览视图轴对象不存在")
            return False
            
    except Exception as e:
        print(f"❌ 全图概览可视化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_color_mapping():
    """分析颜色映射问题"""
    print("\n🎨 分析颜色映射...")
    
    try:
        from cad_visualizer import CADVisualizer
        
        visualizer = CADVisualizer()
        
        # 测试不同图层的颜色映射
        test_layers = ['A-WALL', 'A-DOOR', 'A-WINDOW', 'OTHER']
        
        print("📋 图层颜色映射测试:")
        for layer in test_layers:
            test_entity = {'layer': layer, 'color': 7}
            
            if hasattr(visualizer, '_get_entity_color'):
                color = visualizer._get_entity_color(test_entity)
                print(f"  {layer}: {color}")
            else:
                print(f"  {layer}: _get_entity_color方法不存在")
        
        # 测试状态颜色
        print("\n📋 状态颜色映射测试:")
        test_states = ['current', 'labeled', 'unlabeled']
        
        for state in test_states:
            test_entity = {'layer': 'A-WALL', 'color': 7}
            
            if hasattr(visualizer, '_get_entity_color'):
                try:
                    # 尝试不同的参数组合
                    color = visualizer._get_entity_color(test_entity, state=state)
                    print(f"  状态 {state}: {color}")
                except:
                    color = visualizer._get_entity_color(test_entity)
                    print(f"  状态 {state}: {color} (默认)")
            else:
                print(f"  状态 {state}: 方法不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 颜色映射分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_data_cleaning_impact():
    """检查数据清理对可视化的影响"""
    print("\n🔧 检查数据清理对可视化的影响...")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor(None, None)
        
        # 创建包含各种数据的测试组
        mixed_group = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'color': 7},
            'index',  # 会被清理掉
            {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(50, 0), (50, 50)], 'color': 3},
            'total',  # 会被清理掉
            {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(0, 50), (50, 50)], 'color': 4},
            None,  # 会被清理掉
            {'layer': 'A-WALL'},  # 缺少type，会被清理掉
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 100), (0, 100)], 'color': 7}
        ]
        
        print(f"📋 原始混合组数据: {len(mixed_group)} 个项目")
        
        # 统计原始数据中的有效实体
        original_valid = 0
        for item in mixed_group:
            if isinstance(item, dict) and item.get('type') and item.get('layer'):
                original_valid += 1
        
        print(f"  原始有效实体: {original_valid} 个")
        
        # 清理数据
        cleaned_group = processor._clean_group_data(mixed_group)
        print(f"  清理后实体: {len(cleaned_group)} 个")
        
        # 检查清理是否正确
        if len(cleaned_group) == original_valid:
            print("  ✅ 数据清理正确，未丢失有效实体")
            
            # 检查清理后的数据质量
            all_valid = True
            for entity in cleaned_group:
                if not (isinstance(entity, dict) and entity.get('type') and entity.get('layer')):
                    all_valid = False
                    break
            
            if all_valid:
                print("  ✅ 清理后数据质量良好")
                return True
            else:
                print("  ❌ 清理后数据质量有问题")
                return False
        else:
            print("  ❌ 数据清理可能丢失了有效实体")
            print(f"    期望: {original_valid}, 实际: {len(cleaned_group)}")
            return False
            
    except Exception as e:
        print(f"❌ 数据清理影响检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CAD分类标注工具 - 可视化显示问题诊断")
    print("=" * 60)
    
    tests = [
        ("可视化器方法分析", analyze_visualizer_methods),
        ("实体组可视化测试", test_entity_group_visualization),
        ("全图概览可视化测试", test_overview_visualization),
        ("颜色映射分析", analyze_color_mapping),
        ("数据清理影响检查", check_data_cleaning_impact)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("诊断结果总结:")
    print("=" * 60)
    
    for test_name, success in results:
        status = "✅ 正常" if success else "❌ 异常"
        print(f"  {test_name}: {status}")
    
    failed_tests = [name for name, success in results if not success]
    
    if failed_tests:
        print(f"\n⚠️ 发现问题的测试: {len(failed_tests)} 个")
        for test_name in failed_tests:
            print(f"  - {test_name}")
        
        print("\n🔧 建议检查:")
        print("  1. 可视化器的绘制方法是否正常工作")
        print("  2. 实体数据是否被正确传递到可视化器")
        print("  3. 颜色映射是否导致某些实体不可见")
        print("  4. 数据清理是否意外过滤了有效实体")
        print("  5. 画布更新机制是否正常")
    else:
        print("\n✅ 所有诊断测试通过")
        print("问题可能在于:")
        print("  1. 实际数据与测试数据的差异")
        print("  2. 界面集成层面的问题")
        print("  3. 特定条件下的边界情况")
    
    print("=" * 60)
