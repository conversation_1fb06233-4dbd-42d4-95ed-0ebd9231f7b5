#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
全面的可视化数据传递审计
检查所有文件中是否存在类似的数据传递问题
"""

import os
import re
import sys

def find_visualization_calls():
    """查找所有可视化器调用"""
    print("🔍 全面审计可视化器调用...")
    
    # 要检查的文件
    files_to_check = [
        'main_enhanced.py',
        'main_enhanced_merged.py', 
        'main_enhanced_with_v2_fill.py',
        'main_enhanced_with_v2_fill_original.py'
    ]
    
    issues_found = []
    
    for filename in files_to_check:
        if not os.path.exists(filename):
            print(f"⚠️ 文件不存在: {filename}")
            continue
            
        print(f"\n📁 检查文件: {filename}")
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # 查找 visualize_entity_group 调用
            entity_group_calls = []
            overview_calls = []
            
            for i, line in enumerate(lines):
                line_num = i + 1
                
                # 检查 visualize_entity_group 调用
                if 'visualize_entity_group(' in line:
                    entity_group_calls.append((line_num, line.strip()))
                
                # 检查 visualize_overview 调用
                if 'visualize_overview(' in line:
                    overview_calls.append((line_num, line.strip()))
            
            print(f"  📊 找到 {len(entity_group_calls)} 个 visualize_entity_group 调用")
            print(f"  📊 找到 {len(overview_calls)} 个 visualize_overview 调用")
            
            # 分析每个调用
            for line_num, line in entity_group_calls:
                issue = analyze_entity_group_call(filename, line_num, line, lines)
                if issue:
                    issues_found.append(issue)
            
            for line_num, line in overview_calls:
                issue = analyze_overview_call(filename, line_num, line, lines)
                if issue:
                    issues_found.append(issue)
                    
        except Exception as e:
            print(f"❌ 检查文件失败 {filename}: {e}")
    
    return issues_found

def analyze_entity_group_call(filename, line_num, line, all_lines):
    """分析 visualize_entity_group 调用是否有问题"""
    
    # 检查前几行是否有数据清理
    context_start = max(0, line_num - 10)
    context_lines = all_lines[context_start:line_num]
    
    has_cleaning = False
    for context_line in context_lines:
        if '_clean_group_data' in context_line or 'cleaned_group' in context_line:
            has_cleaning = True
            break
    
    # 检查是否直接传递原始组数据
    if ('current_group' in line or 'group' in line) and not has_cleaning:
        # 进一步检查是否在参数中使用了清理后的数据
        if 'cleaned_group' not in line:
            return {
                'type': 'missing_data_cleaning',
                'file': filename,
                'line': line_num,
                'code': line,
                'description': '可能缺少数据清理，直接传递原始组数据给可视化器'
            }
    
    return None

def analyze_overview_call(filename, line_num, line, all_lines):
    """分析 visualize_overview 调用是否有问题"""
    
    # 检查前几行是否有数据清理
    context_start = max(0, line_num - 10)
    context_lines = all_lines[context_start:line_num + 5]  # 包括后几行，因为参数可能在多行
    
    has_cleaning = False
    for context_line in context_lines:
        if '_clean_group_data' in context_line or 'cleaned_group' in context_line:
            has_cleaning = True
            break
    
    # 检查是否在多行参数中使用了原始组数据
    context_text = '\n'.join(context_lines)
    if ('current_group,' in context_text or 'group,' in context_text) and not has_cleaning:
        if 'cleaned_group' not in context_text:
            return {
                'type': 'missing_data_cleaning_overview',
                'file': filename,
                'line': line_num,
                'code': line,
                'description': '全图概览可能使用了未清理的组数据'
            }
    
    return None

def check_clean_group_data_method():
    """检查哪些文件有 _clean_group_data 方法"""
    print("\n🔧 检查 _clean_group_data 方法...")
    
    files_to_check = [
        'main_enhanced.py',
        'main_enhanced_merged.py', 
        'main_enhanced_with_v2_fill.py'
    ]
    
    method_status = {}
    
    for filename in files_to_check:
        if not os.path.exists(filename):
            method_status[filename] = 'file_not_found'
            continue
            
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'def _clean_group_data(' in content:
                method_status[filename] = 'has_method'
                print(f"  ✅ {filename}: 有 _clean_group_data 方法")
            else:
                method_status[filename] = 'missing_method'
                print(f"  ❌ {filename}: 缺少 _clean_group_data 方法")
                
        except Exception as e:
            method_status[filename] = 'error'
            print(f"  ❌ {filename}: 检查失败 - {e}")
    
    return method_status

def generate_fix_recommendations(issues, method_status):
    """生成修复建议"""
    print("\n📋 修复建议:")
    print("=" * 60)
    
    if not issues:
        print("✅ 没有发现数据传递问题！")
        return
    
    # 按文件分组问题
    issues_by_file = {}
    for issue in issues:
        filename = issue['file']
        if filename not in issues_by_file:
            issues_by_file[filename] = []
        issues_by_file[filename].append(issue)
    
    for filename, file_issues in issues_by_file.items():
        print(f"\n📁 {filename}:")
        
        # 检查是否缺少清理方法
        if method_status.get(filename) == 'missing_method':
            print(f"  🔧 需要添加 _clean_group_data 方法")
        
        for issue in file_issues:
            print(f"  ⚠️ 第{issue['line']}行: {issue['description']}")
            print(f"     代码: {issue['code']}")
            
            if issue['type'] == 'missing_data_cleaning':
                print(f"     建议: 在调用前添加数据清理")
                print(f"     修复: cleaned_group = self._clean_group_data(group)")
                print(f"           然后使用 cleaned_group 而不是 group")
            
            elif issue['type'] == 'missing_data_cleaning_overview':
                print(f"     建议: 确保传递给 visualize_overview 的组数据已清理")
                print(f"     修复: 使用 cleaned_group 而不是原始 group")

def test_data_cleaning_consistency():
    """测试数据清理的一致性"""
    print("\n🧪 测试数据清理一致性...")
    
    try:
        # 测试不同文件中的清理方法是否一致
        files_with_method = []
        
        for filename in ['main_enhanced.py', 'main_enhanced_merged.py', 'main_enhanced_with_v2_fill.py']:
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'def _clean_group_data(' in content:
                    files_with_method.append(filename)
        
        if len(files_with_method) > 1:
            print(f"  📊 找到 {len(files_with_method)} 个文件有清理方法")
            
            # 简单检查方法是否相似（通过长度和关键词）
            method_signatures = {}
            for filename in files_with_method:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 提取方法内容
                start = content.find('def _clean_group_data(')
                if start != -1:
                    # 找到方法结束（下一个def或类结束）
                    end = content.find('\n    def ', start + 1)
                    if end == -1:
                        end = content.find('\nclass ', start + 1)
                    if end == -1:
                        end = len(content)
                    
                    method_content = content[start:end]
                    method_signatures[filename] = len(method_content)
            
            # 检查方法长度是否相似
            lengths = list(method_signatures.values())
            if len(set(lengths)) == 1:
                print(f"  ✅ 所有清理方法长度一致")
            else:
                print(f"  ⚠️ 清理方法长度不一致: {method_signatures}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CAD分类标注工具 - 可视化数据传递全面审计")
    print("=" * 60)
    
    # 1. 查找所有可视化器调用
    issues = find_visualization_calls()
    
    # 2. 检查清理方法状态
    method_status = check_clean_group_data_method()
    
    # 3. 生成修复建议
    generate_fix_recommendations(issues, method_status)
    
    # 4. 测试一致性
    test_data_cleaning_consistency()
    
    print("\n" + "=" * 60)
    print("审计总结:")
    print("=" * 60)
    
    if issues:
        print(f"❌ 发现 {len(issues)} 个潜在问题")
        print("建议按照上述修复建议进行修复")
    else:
        print("✅ 没有发现明显的数据传递问题")
    
    missing_methods = [f for f, status in method_status.items() if status == 'missing_method']
    if missing_methods:
        print(f"⚠️ {len(missing_methods)} 个文件缺少数据清理方法")
    
    print("=" * 60)
