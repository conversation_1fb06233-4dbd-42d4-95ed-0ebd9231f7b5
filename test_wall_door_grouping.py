#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试墙体和门窗分组显示问题
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_enhanced import EnhancedCADProcessor

def create_comprehensive_test_data():
    """创建全面的测试数据"""
    print("🏗️ 创建全面的测试数据...")
    
    entities = []
    
    # ========== 墙体线条 (A-WALL图层) ==========
    print("  📐 创建墙体线条...")
    
    # 墙体组1: 外墙矩形
    wall_group1 = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (6000, 0)], 'color': 7, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(6000, 0), (6000, 4000)], 'color': 7, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(6000, 4000), (0, 4000)], 'color': 7, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 4000), (0, 0)], 'color': 7, 'linetype': 'CONTINUOUS'},
    ]
    
    # 墙体组2: 内墙
    wall_group2 = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(3000, 0), (3000, 4000)], 'color': 7, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 2000), (3000, 2000)], 'color': 7, 'linetype': 'CONTINUOUS'},
    ]
    
    # 墙体组3: 独立墙段
    wall_group3 = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(8000, 1000), (10000, 1000)], 'color': 7, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(10000, 1000), (10000, 3000)], 'color': 7, 'linetype': 'CONTINUOUS'},
    ]
    
    entities.extend(wall_group1)
    entities.extend(wall_group2)
    entities.extend(wall_group3)
    print(f"    ✅ 墙体线条: {len(wall_group1 + wall_group2 + wall_group3)} 条")
    
    # ========== 门窗线条 (A-WINDOW, A-DOOR图层) ==========
    print("  🚪 创建门窗线条...")
    
    # 窗户组1
    window_group1 = [
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(1000, -50), (2000, -50)], 'color': 3, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(1000, 50), (2000, 50)], 'color': 3, 'linetype': 'CONTINUOUS'},
    ]
    
    # 窗户组2
    window_group2 = [
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(4000, -50), (5000, -50)], 'color': 3, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(4000, 50), (5000, 50)], 'color': 3, 'linetype': 'CONTINUOUS'},
    ]
    
    # 门组1
    door_group1 = [
        {'type': 'ARC', 'layer': 'A-DOOR', 'center': (6050, 1500), 'radius': 800, 'start_angle': 0, 'end_angle': 90, 'color': 2},
        {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(6050, 1500), (6850, 1500)], 'color': 2, 'linetype': 'CONTINUOUS'},
    ]
    
    entities.extend(window_group1)
    entities.extend(window_group2)
    entities.extend(door_group1)
    print(f"    ✅ 门窗线条: {len(window_group1 + window_group2 + door_group1)} 条")
    
    # ========== 其他线条 ==========
    print("  📏 创建其他线条...")
    
    # 尺寸标注
    dimension_group = [
        {'type': 'LINE', 'layer': 'A-DIMS', 'points': [(0, -500), (6000, -500)], 'color': 1, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-DIMS', 'points': [(0, -450), (0, -550)], 'color': 1, 'linetype': 'CONTINUOUS'},
    ]
    
    # 家具
    furniture_group = [
        {'type': 'LINE', 'layer': 'A-FURN', 'points': [(500, 500), (1500, 500)], 'color': 5, 'linetype': 'CONTINUOUS'},
        {'type': 'LINE', 'layer': 'A-FURN', 'points': [(1500, 500), (1500, 1500)], 'color': 5, 'linetype': 'CONTINUOUS'},
    ]
    
    entities.extend(dimension_group)
    entities.extend(furniture_group)
    print(f"    ✅ 其他线条: {len(dimension_group + furniture_group)} 条")
    
    print(f"  📊 总实体数: {len(entities)}")
    return entities

def test_grouping_process():
    """测试分组过程"""
    print("\n🔧 测试分组过程")
    print("="*60)
    
    # 创建测试数据
    test_entities = create_comprehensive_test_data()
    
    # 创建处理器
    processor = EnhancedCADProcessor()
    processor.entities = test_entities
    processor.all_groups = []
    processor.auto_labeled_entities = []
    processor.labeled_entities = []
    
    print(f"\n📋 开始分组测试:")
    
    # 1. 测试特殊图层识别
    print(f"1. 特殊图层识别:")
    wall_layers = processor.processor._detect_special_layers(
        test_entities, processor.processor.wall_layer_patterns, debug=False, layer_type="墙体"
    )
    door_window_layers = processor.processor._detect_special_layers(
        test_entities, processor.processor.door_window_layer_patterns, debug=False, layer_type="门窗"
    )
    
    print(f"   墙体图层: {wall_layers}")
    print(f"   门窗图层: {door_window_layers}")
    
    # 2. 测试墙体分组
    print(f"\n2. 墙体分组:")
    wall_entities = [e for e in test_entities if e['layer'] in wall_layers]
    print(f"   墙体实体数: {len(wall_entities)}")
    
    if wall_entities:
        wall_groups = processor.processor._group_special_entities_by_layer(
            wall_entities, connection_threshold=10, entity_type="wall"
        )
        print(f"   墙体分组数: {len(wall_groups)}")
        
        for i, group in enumerate(wall_groups):
            if isinstance(group, dict):
                entities = group.get('entities', [])
                print(f"     组 {i+1}: {len(entities)} 个实体, 类型: {group.get('group_type')}")
            else:
                print(f"     组 {i+1}: {len(group)} 个实体, 格式: 列表")
    
    # 3. 测试门窗分组
    print(f"\n3. 门窗分组:")
    door_window_entities = [e for e in test_entities if e['layer'] in door_window_layers]
    print(f"   门窗实体数: {len(door_window_entities)}")
    
    if door_window_entities:
        door_window_groups = processor.processor._group_special_entities_by_layer(
            door_window_entities, connection_threshold=100, entity_type="door_window"
        )
        print(f"   门窗分组数: {len(door_window_groups)}")
        
        for i, group in enumerate(door_window_groups):
            if isinstance(group, dict):
                entities = group.get('entities', [])
                print(f"     组 {i+1}: {len(entities)} 个实体, 类型: {group.get('group_type')}")
            else:
                print(f"     组 {i+1}: {len(group)} 个实体, 格式: 列表")
    
    # 4. 测试完整的自动处理流程
    print(f"\n4. 完整自动处理流程:")
    try:
        # 模拟 _auto_process_special_entities 的关键部分
        auto_groups = []
        
        # 处理墙体
        if wall_entities:
            for group in wall_groups:
                # 获取实体列表（处理字典格式的组）
                entities_list = group.get('entities', []) if isinstance(group, dict) else group
                
                # 自动标注墙体
                for entity in entities_list:
                    if isinstance(entity, dict):
                        entity['label'] = '墙体'
                        entity['auto_labeled'] = True
                        entity['confidence'] = 0.9
                
                # 添加到数据集
                processor.dataset.append({
                    'features': {'entity_count': len(entities_list), 'layer': 'A-WALL'},
                    'label': '墙体',
                    'source_file': 'test.dxf',
                    'entity_count': len(entities_list),
                    'auto_labeled': True
                })
            
            # 提取实体列表并添加到auto_groups
            wall_entity_groups = [group.get('entities', []) if isinstance(group, dict) else group for group in wall_groups]
            auto_groups.extend(wall_entity_groups)
            processor.auto_labeled_entities.extend(wall_entities)
        
        # 处理门窗
        if door_window_entities:
            for group in door_window_groups:
                # 获取实体列表（处理字典格式的组）
                entities_list = group.get('entities', []) if isinstance(group, dict) else group
                
                # 自动标注门窗
                for entity in entities_list:
                    if isinstance(entity, dict):
                        entity['label'] = '门窗'
                        entity['auto_labeled'] = True
                        entity['confidence'] = 0.8
                
                # 添加到数据集
                processor.dataset.append({
                    'features': {'entity_count': len(entities_list), 'layer': entity.get('layer', 'unknown')},
                    'label': '门窗',
                    'source_file': 'test.dxf',
                    'entity_count': len(entities_list),
                    'auto_labeled': True
                })
            
            # 提取实体列表并添加到auto_groups
            door_window_entity_groups = [group.get('entities', []) if isinstance(group, dict) else group for group in door_window_groups]
            auto_groups.extend(door_window_entity_groups)
            processor.auto_labeled_entities.extend(door_window_entities)
        
        # 处理其他实体
        processed_entity_ids = set()
        processed_entity_ids.update(id(e) for e in wall_entities)
        processed_entity_ids.update(id(e) for e in door_window_entities)
        other_entities = [e for e in test_entities if id(e) not in processed_entity_ids]
        
        if other_entities:
            other_groups = processor.processor._group_other_entities_by_layer(other_entities, distance_threshold=100)
            auto_groups.extend(other_groups)
        
        # 设置所有组
        processor.all_groups = auto_groups

        # 重要：调用 _update_groups_info() 来更新组信息
        processor._update_groups_info()

        print(f"   自动分组完成:")
        print(f"     总组数: {len(auto_groups)}")
        print(f"     墙体实体: {len([e for e in processor.auto_labeled_entities if e.get('label') == '墙体'])}")
        print(f"     门窗实体: {len([e for e in processor.auto_labeled_entities if e.get('label') == '门窗'])}")
        print(f"     数据集记录: {len(processor.dataset)}")
        print(f"     组信息数量: {len(processor.groups_info)}")

        return processor, auto_groups
        
    except Exception as e:
        print(f"   ❌ 自动处理失败: {e}")
        import traceback
        traceback.print_exc()
        return None, []

def test_display_data():
    """测试显示数据"""
    print(f"\n🎨 测试显示数据")
    print("="*60)
    
    processor, auto_groups = test_grouping_process()
    
    if not processor:
        print("❌ 处理器创建失败")
        return
    
    # 测试组信息获取
    print(f"1. 组信息获取:")
    try:
        groups_info = processor.get_groups_info()
        print(f"   组信息数量: {len(groups_info)}")
        
        for i, info in enumerate(groups_info[:5]):  # 只显示前5个
            print(f"     组 {i+1}: 状态={info.get('status')}, 类型={info.get('type')}, 实体数={info.get('entity_count')}")
        
    except Exception as e:
        print(f"   ❌ 组信息获取失败: {e}")
    
    # 测试待处理组更新
    print(f"\n2. 待处理组更新:")
    try:
        processor._update_pending_manual_groups()
        print(f"   待处理组数: {len(processor.pending_manual_groups)}")
        
    except Exception as e:
        print(f"   ❌ 待处理组更新失败: {e}")
    
    # 测试可视化数据准备
    print(f"\n3. 可视化数据准备:")
    try:
        # 统计各类实体
        wall_count = len([e for e in processor.auto_labeled_entities if e.get('label') == '墙体'])
        door_window_count = len([e for e in processor.auto_labeled_entities if e.get('label') == '门窗'])
        
        print(f"   墙体实体: {wall_count} 个")
        print(f"   门窗实体: {door_window_count} 个")
        print(f"   总自动标注实体: {len(processor.auto_labeled_entities)} 个")
        
        # 检查实体是否有正确的标签
        labeled_entities = [e for e in processor.auto_labeled_entities if e.get('label')]
        print(f"   有标签的实体: {len(labeled_entities)} 个")
        
        if labeled_entities:
            labels = {}
            for entity in labeled_entities:
                label = entity.get('label')
                labels[label] = labels.get(label, 0) + 1
            print(f"   标签分布: {labels}")
        
    except Exception as e:
        print(f"   ❌ 可视化数据准备失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🚀 开始测试墙体和门窗分组显示")
    
    try:
        test_display_data()
        
        print(f"\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
