#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CAD显示集成测试程序
专门测试CAD处理器与显示组件的集成
包括实际的文件处理和分组过程中的显示测试
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import os
from main_enhanced_with_v2_fill import EnhancedCADProcessor
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import numpy as np
import math

class CADDisplayIntegrationTest:
    """CAD显示集成测试类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("CAD显示集成测试 - 实际处理流程测试")
        self.root.geometry("1600x1000")
        
        # 测试状态
        self.processor = None
        self.current_entities = []
        self.current_groups = []
        self.test_results = []
        
        # 显示状态
        self.detail_canvas = None
        self.overview_canvas = None
        
        self.create_interface()
        
    def create_interface(self):
        """创建测试界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部控制区域
        control_frame = ttk.LabelFrame(main_frame, text="CAD处理流程测试")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 控制按钮
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(btn_frame, text="1. 加载DXF文件", 
                  command=self.test_load_file).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(btn_frame, text="2. 测试线条处理显示", 
                  command=self.test_line_processing_display).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(btn_frame, text="3. 测试分组过程显示", 
                  command=self.test_grouping_process_display).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(btn_frame, text="4. 测试实时更新显示", 
                  command=self.test_realtime_display).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(btn_frame, text="5. 测试多窗口显示", 
                  command=self.test_multi_window_display).pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        status_frame = ttk.Frame(control_frame)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var, 
                                     foreground="blue")
        self.status_label.pack(side=tk.LEFT, padx=(5, 0))
        
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 显示区域
        display_frame = ttk.Frame(main_frame)
        display_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧详细视图
        left_frame = ttk.LabelFrame(display_frame, text="实体详细视图")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        self.detail_frame = ttk.Frame(left_frame)
        self.detail_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 右侧概览和结果
        right_frame = ttk.Frame(display_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 概览视图
        overview_frame = ttk.LabelFrame(right_frame, text="全图概览")
        overview_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        self.overview_frame = ttk.Frame(overview_frame)
        self.overview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 测试结果
        result_frame = ttk.LabelFrame(right_frame, text="测试结果")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        # 结果文本框
        self.result_text = tk.Text(result_frame, height=15, wrap=tk.WORD, font=("Consolas", 9))
        result_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, 
                                        command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=result_scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
    def log_result(self, test_name, result, details=""):
        """记录测试结果"""
        timestamp = time.strftime("%H:%M:%S")
        status = "✅ 通过" if result else "❌ 失败"
        
        log_entry = f"[{timestamp}] {test_name}: {status}\n"
        if details:
            log_entry += f"  详情: {details}\n"
        log_entry += "-" * 50 + "\n"
        
        self.result_text.insert(tk.END, log_entry)
        self.result_text.see(tk.END)
        self.root.update()
        
        self.test_results.append({
            'test': test_name,
            'result': result,
            'details': details,
            'timestamp': timestamp
        })
    
    def test_load_file(self):
        """测试文件加载和显示"""
        file_path = filedialog.askopenfilename(
            title="选择DXF文件进行测试",
            filetypes=[("DXF files", "*.dxf"), ("All files", "*.*")]
        )
        
        if not file_path:
            return
        
        self.status_var.set("正在加载文件...")
        self.progress.start()
        
        def load_file():
            try:
                # 创建处理器
                self.processor = EnhancedCADProcessor()
                
                # 设置状态回调
                def status_callback(msg_type, message):
                    self.status_var.set(message)
                    self.root.update()
                
                self.processor.status_callback = status_callback
                
                # 加载文件
                success = self.processor.load_file(file_path)
                
                if success and hasattr(self.processor, 'entities'):
                    self.current_entities = self.processor.entities
                    
                    # 显示加载结果
                    self.display_entities(self.current_entities, "文件加载")
                    
                    self.log_result("文件加载测试", True, 
                                  f"成功加载 {len(self.current_entities)} 个实体")
                else:
                    self.log_result("文件加载测试", False, "文件加载失败或无实体数据")
                    
            except Exception as e:
                self.log_result("文件加载测试", False, f"错误: {str(e)}")
            
            finally:
                self.progress.stop()
                self.status_var.set("就绪")
        
        threading.Thread(target=load_file, daemon=True).start()
    
    def test_line_processing_display(self):
        """测试线条处理过程显示"""
        if not self.processor or not self.current_entities:
            messagebox.showwarning("警告", "请先加载DXF文件")
            return
        
        self.status_var.set("测试线条处理显示...")
        self.progress.start()
        
        def process_lines():
            try:
                # 模拟线条处理过程
                original_count = len(self.current_entities)
                
                # 第一阶段：显示原始实体
                self.status_var.set("显示原始实体...")
                self.display_entities(self.current_entities, "原始实体")
                time.sleep(1)
                
                # 第二阶段：执行线条处理
                self.status_var.set("执行线条处理...")
                success = self.processor.process_lines()
                
                if success and hasattr(self.processor, 'merged_entities'):
                    processed_entities = self.processor.merged_entities
                    processed_count = len(processed_entities)
                    
                    # 显示处理后的实体
                    self.status_var.set("显示处理后实体...")
                    self.display_entities(processed_entities, "线条处理后")
                    
                    self.log_result("线条处理显示测试", True, 
                                  f"原始: {original_count} 个实体 -> 处理后: {processed_count} 个实体")
                else:
                    self.log_result("线条处理显示测试", False, "线条处理失败")
                    
            except Exception as e:
                self.log_result("线条处理显示测试", False, f"错误: {str(e)}")
            
            finally:
                self.progress.stop()
                self.status_var.set("就绪")
        
        threading.Thread(target=process_lines, daemon=True).start()
    
    def test_grouping_process_display(self):
        """测试分组过程显示"""
        if not self.processor:
            messagebox.showwarning("警告", "请先加载DXF文件并处理线条")
            return
        
        self.status_var.set("测试分组过程显示...")
        self.progress.start()
        
        def process_grouping():
            try:
                # 执行分组处理
                self.status_var.set("执行分组处理...")
                success = self.processor.process_grouping()
                
                if success and hasattr(self.processor, 'all_groups'):
                    self.current_groups = self.processor.all_groups
                    
                    # 显示分组结果
                    self.status_var.set("显示分组结果...")
                    self.display_groups(self.current_groups)
                    
                    self.log_result("分组过程显示测试", True, 
                                  f"成功创建 {len(self.current_groups)} 个组")
                else:
                    self.log_result("分组过程显示测试", False, "分组处理失败")
                    
            except Exception as e:
                self.log_result("分组过程显示测试", False, f"错误: {str(e)}")
            
            finally:
                self.progress.stop()
                self.status_var.set("就绪")
        
        threading.Thread(target=process_grouping, daemon=True).start()

    def display_entities(self, entities, title="实体显示"):
        """显示实体"""
        try:
            # 清除之前的显示
            for widget in self.detail_frame.winfo_children():
                widget.destroy()

            if not entities:
                ttk.Label(self.detail_frame, text="无实体数据").pack(expand=True)
                return

            # 创建matplotlib图形
            fig, ax = plt.subplots(figsize=(8, 6))

            # 颜色映射
            layer_colors = {}
            colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']

            # 绘制实体
            for i, entity in enumerate(entities[:100]):  # 限制显示数量以提高性能
                layer = entity.get('layer', 'UNKNOWN')
                if layer not in layer_colors:
                    layer_colors[layer] = colors[len(layer_colors) % len(colors)]

                color = layer_colors[layer]

                try:
                    if entity.get('type') == 'LINE' and 'points' in entity:
                        points = entity['points']
                        if len(points) >= 2:
                            ax.plot([points[0][0], points[1][0]],
                                   [points[0][1], points[1][1]],
                                   color=color, linewidth=1, alpha=0.7)

                    elif entity.get('type') == 'ARC' and 'center' in entity:
                        center = entity['center']
                        radius = entity.get('radius', 10)
                        start_angle = math.radians(entity.get('start_angle', 0))
                        end_angle = math.radians(entity.get('end_angle', 360))

                        angles = np.linspace(start_angle, end_angle, 50)
                        x = center[0] + radius * np.cos(angles)
                        y = center[1] + radius * np.sin(angles)
                        ax.plot(x, y, color=color, linewidth=1, alpha=0.7)

                    elif entity.get('type') == 'CIRCLE' and 'center' in entity:
                        center = entity['center']
                        radius = entity.get('radius', 10)
                        circle = plt.Circle(center, radius, fill=False, color=color, linewidth=1, alpha=0.7)
                        ax.add_patch(circle)

                    elif entity.get('type') in ('LWPOLYLINE', 'POLYLINE') and 'points' in entity:
                        points = entity['points']
                        if len(points) >= 2:
                            x_coords = [p[0] for p in points]
                            y_coords = [p[1] for p in points]
                            ax.plot(x_coords, y_coords, color=color, linewidth=1, alpha=0.7)

                except Exception as e:
                    continue  # 跳过有问题的实体

            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            ax.set_title(f"{title} ({len(entities)} 个实体)", fontsize=12, fontweight='bold')

            # 添加图例
            if layer_colors:
                legend_elements = [plt.Line2D([0], [0], color=color, lw=2, label=layer)
                                 for layer, color in list(layer_colors.items())[:10]]  # 限制图例数量
                ax.legend(handles=legend_elements, loc='upper right', fontsize=8)

            # 嵌入到tkinter
            self.detail_canvas = FigureCanvasTkAgg(fig, self.detail_frame)
            self.detail_canvas.draw()
            self.detail_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            # 添加工具栏
            toolbar = NavigationToolbar2Tk(self.detail_canvas, self.detail_frame)
            toolbar.update()

            # 更新概览
            self.update_overview(entities, title)

        except Exception as e:
            self.log_result(f"{title}显示", False, f"显示错误: {str(e)}")

    def display_groups(self, groups):
        """显示分组结果"""
        try:
            # 清除之前的显示
            for widget in self.detail_frame.winfo_children():
                widget.destroy()

            if not groups:
                ttk.Label(self.detail_frame, text="无分组数据").pack(expand=True)
                return

            # 创建matplotlib图形
            fig, ax = plt.subplots(figsize=(8, 6))

            # 为每个组分配不同颜色
            group_colors = plt.cm.Set3(np.linspace(0, 1, len(groups)))

            total_entities = 0

            for group_idx, group in enumerate(groups):
                color = group_colors[group_idx]

                # 处理不同格式的组
                if isinstance(group, dict):
                    entities = group.get('entities', [])
                    group_label = group.get('label', f'组{group_idx+1}')
                elif isinstance(group, list):
                    entities = group
                    group_label = f'组{group_idx+1}'
                else:
                    continue

                total_entities += len(entities)

                # 绘制组中的实体
                for entity in entities:
                    try:
                        if entity.get('type') == 'LINE' and 'points' in entity:
                            points = entity['points']
                            if len(points) >= 2:
                                ax.plot([points[0][0], points[1][0]],
                                       [points[0][1], points[1][1]],
                                       color=color, linewidth=2, alpha=0.8, label=group_label if entity == entities[0] else "")

                        elif entity.get('type') == 'ARC' and 'center' in entity:
                            center = entity['center']
                            radius = entity.get('radius', 10)
                            start_angle = math.radians(entity.get('start_angle', 0))
                            end_angle = math.radians(entity.get('end_angle', 360))

                            angles = np.linspace(start_angle, end_angle, 50)
                            x = center[0] + radius * np.cos(angles)
                            y = center[1] + radius * np.sin(angles)
                            ax.plot(x, y, color=color, linewidth=2, alpha=0.8, label=group_label if entity == entities[0] else "")

                        elif entity.get('type') == 'CIRCLE' and 'center' in entity:
                            center = entity['center']
                            radius = entity.get('radius', 10)
                            circle = plt.Circle(center, radius, fill=False, color=color, linewidth=2, alpha=0.8)
                            ax.add_patch(circle)
                            if entity == entities[0]:
                                ax.plot([], [], color=color, linewidth=2, label=group_label)

                    except Exception as e:
                        continue

            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            ax.set_title(f"分组结果 ({len(groups)} 个组, {total_entities} 个实体)", fontsize=12, fontweight='bold')

            # 添加图例
            ax.legend(loc='upper right', fontsize=8)

            # 嵌入到tkinter
            self.detail_canvas = FigureCanvasTkAgg(fig, self.detail_frame)
            self.detail_canvas.draw()
            self.detail_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            # 添加工具栏
            toolbar = NavigationToolbar2Tk(self.detail_canvas, self.detail_frame)
            toolbar.update()

        except Exception as e:
            self.log_result("分组显示", False, f"显示错误: {str(e)}")

    def update_overview(self, entities, title="概览"):
        """更新概览视图"""
        try:
            # 清除之前的显示
            for widget in self.overview_frame.winfo_children():
                widget.destroy()

            if not entities:
                return

            # 创建简化的概览图
            fig, ax = plt.subplots(figsize=(6, 4))

            # 简化显示，只显示轮廓
            for entity in entities[::5]:  # 每5个实体显示一个
                try:
                    if entity.get('type') == 'LINE' and 'points' in entity:
                        points = entity['points']
                        if len(points) >= 2:
                            ax.plot([points[0][0], points[1][0]],
                                   [points[0][1], points[1][1]],
                                   'k-', linewidth=0.5, alpha=0.5)
                except Exception:
                    continue

            ax.set_aspect('equal')
            ax.set_title(f"{title} - 概览", fontsize=10)
            ax.axis('off')  # 隐藏坐标轴

            # 嵌入到tkinter
            self.overview_canvas = FigureCanvasTkAgg(fig, self.overview_frame)
            self.overview_canvas.draw()
            self.overview_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        except Exception as e:
            pass  # 概览显示失败不影响主要功能

    def test_realtime_display(self):
        """测试实时更新显示"""
        if not self.current_entities:
            messagebox.showwarning("警告", "请先加载实体数据")
            return

        self.status_var.set("测试实时显示更新...")
        self.progress.start()

        def realtime_test():
            try:
                # 模拟实时处理过程
                batch_size = max(1, len(self.current_entities) // 10)

                for i in range(0, len(self.current_entities), batch_size):
                    if i + batch_size > len(self.current_entities):
                        batch = self.current_entities[i:]
                    else:
                        batch = self.current_entities[i:i+batch_size]

                    # 更新显示
                    self.status_var.set(f"处理进度: {i+len(batch)}/{len(self.current_entities)}")
                    self.display_entities(self.current_entities[:i+len(batch)],
                                        f"实时更新 ({i+len(batch)}/{len(self.current_entities)})")

                    time.sleep(0.2)  # 模拟处理时间
                    self.root.update()

                self.log_result("实时显示测试", True,
                              f"成功实时更新显示 {len(self.current_entities)} 个实体")

            except Exception as e:
                self.log_result("实时显示测试", False, f"错误: {str(e)}")

            finally:
                self.progress.stop()
                self.status_var.set("就绪")

        threading.Thread(target=realtime_test, daemon=True).start()

    def test_multi_window_display(self):
        """测试多窗口显示"""
        if not self.current_entities:
            messagebox.showwarning("警告", "请先加载实体数据")
            return

        try:
            # 创建新窗口
            multi_window = tk.Toplevel(self.root)
            multi_window.title("多窗口显示测试")
            multi_window.geometry("800x600")

            # 创建多个显示区域
            notebook = ttk.Notebook(multi_window)
            notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 按图层分别显示
            layers = set(entity.get('layer', 'UNKNOWN') for entity in self.current_entities)

            for layer in list(layers)[:5]:  # 限制显示的图层数量
                layer_frame = ttk.Frame(notebook)
                notebook.add(layer_frame, text=f"图层: {layer}")

                # 过滤该图层的实体
                layer_entities = [e for e in self.current_entities if e.get('layer') == layer]

                if layer_entities:
                    # 创建图形
                    fig, ax = plt.subplots(figsize=(6, 4))

                    for entity in layer_entities[:50]:  # 限制显示数量
                        try:
                            if entity.get('type') == 'LINE' and 'points' in entity:
                                points = entity['points']
                                if len(points) >= 2:
                                    ax.plot([points[0][0], points[1][0]],
                                           [points[0][1], points[1][1]],
                                           'b-', linewidth=1)
                        except Exception:
                            continue

                    ax.set_aspect('equal')
                    ax.grid(True, alpha=0.3)
                    ax.set_title(f"图层 {layer} ({len(layer_entities)} 个实体)")

                    # 嵌入到窗口
                    canvas = FigureCanvasTkAgg(fig, layer_frame)
                    canvas.draw()
                    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            self.log_result("多窗口显示测试", True,
                          f"成功创建多窗口显示，包含 {len(layers)} 个图层")

        except Exception as e:
            self.log_result("多窗口显示测试", False, f"错误: {str(e)}")

    def run_comprehensive_test(self):
        """运行综合测试"""
        self.status_var.set("运行综合测试...")

        def comprehensive_test():
            try:
                # 清除之前的结果
                self.result_text.delete(1.0, tk.END)

                # 测试序列
                tests = [
                    ("窗口适配性", self.test_window_adaptation),
                    ("显示性能", self.test_display_performance),
                    ("内存使用", self.test_memory_usage),
                    ("错误处理", self.test_error_handling)
                ]

                for test_name, test_func in tests:
                    self.status_var.set(f"正在运行: {test_name}")
                    test_func()
                    time.sleep(1)

                # 生成综合报告
                self.generate_comprehensive_report()

            except Exception as e:
                self.log_result("综合测试", False, f"测试失败: {str(e)}")

            finally:
                self.status_var.set("综合测试完成")

        threading.Thread(target=comprehensive_test, daemon=True).start()

    def test_window_adaptation(self):
        """测试窗口适配性"""
        try:
            original_size = (self.root.winfo_width(), self.root.winfo_height())
            test_sizes = [(800, 600), (1200, 800), (1600, 1000)]

            adaptation_success = True

            for width, height in test_sizes:
                self.root.geometry(f"{width}x{height}")
                self.root.update()
                time.sleep(0.5)

                # 检查界面元素是否可见
                if not (self.detail_frame.winfo_viewable() and
                       self.overview_frame.winfo_viewable()):
                    adaptation_success = False
                    break

            # 恢复原始大小
            self.root.geometry(f"{original_size[0]}x{original_size[1]}")

            self.log_result("窗口适配性测试", adaptation_success,
                          "测试了多种窗口大小的适配性")

        except Exception as e:
            self.log_result("窗口适配性测试", False, f"错误: {str(e)}")

    def test_display_performance(self):
        """测试显示性能"""
        try:
            if not self.current_entities:
                self.log_result("显示性能测试", False, "无测试数据")
                return

            # 测试大量实体的显示性能
            start_time = time.time()

            # 显示实体
            self.display_entities(self.current_entities[:500], "性能测试")

            end_time = time.time()
            display_time = end_time - start_time

            # 性能评估
            if display_time < 2.0:
                performance = "优秀"
            elif display_time < 5.0:
                performance = "良好"
            else:
                performance = "需要优化"

            self.log_result("显示性能测试", True,
                          f"显示时间: {display_time:.2f}秒, 性能评级: {performance}")

        except Exception as e:
            self.log_result("显示性能测试", False, f"错误: {str(e)}")

    def test_memory_usage(self):
        """测试内存使用"""
        try:
            import psutil
            import os

            process = psutil.Process(os.getpid())
            memory_before = process.memory_info().rss / 1024 / 1024  # MB

            # 执行显示操作
            if self.current_entities:
                self.display_entities(self.current_entities, "内存测试")

            memory_after = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = memory_after - memory_before

            self.log_result("内存使用测试", True,
                          f"内存使用: {memory_before:.1f}MB -> {memory_after:.1f}MB (增加 {memory_increase:.1f}MB)")

        except ImportError:
            self.log_result("内存使用测试", False, "需要安装psutil库")
        except Exception as e:
            self.log_result("内存使用测试", False, f"错误: {str(e)}")

    def test_error_handling(self):
        """测试错误处理"""
        try:
            # 测试空数据
            self.display_entities([], "空数据测试")

            # 测试错误数据
            bad_entities = [
                {'type': 'LINE'},  # 缺少points
                {'type': 'CIRCLE'},  # 缺少center
                {'invalid': 'data'}  # 无效数据
            ]
            self.display_entities(bad_entities, "错误数据测试")

            self.log_result("错误处理测试", True, "成功处理各种错误情况")

        except Exception as e:
            self.log_result("错误处理测试", False, f"错误: {str(e)}")

    def generate_comprehensive_report(self):
        """生成综合测试报告"""
        if not self.test_results:
            return

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['result'])

        report = f"""
{'='*60}
CAD显示集成测试报告
{'='*60}
测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
总测试数: {total_tests}
通过测试: {passed_tests}
失败测试: {total_tests - passed_tests}
成功率: {(passed_tests/total_tests*100):.1f}%

测试详情:
"""

        for result in self.test_results:
            status = "✅" if result['result'] else "❌"
            report += f"{status} {result['test']}: {result['details']}\n"

        report += f"\n{'='*60}\n"

        self.result_text.insert(tk.END, report)
        self.result_text.see(tk.END)

    def run(self):
        """运行测试应用"""
        # 添加综合测试按钮
        btn_frame = self.root.children['!frame'].children['!labelframe'].children['!frame']
        ttk.Button(btn_frame, text="运行综合测试",
                  command=self.run_comprehensive_test).pack(side=tk.LEFT, padx=5)

        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = CADDisplayIntegrationTest()
        app.run()
    except Exception as e:
        print(f"应用启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
