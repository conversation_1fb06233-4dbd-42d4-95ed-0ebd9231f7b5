#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
详细调试线条合并算法
"""

import sys
import os
import math
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from line_merger import SimpleLineMerger

def debug_connection_detection():
    """调试连接检测逻辑"""
    print("=" * 60)
    print("🔍 调试连接检测逻辑")
    print("=" * 60)
    
    # 创建测试数据 - 两条应该能连接的线段
    test_lines = [
        [(0.0, 0.0), (100.0, 0.0)],    # 第一条线
        [(100.0, 0.0), (200.0, 0.0)]   # 第二条线，精确连接
    ]
    
    print(f"测试线段:")
    for i, line in enumerate(test_lines):
        print(f"  线段 {i+1}: {line[0]} -> {line[1]}")
    
    # 创建合并器
    merger = SimpleLineMerger(
        distance_threshold=10,  # 10mm连接阈值
        angle_threshold=5,      # 5度角度阈值
        enable_iterative=False  # 禁用迭代，只测试单次合并
    )
    
    # 手动测试连接检测
    print(f"\n🔧 测试连接检测:")
    print(f"  距离阈值: {merger.dist_thresh}mm")
    print(f"  角度阈值: {merger.angle_thresh}度")
    
    # 测试 _can_connect_simple 方法
    can_connect = merger._can_connect_simple(test_lines[0], test_lines[1])
    print(f"  连接检测结果: {can_connect}")
    
    # 详细分析连接检测
    line1, line2 = test_lines[0], test_lines[1]
    start1, end1 = line1[0], line1[-1]
    start2, end2 = line2[0], line2[-1]
    
    print(f"\n📐 端点分析:")
    print(f"  线段1端点: 起点{start1}, 终点{end1}")
    print(f"  线段2端点: 起点{start2}, 终点{end2}")
    
    # 计算所有端点间距离
    connections = [
        ("线段1起点", "线段2起点", start1, start2),
        ("线段1起点", "线段2终点", start1, end2),
        ("线段1终点", "线段2起点", end1, start2),
        ("线段1终点", "线段2终点", end1, end2)
    ]
    
    min_distance = float('inf')
    closest_connection = None
    
    for desc1, desc2, p1, p2 in connections:
        dist = math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
        print(f"  {desc1} <-> {desc2}: 距离 = {dist:.3f}mm")
        if dist < min_distance:
            min_distance = dist
            closest_connection = (desc1, desc2)
    
    print(f"  最小距离: {min_distance:.3f}mm (阈值: {merger.dist_thresh}mm)")
    print(f"  最近连接: {closest_connection[0]} <-> {closest_connection[1]}")
    print(f"  距离检测通过: {min_distance <= merger.dist_thresh}")
    
    # 测试平行检测
    is_parallel = merger._are_parallel_simple(line1, line2)
    print(f"\n📏 平行检测:")
    print(f"  平行检测结果: {is_parallel}")
    
    # 详细分析平行检测
    dx1, dy1 = end1[0] - start1[0], end1[1] - start1[1]
    dx2, dy2 = end2[0] - start2[0], end2[1] - start2[1]
    
    print(f"  线段1方向向量: ({dx1}, {dy1})")
    print(f"  线段2方向向量: ({dx2}, {dy2})")
    
    # 计算角度
    length1 = math.sqrt(dx1**2 + dy1**2)
    length2 = math.sqrt(dx2**2 + dy2**2)
    
    if length1 > 1e-5 and length2 > 1e-5:
        dot_product = (dx1 * dx2 + dy1 * dy2) / (length1 * length2)
        dot_product = max(-1, min(1, abs(dot_product)))  # 限制在[-1, 1]范围内
        angle_rad = math.acos(dot_product)
        angle_deg = math.degrees(angle_rad)
        
        print(f"  点积: {dot_product:.6f}")
        print(f"  夹角: {angle_deg:.3f}度 (阈值: {merger.angle_thresh}度)")
        print(f"  平行检测通过: {angle_deg < merger.angle_thresh}")
    else:
        print(f"  ⚠️ 线段长度过短，无法计算角度")
    
    return can_connect

def debug_graph_building():
    """调试图构建逻辑"""
    print("\n" + "=" * 60)
    print("🔗 调试图构建逻辑")
    print("=" * 60)
    
    # 创建更复杂的测试数据
    test_lines = [
        [(0.0, 0.0), (100.0, 0.0)],      # 线段0
        [(100.0, 0.0), (200.0, 0.0)],    # 线段1，与线段0连接
        [(200.0, 0.0), (300.0, 0.0)],    # 线段2，与线段1连接
        [(0.0, 100.0), (100.0, 100.0)],  # 线段3，独立的平行线段
        [(500.0, 500.0), (600.0, 600.0)] # 线段4，完全独立的斜线
    ]
    
    print(f"测试线段:")
    for i, line in enumerate(test_lines):
        print(f"  线段 {i}: {line[0]} -> {line[1]}")
    
    # 创建合并器
    merger = SimpleLineMerger(
        distance_threshold=10,
        angle_threshold=5,
        enable_iterative=False
    )
    
    # 构建连接图
    graph = merger._build_simple_line_graph(test_lines)
    
    print(f"\n🔗 连接图:")
    for node, neighbors in graph.items():
        print(f"  线段 {node} 连接到: {neighbors}")
    
    # 查找连通分量
    print(f"\n🔍 连通分量:")
    visited = set()
    components = []
    
    for idx in range(len(test_lines)):
        if idx not in visited:
            component = merger._find_component(idx, graph, visited)
            components.append(component)
            print(f"  分量 {len(components)}: {component}")
    
    return graph, components

def debug_merging_process():
    """调试合并过程"""
    print("\n" + "=" * 60)
    print("🔄 调试合并过程")
    print("=" * 60)
    
    # 创建简单的连接线段
    test_lines = [
        [(0.0, 0.0), (100.0, 0.0)],
        [(100.0, 0.0), (200.0, 0.0)],
        [(200.0, 0.0), (300.0, 0.0)]
    ]
    
    print(f"原始线段:")
    for i, line in enumerate(test_lines):
        print(f"  线段 {i}: {line[0]} -> {line[1]}")
    
    # 创建合并器
    merger = SimpleLineMerger(
        distance_threshold=10,
        angle_threshold=5,
        enable_iterative=False
    )
    
    # 执行合并
    merged_lines = merger._merge_lines_simple(test_lines)
    
    print(f"\n合并结果:")
    for i, line in enumerate(merged_lines):
        print(f"  合并线段 {i}: {line[0]} -> {line[-1]}")
    
    print(f"\n统计:")
    print(f"  原始线段数: {len(test_lines)}")
    print(f"  合并后线段数: {len(merged_lines)}")
    print(f"  减少线段数: {len(test_lines) - len(merged_lines)}")
    
    return merged_lines

def main():
    """主测试函数"""
    print("🚀 开始详细调试线条合并算法")
    
    # 1. 测试连接检测
    can_connect = debug_connection_detection()
    
    # 2. 测试图构建
    graph, components = debug_graph_building()
    
    # 3. 测试合并过程
    merged_lines = debug_merging_process()
    
    print("\n" + "=" * 60)
    print("📊 调试总结")
    print("=" * 60)
    print(f"✅ 连接检测: {'通过' if can_connect else '失败'}")
    print(f"✅ 图构建: 发现 {len(components)} 个连通分量")
    print(f"✅ 合并过程: 生成 {len(merged_lines)} 条合并线段")
    
    if not can_connect:
        print("\n⚠️ 连接检测失败，可能的原因:")
        print("  1. 距离阈值设置过小")
        print("  2. 角度阈值设置过小")
        print("  3. 端点坐标精度问题")
        print("  4. 平行检测算法问题")

if __name__ == "__main__":
    main()
