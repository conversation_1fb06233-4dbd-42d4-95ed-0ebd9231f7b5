#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复处理器持久性问题
解决"⚠️ 处理器不存在，创建新的处理器"导致的图像显示问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_processor_persistence():
    """修复处理器持久性问题"""
    print("🔧 修复处理器持久性问题")
    print("="*60)
    
    file_path = 'main_enhanced_with_v2_fill.py'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复1：改进处理器恢复逻辑，避免数据丢失
        print("1. 修复处理器恢复逻辑...")
        
        old_restore_logic = '''    def _update_group_list_enhanced(self):
        """增强版组列表更新，支持隐藏组显示"""
        if not self.processor:
            print("⚠️ 处理器不存在，尝试恢复处理器状态")
            # 尝试从当前文件缓存恢复处理器状态
            if hasattr(self, 'current_file') and self.current_file:
                success = self._restore_processor_from_current_file()
                if success:
                    print("✅ 处理器状态恢复成功")
                else:
                    print("❌ 处理器状态恢复失败")
                    return
            else:
                print("❌ 没有当前文件，无法恢复处理器状态")
                return'''
        
        new_restore_logic = '''    def _update_group_list_enhanced(self):
        """增强版组列表更新，支持隐藏组显示"""
        if not self.processor:
            print("⚠️ 处理器不存在，尝试恢复处理器状态")
            # 尝试从当前文件缓存恢复处理器状态
            if hasattr(self, 'current_file') and self.current_file:
                success = self._restore_processor_from_current_file()
                if success:
                    print("✅ 处理器状态恢复成功")
                    # 🔧 修复：恢复后立即验证数据完整性
                    if not self._validate_processor_data_quick():
                        print("❌ 恢复的处理器数据不完整")
                        return
                else:
                    print("❌ 处理器状态恢复失败")
                    return
            else:
                print("❌ 没有当前文件，无法恢复处理器状态")
                return'''
        
        if old_restore_logic in content:
            content = content.replace(old_restore_logic, new_restore_logic)
            print("  ✅ 修复了 _update_group_list_enhanced 方法")
        
        # 修复2：添加快速数据验证方法
        print("2. 添加快速数据验证方法...")
        
        validation_method = '''
    def _validate_processor_data_quick(self):
        """快速验证处理器数据完整性"""
        try:
            if not self.processor:
                return False
            
            # 检查关键数据是否存在
            has_entities = hasattr(self.processor, 'current_file_entities') and self.processor.current_file_entities
            has_groups = hasattr(self.processor, 'all_groups') and self.processor.all_groups
            has_mapping = hasattr(self.processor, 'category_mapping') and self.processor.category_mapping
            
            if has_entities and has_groups and has_mapping:
                print(f"  📊 数据验证通过: 实体={len(self.processor.current_file_entities)}, 组={len(self.processor.all_groups)}")
                return True
            else:
                print(f"  ⚠️ 数据不完整: 实体={has_entities}, 组={has_groups}, 映射={has_mapping}")
                return False
                
        except Exception as e:
            print(f"  ❌ 数据验证失败: {e}")
            return False

'''
        
        # 在 _validate_processor_data 方法前插入
        insertion_point = "    def _validate_processor_data(self):"
        if insertion_point in content:
            content = content.replace(insertion_point, validation_method + insertion_point)
            print("  ✅ 添加了快速数据验证方法")
        
        # 修复3：改进处理器创建逻辑，确保数据持久性
        print("3. 改进处理器创建逻辑...")
        
        old_creation_logic = '''            # 确保处理器存在（关键修复）
            if not self.processor:
                print("  ⚠️ 处理器不存在，创建新的处理器")
                from main_enhanced import EnhancedCADProcessor
                self.processor = EnhancedCADProcessor(self.visualizer, self.canvas)
                # 设置回调
                if hasattr(self, 'on_status_update') and hasattr(self, 'on_progress_update'):
                    self.processor.set_callbacks(self.on_status_update, self.on_progress_update)'''
        
        new_creation_logic = '''            # 确保处理器存在（关键修复）
            if not self.processor:
                print("  ⚠️ 处理器不存在，创建新的处理器")
                from main_enhanced import EnhancedCADProcessor
                self.processor = EnhancedCADProcessor(self.visualizer, self.canvas)
                # 设置回调
                if hasattr(self, 'on_status_update') and hasattr(self, 'on_progress_update'):
                    self.processor.set_callbacks(self.on_status_update, self.on_progress_update)
                
                # 🔧 修复：确保新处理器有正确的初始化
                if not hasattr(self.processor, 'category_mapping') or not self.processor.category_mapping:
                    self.processor.category_mapping = {
                        'wall': '墙体',
                        'door_window': '门窗', 
                        'other': '其他'
                    }
                    print("  ✅ 初始化处理器类别映射")'''
        
        if old_creation_logic in content:
            content = content.replace(old_creation_logic, new_creation_logic)
            print("  ✅ 改进了处理器创建逻辑")
        
        # 修复4：添加处理器状态监控
        print("4. 添加处理器状态监控...")
        
        monitoring_method = '''
    def _monitor_processor_state(self, operation_name):
        """监控处理器状态"""
        try:
            if not self.processor:
                print(f"  ⚠️ {operation_name}: 处理器不存在")
                return False
            
            # 检查关键数据
            entity_count = len(getattr(self.processor, 'current_file_entities', []))
            group_count = len(getattr(self.processor, 'all_groups', []))
            
            if entity_count == 0 and group_count == 0:
                print(f"  ⚠️ {operation_name}: 处理器数据为空")
                return False
            
            print(f"  ✅ {operation_name}: 处理器状态正常 (实体={entity_count}, 组={group_count})")
            return True
            
        except Exception as e:
            print(f"  ❌ {operation_name}: 处理器状态监控失败: {e}")
            return False

'''
        
        # 在快速验证方法后插入
        if validation_method in content:
            content = content.replace(validation_method, validation_method + monitoring_method)
            print("  ✅ 添加了处理器状态监控方法")
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 处理器持久性修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_image_display_issues():
    """修复图像显示问题"""
    print(f"\n🔧 修复图像显示问题")
    print("="*60)
    
    file_path = 'main_enhanced_with_v2_fill.py'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复1：改进 _show_group 方法，确保数据传递
        print("1. 改进 _show_group 方法...")
        
        old_show_group = '''    def _show_group(self, group, group_index=None):
        """显示指定组（修复缺失方法，支持后台处理检查）"""
        try:
            # 检查是否在后台处理中，如果是则跳过界面更新
            if hasattr(self.processor, '_is_background_processing') and self.processor._is_background_processing:
                print(f"  跳过后台处理的界面更新: 组{group_index if group_index else '未知'}")
                return

            # 检查当前是否在处理显示文件
            is_display_file = self._is_processing_display_file()
            if hasattr(self, 'current_file') and hasattr(self, 'display_file'):
                print(f"  - 当前文件: {os.path.basename(self.current_file) if self.current_file else '无'}")
                print(f"  - 显示文件: {os.path.basename(self.display_file) if self.display_file else '无'}")
                print(f"  - 处理器文件: {os.path.basename(self.processor.current_file)}")

            if not is_display_file:
                print(f"  跳过后台文件的界面更新: 组{group_index if group_index else '未知'}")
                return'''
        
        new_show_group = '''    def _show_group(self, group, group_index=None):
        """显示指定组（修复缺失方法，支持后台处理检查）"""
        try:
            # 🔧 修复：首先监控处理器状态
            if not self._monitor_processor_state(f"显示组{group_index}"):
                print(f"  ❌ 处理器状态异常，无法显示组{group_index}")
                return
            
            # 检查是否在后台处理中，如果是则跳过界面更新
            if hasattr(self.processor, '_is_background_processing') and self.processor._is_background_processing:
                print(f"  跳过后台处理的界面更新: 组{group_index if group_index else '未知'}")
                return

            # 检查当前是否在处理显示文件
            is_display_file = self._is_processing_display_file()
            if hasattr(self, 'current_file') and hasattr(self, 'display_file'):
                print(f"  - 当前文件: {os.path.basename(self.current_file) if self.current_file else '无'}")
                print(f"  - 显示文件: {os.path.basename(self.display_file) if self.display_file else '无'}")
                print(f"  - 处理器文件: {os.path.basename(self.processor.current_file)}")

            if not is_display_file:
                print(f"  跳过后台文件的界面更新: 组{group_index if group_index else '未知'}")
                return'''
        
        if old_show_group in content:
            content = content.replace(old_show_group, new_show_group)
            print("  ✅ 改进了 _show_group 方法")
        
        # 修复2：改进组列表更新，确保数据完整性
        print("2. 改进组列表更新...")
        
        old_update_logic = '''        try:
            # 检查是否有处理器和显示文件
            if not self.processor:
                print("⚠️ 没有处理器，跳过组列表更新")
                return'''
        
        new_update_logic = '''        try:
            # 🔧 修复：检查处理器状态并尝试恢复
            if not self.processor:
                print("⚠️ 没有处理器，尝试恢复...")
                if hasattr(self, 'current_file') and self.current_file:
                    success = self._restore_processor_from_current_file()
                    if not success:
                        print("❌ 处理器恢复失败，跳过组列表更新")
                        return
                else:
                    print("❌ 没有当前文件，跳过组列表更新")
                    return
            
            # 验证处理器数据完整性
            if not self._validate_processor_data_quick():
                print("❌ 处理器数据不完整，跳过组列表更新")
                return'''
        
        if old_update_logic in content:
            content = content.replace(old_update_logic, new_update_logic)
            print("  ✅ 改进了组列表更新逻辑")
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 图像显示问题修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fixes():
    """测试修复效果"""
    print(f"\n🧪 测试修复效果")
    print("="*60)
    
    try:
        # 模拟处理器状态问题
        class MockUI:
            def __init__(self):
                self.processor = None
                self.current_file = "test.dxf"
                self.file_cache = {
                    "test.dxf": {
                        'entities': [{'type': 'LINE', 'layer': 'A-WALL'}],
                        'all_groups': [[{'type': 'LINE', 'layer': 'A-WALL'}]],
                        'auto_labeled_entities': [],
                        'labeled_entities': []
                    }
                }
            
            def _validate_processor_data_quick(self):
                """快速验证处理器数据完整性"""
                try:
                    if not self.processor:
                        return False
                    
                    # 检查关键数据是否存在
                    has_entities = hasattr(self.processor, 'current_file_entities') and self.processor.current_file_entities
                    has_groups = hasattr(self.processor, 'all_groups') and self.processor.all_groups
                    has_mapping = hasattr(self.processor, 'category_mapping') and self.processor.category_mapping
                    
                    if has_entities and has_groups and has_mapping:
                        print(f"  📊 数据验证通过: 实体={len(self.processor.current_file_entities)}, 组={len(self.processor.all_groups)}")
                        return True
                    else:
                        print(f"  ⚠️ 数据不完整: 实体={has_entities}, 组={has_groups}, 映射={has_mapping}")
                        return False
                        
                except Exception as e:
                    print(f"  ❌ 数据验证失败: {e}")
                    return False
            
            def _monitor_processor_state(self, operation_name):
                """监控处理器状态"""
                try:
                    if not self.processor:
                        print(f"  ⚠️ {operation_name}: 处理器不存在")
                        return False
                    
                    # 检查关键数据
                    entity_count = len(getattr(self.processor, 'current_file_entities', []))
                    group_count = len(getattr(self.processor, 'all_groups', []))
                    
                    if entity_count == 0 and group_count == 0:
                        print(f"  ⚠️ {operation_name}: 处理器数据为空")
                        return False
                    
                    print(f"  ✅ {operation_name}: 处理器状态正常 (实体={entity_count}, 组={group_count})")
                    return True
                    
                except Exception as e:
                    print(f"  ❌ {operation_name}: 处理器状态监控失败: {e}")
                    return False
        
        # 测试场景
        print("1. 测试处理器为空的情况:")
        ui = MockUI()
        result = ui._validate_processor_data_quick()
        print(f"   验证结果: {'通过' if result else '失败'}")
        
        print("\n2. 测试处理器状态监控:")
        result = ui._monitor_processor_state("测试操作")
        print(f"   监控结果: {'正常' if result else '异常'}")
        
        print("\n✅ 修复测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主修复函数"""
    print("🚀 开始修复处理器持久性和图像显示问题")
    
    try:
        # 1. 修复处理器持久性问题
        fix1_success = fix_processor_persistence()
        
        # 2. 修复图像显示问题
        fix2_success = fix_image_display_issues()
        
        # 3. 测试修复效果
        test_success = test_fixes()
        
        print(f"\n" + "="*60)
        print(f"📊 修复结果总结:")
        print(f"  处理器持久性修复: {'✅ 成功' if fix1_success else '❌ 失败'}")
        print(f"  图像显示问题修复: {'✅ 成功' if fix2_success else '❌ 失败'}")
        print(f"  修复效果测试: {'✅ 成功' if test_success else '❌ 失败'}")
        
        if all([fix1_success, fix2_success, test_success]):
            print(f"\n🎉 所有修复完成！")
            print(f"💡 修复内容:")
            print(f"   - 改进了处理器恢复逻辑，避免数据丢失")
            print(f"   - 添加了数据完整性验证")
            print(f"   - 增强了处理器状态监控")
            print(f"   - 修复了图像显示数据传递问题")
            print(f"\n🔧 现在图像预览应该能正常显示了！")
        else:
            print(f"\n⚠️ 部分修复失败，请检查错误信息")
        
    except Exception as e:
        print(f"❌ 修复过程失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
