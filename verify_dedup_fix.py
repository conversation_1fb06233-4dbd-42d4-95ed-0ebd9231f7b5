#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证去重修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_real_scenario():
    """测试真实场景"""
    print("🧪 测试真实场景的去重修复...")
    
    try:
        from cad_data_processor import CADDataProcessor
        processor = CADDataProcessor()
        
        # 模拟真实的26个墙体实体（基于调试输出）
        wall_entities = []
        
        # 创建一些不同的墙体线段
        coords = [
            (342727.5, 25051.0, 342727.5, 15031.0),  # 垂直线1
            (342727.5, 15031.0, 339439.5, 15031.0),  # 水平线1
            (339439.5, 15031.0, 339439.5, 25051.0),  # 垂直线2
            (339439.5, 25051.0, 342727.5, 25051.0),  # 水平线2
            (341000.0, 20000.0, 341000.0, 22000.0),  # 内部垂直线
            (340000.0, 21000.0, 342000.0, 21000.0),  # 内部水平线
        ]
        
        # 为每个坐标创建多个实体（模拟重复）
        for i, (sx, sy, ex, ey) in enumerate(coords):
            # 原始实体
            wall_entities.append({
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': sx,
                'start_y': sy,
                'end_x': ex,
                'end_y': ey,
                'color': 256,
                'linetype': 'Continuous'
            })
            
            # 添加一些重复实体
            for j in range(3):  # 每个坐标重复3次
                wall_entities.append({
                    'type': 'LINE',
                    'layer': 'A-WALL',
                    'start_x': sx,
                    'start_y': sy,
                    'end_x': ex,
                    'end_y': ey,
                    'color': 256,
                    'linetype': 'Continuous',
                    'merged': True
                })
            
            # 添加方向相反的重复实体
            wall_entities.append({
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': ex,  # 起点和终点交换
                'start_y': ey,
                'end_x': sx,
                'end_y': sy,
                'color': 256,
                'linetype': 'Continuous',
                'merged': True
            })
        
        print(f"📋 创建测试数据: {len(wall_entities)} 个墙体实体")
        print(f"  期望去重后: {len(coords)} 个不同的实体")
        
        # 执行去重
        print(f"\n🔧 执行去重...")
        unique_entities = processor._remove_duplicate_entities(wall_entities)
        
        print(f"\n📊 去重结果:")
        print(f"  输入: {len(wall_entities)} 个实体")
        print(f"  输出: {len(unique_entities)} 个实体")
        print(f"  期望: {len(coords)} 个实体")
        
        # 验证结果
        success = len(unique_entities) == len(coords)
        
        if success:
            print(f"  ✅ 去重修复成功！")
            print(f"  🎯 正确识别了 {len(coords)} 个不同的线段")
        else:
            print(f"  ❌ 去重结果不符合预期")
            print(f"    期望: {len(coords)} 个实体")
            print(f"    实际: {len(unique_entities)} 个实体")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_format_consistency():
    """测试格式一致性"""
    print("\n🔧 测试不同格式的一致性...")
    
    try:
        from cad_data_processor import CADDataProcessor
        processor = CADDataProcessor()
        
        # 相同线段的不同格式
        entities = [
            # start_x/start_y/end_x/end_y格式
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 0.0,
                'start_y': 0.0,
                'end_x': 100.0,
                'end_y': 0.0
            },
            # points格式
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'points': [(0.0, 0.0), (100.0, 0.0)]
            },
            # start/end格式
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start': (0.0, 0.0),
                'end': (100.0, 0.0)
            },
            # 方向相反
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 100.0,
                'start_y': 0.0,
                'end_x': 0.0,
                'end_y': 0.0
            }
        ]
        
        print(f"📋 测试相同线段的不同格式: {len(entities)} 个")
        
        # 生成哈希
        hashes = []
        for i, entity in enumerate(entities):
            hash_val = processor._get_entity_hash(entity)
            hashes.append(hash_val)
            print(f"  格式{i+1}: {hash_val}")
        
        # 检查一致性
        unique_hashes = set(hashes)
        print(f"\n📊 一致性检查:")
        print(f"  唯一哈希数: {len(unique_hashes)}")
        
        if len(unique_hashes) == 1:
            print(f"  ✅ 所有格式产生相同哈希")
        else:
            print(f"  ❌ 格式不一致")
            return False
        
        # 测试去重
        unique_entities = processor._remove_duplicate_entities(entities)
        print(f"\n🔧 去重测试:")
        print(f"  输入: {len(entities)} 个实体")
        print(f"  输出: {len(unique_entities)} 个实体")
        
        success = len(unique_entities) == 1
        if success:
            print(f"  ✅ 格式一致性测试通过")
        else:
            print(f"  ❌ 格式一致性测试失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CAD分类标注工具 - 去重修复验证")
    print("=" * 60)
    
    tests = [
        ("真实场景测试", test_real_scenario),
        ("格式一致性测试", test_format_consistency)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("验证结果总结:")
    print("=" * 60)
    
    all_passed = True
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not success:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 所有测试通过！")
        print(f"✅ 去重修复成功")
        print(f"✅ 现在可以正确处理不同格式的实体")
        print(f"✅ 过度去重问题已解决")
    else:
        print(f"\n⚠️ 部分测试失败")
        print(f"需要进一步调查")
    
    print("=" * 60)
