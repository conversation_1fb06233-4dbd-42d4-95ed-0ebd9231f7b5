#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试线条处理后的可视化显示
验证修复后的视图更新是否正确工作
"""

import os
import sys

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_line_processing_visualization():
    """测试线条处理后的可视化显示"""
    print("🧪 测试线条处理可视化修复...")
    
    try:
        # 导入必要的模块
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        from cad_data_processor import CADDataProcessor
        from cad_visualizer import CADVisualizer
        
        print("✅ 模块导入成功")
        
        # 创建测试用的处理器和可视化器
        processor = CADDataProcessor()
        visualizer = CADVisualizer()
        
        print("✅ 处理器和可视化器创建成功")
        
        # 创建一些测试实体
        test_entities = [
            {
                'type': 'LINE',
                'layer': 'WALL',
                'points': [[0, 0], [100, 0]],
                'start': [0, 0],
                'end': [100, 0]
            },
            {
                'type': 'LINE', 
                'layer': 'WALL',
                'points': [[100, 0], [100, 100]],
                'start': [100, 0],
                'end': [100, 100]
            },
            {
                'type': 'LINE',
                'layer': 'DOOR',
                'points': [[50, 0], [50, 20]],
                'start': [50, 0],
                'end': [50, 20]
            }
        ]
        
        print(f"✅ 创建了 {len(test_entities)} 个测试实体")
        
        # 测试基础数据处理
        processor.raw_entities = test_entities
        print("✅ 设置原始实体数据")
        
        # 测试线条去重处理
        deduped_entities = processor.process_line_deduplication(test_entities)
        print(f"✅ 去重处理完成: {len(test_entities)} -> {len(deduped_entities)} 个实体")
        
        # 测试线条合并处理
        merged_entities = processor.merge_lines(deduped_entities)
        processor.merged_entities = merged_entities
        print(f"✅ 合并处理完成: {len(deduped_entities)} -> {len(merged_entities)} 个实体")
        
        # 测试可视化方法
        print("\n🎨 测试可视化方法...")
        
        # 测试 draw_entities 方法
        try:
            visualizer.draw_entities(merged_entities)
            print("✅ draw_entities 方法测试成功")
        except Exception as e:
            print(f"❌ draw_entities 方法测试失败: {e}")
        
        # 测试 visualize_overview 方法
        try:
            visualizer.visualize_overview(
                merged_entities,
                None,
                [],
                processor=processor
            )
            print("✅ visualize_overview 方法测试成功")
        except Exception as e:
            print(f"❌ visualize_overview 方法测试失败: {e}")
        
        print("\n✅ 所有测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_visualization_update_methods():
    """测试可视化更新方法"""
    print("\n🧪 测试可视化更新方法...")
    
    try:
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        # 创建测试处理器
        from cad_data_processor import CADDataProcessor
        app.processor = CADDataProcessor()
        
        # 设置测试数据
        test_entities = [
            {
                'type': 'LINE',
                'layer': 'WALL',
                'points': [[0, 0], [100, 0]],
                'start': [0, 0],
                'end': [100, 0]
            }
        ]
        
        app.processor.raw_entities = test_entities
        app.processor.merged_entities = test_entities
        
        # 测试基础数据可视化更新
        try:
            app._update_visualization_basic_v2()
            print("✅ _update_visualization_basic_v2 方法测试成功")
        except Exception as e:
            print(f"❌ _update_visualization_basic_v2 方法测试失败: {e}")
        
        # 测试线条处理可视化更新
        try:
            app._update_visualization_line_v2()
            print("✅ _update_visualization_line_v2 方法测试成功")
        except Exception as e:
            print(f"❌ _update_visualization_line_v2 方法测试失败: {e}")
        
        # 清理
        root.destroy()
        
        print("✅ 可视化更新方法测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 可视化更新方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始线条处理可视化测试...")
    
    # 运行基础测试
    success1 = test_line_processing_visualization()
    
    # 运行可视化更新方法测试
    success2 = test_visualization_update_methods()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！线条处理可视化修复成功")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
