#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示功能测试启动器
提供多种测试选项的启动界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os

class TestLauncher:
    """测试启动器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("CAD显示功能测试启动器")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        self.create_interface()
        
    def create_interface(self):
        """创建界面"""
        # 标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill=tk.X, padx=20, pady=20)
        
        title_label = ttk.Label(title_frame, text="CAD显示功能测试套件", 
                               font=("Arial", 16, "bold"))
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, 
                                  text="测试实体详细视图、全图概览的显示功能\n包括文件处理和编组过程中的显示正确性和窗口适配性", 
                                  font=("Arial", 10), foreground="gray")
        subtitle_label.pack(pady=(10, 0))
        
        # 测试选项
        options_frame = ttk.LabelFrame(self.root, text="选择测试类型")
        options_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 基础显示功能测试
        basic_frame = ttk.Frame(options_frame)
        basic_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(basic_frame, text="1. 基础显示功能测试", 
                 font=("Arial", 12, "bold")).pack(anchor=tk.W)
        
        ttk.Label(basic_frame, 
                 text="• 测试实体详细视图显示\n• 测试全图概览显示\n• 测试缩放和平移功能\n• 测试窗口适配性", 
                 font=("Arial", 9)).pack(anchor=tk.W, padx=20)
        
        ttk.Button(basic_frame, text="启动基础显示测试", 
                  command=self.launch_basic_test).pack(anchor=tk.W, padx=20, pady=5)
        
        # 集成测试
        integration_frame = ttk.Frame(options_frame)
        integration_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(integration_frame, text="2. CAD处理集成测试", 
                 font=("Arial", 12, "bold")).pack(anchor=tk.W)
        
        ttk.Label(integration_frame, 
                 text="• 测试文件加载过程显示\n• 测试线条处理过程显示\n• 测试分组过程显示\n• 测试实时更新显示", 
                 font=("Arial", 9)).pack(anchor=tk.W, padx=20)
        
        ttk.Button(integration_frame, text="启动集成测试", 
                  command=self.launch_integration_test).pack(anchor=tk.W, padx=20, pady=5)
        
        # 性能测试
        performance_frame = ttk.Frame(options_frame)
        performance_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(performance_frame, text="3. 性能和稳定性测试", 
                 font=("Arial", 12, "bold")).pack(anchor=tk.W)
        
        ttk.Label(performance_frame, 
                 text="• 测试大文件显示性能\n• 测试内存使用情况\n• 测试错误处理能力\n• 测试多窗口显示", 
                 font=("Arial", 9)).pack(anchor=tk.W, padx=20)
        
        ttk.Button(performance_frame, text="启动性能测试", 
                  command=self.launch_performance_test).pack(anchor=tk.W, padx=20, pady=5)
        
        # 底部按钮
        bottom_frame = ttk.Frame(self.root)
        bottom_frame.pack(fill=tk.X, padx=20, pady=20)
        
        ttk.Button(bottom_frame, text="运行所有测试", 
                  command=self.launch_all_tests,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(bottom_frame, text="查看测试说明", 
                  command=self.show_help).pack(side=tk.LEFT, padx=10)
        
        ttk.Button(bottom_frame, text="退出", 
                  command=self.root.quit).pack(side=tk.RIGHT)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
    def launch_basic_test(self):
        """启动基础显示测试"""
        self.status_var.set("启动基础显示功能测试...")
        try:
            subprocess.Popen([sys.executable, "test_display_functionality.py"])
            self.status_var.set("基础显示测试已启动")
        except Exception as e:
            messagebox.showerror("错误", f"启动测试失败: {e}")
            self.status_var.set("启动失败")
    
    def launch_integration_test(self):
        """启动集成测试"""
        self.status_var.set("启动CAD处理集成测试...")
        try:
            subprocess.Popen([sys.executable, "test_cad_display_integration.py"])
            self.status_var.set("集成测试已启动")
        except Exception as e:
            messagebox.showerror("错误", f"启动测试失败: {e}")
            self.status_var.set("启动失败")
    
    def launch_performance_test(self):
        """启动性能测试"""
        self.status_var.set("启动性能测试...")
        try:
            # 启动集成测试并运行综合测试
            subprocess.Popen([sys.executable, "test_cad_display_integration.py"])
            self.status_var.set("性能测试已启动（请在集成测试界面中点击'运行综合测试'）")
            messagebox.showinfo("提示", "性能测试已启动\n请在打开的集成测试界面中点击'运行综合测试'按钮")
        except Exception as e:
            messagebox.showerror("错误", f"启动测试失败: {e}")
            self.status_var.set("启动失败")
    
    def launch_all_tests(self):
        """启动所有测试"""
        self.status_var.set("启动所有测试...")
        try:
            # 启动基础测试
            subprocess.Popen([sys.executable, "test_display_functionality.py"])
            
            # 稍等一下再启动集成测试
            self.root.after(2000, lambda: subprocess.Popen([sys.executable, "test_cad_display_integration.py"]))
            
            self.status_var.set("所有测试已启动")
            messagebox.showinfo("提示", "所有测试已启动\n\n基础显示测试和集成测试窗口将依次打开\n请按需要运行相应的测试功能")
        except Exception as e:
            messagebox.showerror("错误", f"启动测试失败: {e}")
            self.status_var.set("启动失败")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
CAD显示功能测试说明

1. 基础显示功能测试 (test_display_functionality.py)
   - 测试实体详细视图的正确显示
   - 测试全图概览的显示效果
   - 测试缩放、平移等交互功能
   - 测试不同窗口大小的适配性
   - 使用模拟数据进行测试

2. CAD处理集成测试 (test_cad_display_integration.py)
   - 测试真实DXF文件的加载和显示
   - 测试文件处理过程中的显示更新
   - 测试分组过程中的显示变化
   - 测试实时显示更新功能
   - 测试多窗口显示能力

3. 性能和稳定性测试
   - 测试大文件的显示性能
   - 测试内存使用情况
   - 测试错误处理能力
   - 测试长时间运行的稳定性

使用建议：
1. 首先运行基础显示测试，验证基本功能
2. 然后运行集成测试，使用真实DXF文件
3. 最后运行性能测试，评估系统性能

注意事项：
- 确保已安装所需的依赖库（matplotlib, numpy等）
- 准备一些DXF文件用于集成测试
- 测试过程中注意观察显示效果和性能表现
"""
        
        help_window = tk.Toplevel(self.root)
        help_window.title("测试说明")
        help_window.geometry("600x500")
        
        text_widget = tk.Text(help_window, wrap=tk.WORD, padx=10, pady=10)
        scrollbar = ttk.Scrollbar(help_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def run(self):
        """运行启动器"""
        self.root.mainloop()

def check_dependencies():
    """检查依赖"""
    required_modules = ['matplotlib', 'numpy', 'tkinter']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"缺少依赖模块: {', '.join(missing_modules)}")
        print("请安装缺少的模块后再运行测试")
        return False
    
    return True

def main():
    """主函数"""
    print("CAD显示功能测试启动器")
    print("="*40)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    # 检查测试文件是否存在
    test_files = ["test_display_functionality.py", "test_cad_display_integration.py"]
    missing_files = [f for f in test_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"缺少测试文件: {', '.join(missing_files)}")
        print("请确保所有测试文件都在当前目录中")
        input("按回车键退出...")
        return
    
    try:
        app = TestLauncher()
        app.run()
    except Exception as e:
        print(f"启动器运行失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
