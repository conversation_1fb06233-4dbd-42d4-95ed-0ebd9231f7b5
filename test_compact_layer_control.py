#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试紧凑图层控制布局
验证所有按钮水平并列布置，压缩行高，无需滚动显示所有内容
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

def test_compact_layer_control():
    """测试紧凑图层控制布局"""
    print("🧪 测试紧凑图层控制布局...")
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("紧凑图层控制测试")
        root.geometry("900x400")  # 设置合适的窗口大小
        
        # 创建测试用的图层控制区域
        test_frame = tk.Frame(root, relief='ridge', bd=2, bg='#E6F3FF')
        test_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 创建测试用的图层控制实例
        layer_control = CompactLayerControlTest(test_frame)
        
        print("✅ 紧凑图层控制测试窗口已创建")
        print("📋 测试要点:")
        print("  1. 所有按钮是否水平并列布置")
        print("  2. 行高是否已压缩")
        print("  3. 是否能在不滚动的情况下显示所有图层")
        print("  4. 按钮文字是否清晰可读")
        print("  5. 参数输入框是否正常工作")
        
        # 启动测试窗口
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

class CompactLayerControlTest:
    """紧凑图层控制测试类"""
    
    def __init__(self, parent):
        self.parent = parent
        self.layer_states = {
            'cad_lines': tk.BooleanVar(value=True),
            'wall_fill': tk.BooleanVar(value=True),
            'furniture_fill': tk.BooleanVar(value=False),
            'room_fill': tk.BooleanVar(value=True)
        }
        self.layer_order = ['cad_lines', 'wall_fill', 'furniture_fill', 'room_fill']
        self.layer_items_data = [
            ('cad_lines', 'CAD线条', '#2196F3', '显示/隐藏CAD原始线条'),
            ('wall_fill', '墙体填充', '#4CAF50', '显示/隐藏墙体填充效果'),
            ('furniture_fill', '家具填充', '#FF9800', '显示/隐藏家具填充效果'),
            ('room_fill', '房间填充', '#9C27B0', '显示/隐藏房间填充效果')
        ]
        self.shadow_controls = {}
        self._create_test_compact_layout()
    
    def _create_test_compact_layout(self):
        """创建测试用的紧凑布局"""
        try:
            # 标题和应用按钮水平并列
            header_frame = tk.Frame(self.parent)
            header_frame.pack(fill='x', pady=(2, 5), padx=5)
            
            # 左侧标题
            layer_title = tk.Label(header_frame, text="图层控制（紧凑布局）",
                                 font=('Arial', 9, 'bold'), bg='#E6F3FF')
            layer_title.pack(side='left')
            
            # 右侧应用按钮
            apply_btn = tk.Button(header_frame, text="应用图层设置",
                                command=self._test_apply_settings,
                                bg='#FF5722', fg='white', font=('Arial', 8, 'bold'))
            apply_btn.pack(side='right')

            # 直接创建图层列表容器（不使用滚动）
            self.layer_list_frame = tk.Frame(self.parent)
            self.layer_list_frame.pack(fill='both', expand=True, padx=3, pady=2)
            
            # 创建图层控制项
            self._create_test_layer_items()
            
            print("✅ 测试紧凑布局创建完成")

        except Exception as e:
            print(f"❌ 创建测试布局失败: {e}")
    
    def _create_test_layer_items(self):
        """创建测试图层项"""
        try:
            # 清空现有项目
            for widget in self.layer_list_frame.winfo_children():
                widget.destroy()
            
            # 按当前顺序创建图层项
            for i, layer_key in enumerate(self.layer_order):
                # 找到对应的图层数据
                layer_data = None
                for data in self.layer_items_data:
                    if data[0] == layer_key:
                        layer_data = data
                        break
                
                if layer_data:
                    self._create_test_single_layer_item(i, layer_data)
                    
        except Exception as e:
            print(f"❌ 创建测试图层项失败: {e}")

    def _create_test_single_layer_item(self, index, layer_data):
        """创建单个测试图层项（紧凑水平布局）"""
        try:
            layer_key, layer_name, color, tooltip = layer_data
            
            # 图层项容器（方框）- 减少内边距
            item_frame = tk.Frame(self.layer_list_frame, relief='ridge', bd=1, bg='#F5F5F5')
            item_frame.pack(fill='x', pady=1)  # 减少垂直间距
            
            # 单行水平布局 - 所有控件在一行
            main_row = tk.Frame(item_frame)
            main_row.pack(fill='x', padx=3, pady=2)  # 减少内边距

            # 1. 圆形显示/隐藏按钮
            self._create_test_circular_toggle_button(main_row, layer_key)

            # 2. 颜色指示器
            color_label = tk.Label(main_row, text="●", fg=color, font=('Arial', 8, 'bold'))
            color_label.pack(side='left', padx=(3, 2))
            
            # 3. 图层名称 - 固定宽度
            name_label = tk.Label(main_row, text=layer_name, font=('Arial', 7), 
                                anchor='w', width=8)
            name_label.pack(side='left', padx=(0, 3))

            # 4. 上下箭头按钮 - 水平排列
            up_btn = tk.Button(main_row, text="▲", font=('Arial', 6),
                             width=1, height=1,
                             command=lambda: self._test_move_layer_up(index),
                             state='normal' if index > 0 else 'disabled')
            up_btn.pack(side='left', padx=(0, 1))
            
            down_btn = tk.Button(main_row, text="▼", font=('Arial', 6),
                               width=1, height=1,
                               command=lambda: self._test_move_layer_down(index),
                               state='normal' if index < len(self.layer_order) - 1 else 'disabled')
            down_btn.pack(side='left', padx=(0, 3))

            # 5. 阴影功能按钮 - 水平排列
            self._create_test_compact_shadow_controls(main_row, layer_key)

            # 添加工具提示
            def show_tooltip(event):
                print(f"💡 {layer_name}: {tooltip}")

            item_frame.bind("<Enter>", show_tooltip)

        except Exception as e:
            print(f"❌ 创建测试图层项失败: {e}")
    
    def _create_test_circular_toggle_button(self, parent, layer_key):
        """创建测试圆形按钮"""
        try:
            # 创建画布用于绘制圆形按钮
            canvas = tk.Canvas(parent, width=16, height=16, highlightthickness=0)  # 稍微缩小
            canvas.pack(side='left', padx=(0, 3))
            
            # 根据当前状态确定颜色
            is_visible = self.layer_states[layer_key].get()
            fill_color = '#4CAF50' if is_visible else '#9E9E9E'  # 绿色或灰色
            
            # 绘制圆形
            circle = canvas.create_oval(1, 1, 15, 15, fill=fill_color, outline='#333333', width=1)
            
            # 点击事件处理
            def on_click(event):
                self._test_toggle_visibility(layer_key, canvas, circle)
            
            canvas.bind("<Button-1>", on_click)
            
        except Exception as e:
            print(f"❌ 创建测试圆形按钮失败: {e}")
    
    def _create_test_compact_shadow_controls(self, parent, layer_key):
        """创建测试紧凑阴影控制"""
        try:
            # 阴影功能按钮 - 紧凑版本
            add_btn = tk.Button(parent, text="阴影", font=('Arial', 6),
                              command=lambda: self._test_add_shadow(layer_key),
                              bg='#4CAF50', fg='white', width=4, height=1)
            add_btn.pack(side='left', padx=(0, 1))
            
            remove_btn = tk.Button(parent, text="删", font=('Arial', 6),
                                 command=lambda: self._test_remove_shadow(layer_key),
                                 bg='#F44336', fg='white', width=2, height=1)
            remove_btn.pack(side='left', padx=(0, 1))
            
            hide_btn = tk.Button(parent, text="隐", font=('Arial', 6),
                               command=lambda: self._test_hide_shadow(layer_key),
                               bg='#FF9800', fg='white', width=2, height=1)
            hide_btn.pack(side='left', padx=(0, 1))
            
            reidentify_btn = tk.Button(parent, text="识", font=('Arial', 6),
                                     command=lambda: self._test_reidentify_shadow(layer_key),
                                     bg='#2196F3', fg='white', width=2, height=1)
            reidentify_btn.pack(side='left', padx=(0, 3))
            
            # 阴影参数控制 - 紧凑版本
            # 方向
            tk.Label(parent, text="方:", font=('Arial', 6)).pack(side='left')
            direction_var = tk.StringVar(value="315")
            direction_entry = tk.Entry(parent, textvariable=direction_var, 
                                     width=3, font=('Arial', 6))
            direction_entry.pack(side='left', padx=(1, 2))
            direction_entry.bind('<Return>', lambda e: self._test_update_direction(layer_key, direction_var.get()))
            
            # 强度
            tk.Label(parent, text="强:", font=('Arial', 6)).pack(side='left')
            intensity_var = tk.StringVar(value="0.3")
            intensity_entry = tk.Entry(parent, textvariable=intensity_var, 
                                     width=3, font=('Arial', 6))
            intensity_entry.pack(side='left', padx=(1, 2))
            intensity_entry.bind('<Return>', lambda e: self._test_update_intensity(layer_key, intensity_var.get()))
            
            # 长度
            tk.Label(parent, text="长:", font=('Arial', 6)).pack(side='left')
            length_var = tk.StringVar(value="10")
            length_entry = tk.Entry(parent, textvariable=length_var, 
                                   width=3, font=('Arial', 6))
            length_entry.pack(side='left', padx=(1, 0))
            length_entry.bind('<Return>', lambda e: self._test_update_length(layer_key, length_var.get()))
            
            # 存储控件引用
            self.shadow_controls[layer_key] = {
                'add_btn': add_btn,
                'remove_btn': remove_btn,
                'hide_btn': hide_btn,
                'reidentify_btn': reidentify_btn,
                'direction_var': direction_var,
                'intensity_var': intensity_var,
                'length_var': length_var
            }
            
        except Exception as e:
            print(f"❌ 创建测试紧凑阴影控制失败: {e}")
    
    def _test_toggle_visibility(self, layer_key, canvas, circle):
        """测试切换可见性"""
        try:
            # 切换状态
            current_state = self.layer_states[layer_key].get()
            new_state = not current_state
            self.layer_states[layer_key].set(new_state)
            
            # 更新按钮颜色
            fill_color = '#4CAF50' if new_state else '#9E9E9E'  # 绿色或灰色
            canvas.itemconfig(circle, fill=fill_color)
            
            print(f"🔄 图层 {layer_key} 可见性: {'显示' if new_state else '隐藏'}")
            
        except Exception as e:
            print(f"❌ 测试切换可见性失败: {e}")
    
    def _test_move_layer_up(self, index):
        """测试向上移动图层"""
        if index > 0:
            self.layer_order[index], self.layer_order[index - 1] = \
                self.layer_order[index - 1], self.layer_order[index]
            self._create_test_layer_items()
            print(f"📈 图层已向上移动: {self.layer_order}")
    
    def _test_move_layer_down(self, index):
        """测试向下移动图层"""
        if index < len(self.layer_order) - 1:
            self.layer_order[index], self.layer_order[index + 1] = \
                self.layer_order[index + 1], self.layer_order[index]
            self._create_test_layer_items()
            print(f"📉 图层已向下移动: {self.layer_order}")
    
    def _test_add_shadow(self, layer_key):
        """测试添加阴影"""
        print(f"✅ 测试：为图层 {layer_key} 添加阴影")
    
    def _test_remove_shadow(self, layer_key):
        """测试删除阴影"""
        print(f"🗑️ 测试：删除图层 {layer_key} 的阴影")
    
    def _test_hide_shadow(self, layer_key):
        """测试隐藏阴影"""
        print(f"👁️ 测试：切换图层 {layer_key} 阴影可见性")
    
    def _test_reidentify_shadow(self, layer_key):
        """测试重新识别阴影"""
        print(f"🔄 测试：重新识别图层 {layer_key} 的阴影")
    
    def _test_update_direction(self, layer_key, direction_str):
        """测试更新阴影方向"""
        try:
            direction = float(direction_str)
            print(f"🧭 测试：图层 {layer_key} 阴影方向更新为 {direction}°")
        except ValueError:
            print(f"⚠️ 测试：无效的方向值 {direction_str}")
    
    def _test_update_intensity(self, layer_key, intensity_str):
        """测试更新阴影强度"""
        try:
            intensity = float(intensity_str)
            print(f"💪 测试：图层 {layer_key} 阴影强度更新为 {intensity}")
        except ValueError:
            print(f"⚠️ 测试：无效的强度值 {intensity_str}")
    
    def _test_update_length(self, layer_key, length_str):
        """测试更新阴影长度"""
        try:
            length = float(length_str)
            print(f"📏 测试：图层 {layer_key} 阴影长度更新为 {length}")
        except ValueError:
            print(f"⚠️ 测试：无效的长度值 {length_str}")
    
    def _test_apply_settings(self):
        """测试应用设置"""
        print("🔧 测试：应用图层设置")
        for layer_key, state_var in self.layer_states.items():
            is_visible = state_var.get()
            print(f"  - {layer_key}: {'显示' if is_visible else '隐藏'}")
        print(f"  - 图层顺序: {' → '.join(self.layer_order)}")

if __name__ == "__main__":
    print("🚀 开始紧凑图层控制布局测试\n")
    
    # 运行测试
    success = test_compact_layer_control()
    
    if success:
        print("\n🎉 紧凑图层控制布局测试完成")
        print("💡 布局特点:")
        print("  - 所有按钮水平并列布置")
        print("  - 压缩行高，节省空间")
        print("  - 无需滚动即可显示所有图层")
        print("  - 保持功能完整性")
    else:
        print("\n⚠️ 紧凑图层控制布局测试失败")
