#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复图层识别和分组问题
解决只识别到门窗图层，其他图层内容识别、分组及显示的问题
"""

import os
import sys

def fix_enhanced_cad_processor():
    """修复EnhancedCADProcessor中的问题"""
    print("🔧 修复EnhancedCADProcessor...")
    
    try:
        # 读取main_enhanced.py文件
        with open('main_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. 修复_update_groups_info方法
        if '_update_groups_info' not in content:
            print("  添加缺失的_update_groups_info方法...")
            
            update_groups_info_method = '''
    def _update_groups_info(self):
        """更新组信息"""
        try:
            self.groups_info = []
            
            if not self.all_groups:
                print("  ⚠️ 没有分组数据，无法更新组信息")
                return
            
            for i, group in enumerate(self.all_groups):
                group_info = {
                    'index': i,
                    'label': group.get('label', 'unlabeled'),
                    'entity_count': len(group.get('entities', [])),
                    'status': group.get('status', 'pending'),
                    'confidence': group.get('confidence', 0.0),
                    'group_type': group.get('group_type', 'unknown')
                }
                self.groups_info.append(group_info)
            
            print(f"  ✅ 组信息更新完成: {len(self.groups_info)}个组")
            
        except Exception as e:
            print(f"  ❌ 更新组信息失败: {e}")
            self.groups_info = []
'''
            
            # 在类的末尾添加方法
            class_end = content.rfind('class EnhancedCADProcessor')
            if class_end != -1:
                # 找到类的结束位置
                next_class = content.find('\nclass ', class_end + 1)
                if next_class == -1:
                    next_class = len(content)
                
                # 在类结束前插入方法
                content = content[:next_class] + update_groups_info_method + content[next_class:]
        
        # 2. 修复_process_other_entities方法，确保非特殊图层也能被处理
        if '_process_other_entities' in content:
            print("  修复_process_other_entities方法...")
            
            # 查找并替换_process_other_entities方法
            method_start = content.find('def _process_other_entities(self, entities, auto_groups):')
            if method_start != -1:
                # 找到方法结束位置
                method_end = content.find('\n    def ', method_start + 1)
                if method_end == -1:
                    method_end = content.find('\nclass ', method_start + 1)
                if method_end == -1:
                    method_end = len(content)
                
                # 替换方法内容
                new_method = '''def _process_other_entities(self, entities, auto_groups):
        """处理其他实体（非特殊图层）"""
        try:
            # 获取已处理的实体ID
            processed_entity_ids = set()
            for group in auto_groups:
                for entity in group.get('entities', []):
                    processed_entity_ids.add(id(entity))
            
            # 获取未处理的实体
            unprocessed_entities = [e for e in entities if id(e) not in processed_entity_ids]
            
            print(f"  未处理实体数量: {len(unprocessed_entities)}")
            
            if not unprocessed_entities:
                return []
            
            # 使用CAD数据处理器进行分组
            other_groups = self.processor.group_entities(unprocessed_entities, distance_threshold=20, debug=False)
            
            print(f"  其他实体分组结果: {len(other_groups)}个组")
            
            # 为每个组添加基本信息
            for i, group in enumerate(other_groups):
                if 'label' not in group:
                    group['label'] = 'other'
                if 'group_type' not in group:
                    group['group_type'] = 'general'
                if 'status' not in group:
                    group['status'] = 'pending'
                if 'confidence' not in group:
                    group['confidence'] = 0.5
            
            return other_groups
            
        except Exception as e:
            print(f"  ❌ 处理其他实体失败: {e}")
            return []

    '''
                content = content[:method_start] + new_method + content[method_end:]
        
        # 3. 修复_optimize_groups方法
        if '_optimize_groups' in content:
            print("  修复_optimize_groups方法...")
            
            method_start = content.find('def _optimize_groups(self, other_groups, auto_groups):')
            if method_start != -1:
                method_end = content.find('\n    def ', method_start + 1)
                if method_end == -1:
                    method_end = content.find('\nclass ', method_start + 1)
                if method_end == -1:
                    method_end = len(content)
                
                new_method = '''def _optimize_groups(self, other_groups, auto_groups):
        """优化分组结果"""
        try:
            print(f"  开始优化分组: {len(other_groups)}个其他组, {len(auto_groups)}个自动组")
            
            # 简单的优化：过滤掉太小的组
            optimized_groups = []
            
            for group in other_groups:
                entities = group.get('entities', [])
                if len(entities) >= 1:  # 至少包含1个实体
                    optimized_groups.append(group)
                else:
                    print(f"    过滤掉小组: {len(entities)}个实体")
            
            print(f"  优化完成: {len(optimized_groups)}个组")
            return optimized_groups
            
        except Exception as e:
            print(f"  ❌ 优化分组失败: {e}")
            return other_groups

    '''
                content = content[:method_start] + new_method + content[method_end:]
        
        # 保存修改后的文件
        with open('main_enhanced.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("  ✅ main_enhanced.py修复完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复main_enhanced.py失败: {e}")
        return False

def fix_cad_data_processor():
    """修复CADDataProcessor中的分组问题"""
    print("🔧 修复CADDataProcessor...")
    
    try:
        # 读取cad_data_processor.py文件
        with open('cad_data_processor.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复group_entities方法，确保即使没有特殊图层也能正常分组
        if 'def group_entities(self, entities, distance_threshold=20, debug=False):' in content:
            print("  修复group_entities方法...")
            
            # 查找方法
            method_start = content.find('def group_entities(self, entities, distance_threshold=20, debug=False):')
            if method_start != -1:
                # 找到方法结束位置
                method_end = content.find('\n    def ', method_start + 1)
                if method_end == -1:
                    method_end = content.find('\nclass ', method_start + 1)
                if method_end == -1:
                    method_end = len(content)
                
                # 在方法开始处添加通用分组逻辑
                method_content = content[method_start:method_end]
                
                # 如果方法中没有处理通用实体的逻辑，添加它
                if '# 步骤7: 处理所有未分组的实体（通用分组）' not in method_content:
                    print("    添加通用实体分组逻辑...")
                    
                    # 在return语句前添加通用分组逻辑
                    return_pos = method_content.rfind('return all_groups')
                    if return_pos != -1:
                        additional_logic = '''
        
        # 步骤7: 处理所有未分组的实体（通用分组）
        if len(all_groups) == 0 and len(entities) > 0:
            print("  没有特殊图层分组，执行通用分组...")
            
            # 按图层进行基本分组
            layer_groups = defaultdict(list)
            for entity in entities:
                layer = entity.get('layer', 'UNKNOWN')
                layer_groups[layer].append(entity)
            
            # 为每个图层创建一个组
            for layer, layer_entities in layer_groups.items():
                if len(layer_entities) > 0:
                    group = {
                        'entities': layer_entities,
                        'label': f'layer_{layer}',
                        'group_type': 'layer_based',
                        'layer': layer,
                        'status': 'pending',
                        'confidence': 0.3
                    }
                    all_groups.append(group)
            
            print(f"  通用分组完成: {len(all_groups)}个组")
        
        '''
                        new_method_content = method_content[:return_pos] + additional_logic + method_content[return_pos:]
                        content = content[:method_start] + new_method_content + content[method_end:]
        
        # 保存修改后的文件
        with open('cad_data_processor.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("  ✅ cad_data_processor.py修复完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复cad_data_processor.py失败: {e}")
        return False

def fix_main_enhanced_with_v2_fill():
    """修复主应用中的处理器创建问题"""
    print("🔧 修复主应用处理器创建...")
    
    try:
        # 读取main_enhanced_with_v2_fill.py文件
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 确保处理器创建时正确设置回调
        if '⚠️ 处理器不存在，创建新的处理器' in content:
            print("  修复处理器创建逻辑...")
            
            # 查找处理器创建的位置
            create_pos = content.find('⚠️ 处理器不存在，创建新的处理器')
            if create_pos != -1:
                # 查找前后的代码上下文
                context_start = content.rfind('if not self.processor:', create_pos - 500)
                context_end = content.find('self.processor.set_callbacks', create_pos + 200)
                
                if context_start != -1 and context_end != -1:
                    # 确保在创建处理器后立即设置回调
                    replacement = '''if not self.processor:
            print("⚠️ 处理器不存在，创建新的处理器")
            from main_enhanced import EnhancedCADProcessor
            self.processor = EnhancedCADProcessor(self.visualizer, self.canvas)
            
            # 立即设置回调以确保状态更新正常工作
            self.processor.set_callbacks(self.on_status_update, self.on_progress_update)
            print("✅ 处理器创建完成并设置回调")

        # 设置回调以接收状态更新（确保回调设置）
        if hasattr(self.processor, 'set_callbacks'):'''
                    
                    context_end_full = content.find('\n', context_end + 100)
                    if context_end_full != -1:
                        content = content[:context_start] + replacement + content[context_end_full:]
        
        # 保存修改后的文件
        with open('main_enhanced_with_v2_fill.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("  ✅ main_enhanced_with_v2_fill.py修复完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复main_enhanced_with_v2_fill.py失败: {e}")
        return False

def test_fixes():
    """测试修复效果"""
    print("🧪 测试修复效果...")
    
    try:
        # 重新导入模块
        import importlib
        
        # 清除模块缓存
        modules_to_reload = ['main_enhanced', 'cad_data_processor']
        for module_name in modules_to_reload:
            if module_name in sys.modules:
                importlib.reload(sys.modules[module_name])
        
        # 测试处理器创建
        from main_enhanced import EnhancedCADProcessor
        processor = EnhancedCADProcessor(None, None)
        
        # 检查方法是否存在
        methods_to_check = ['_update_groups_info', '_process_other_entities', '_optimize_groups']
        for method_name in methods_to_check:
            if hasattr(processor, method_name):
                print(f"  ✅ {method_name}方法存在")
            else:
                print(f"  ❌ {method_name}方法缺失")
        
        # 测试CAD数据处理器
        from cad_data_processor import CADDataProcessor
        cad_processor = CADDataProcessor()
        
        if hasattr(cad_processor, 'group_entities'):
            print(f"  ✅ group_entities方法存在")
        else:
            print(f"  ❌ group_entities方法缺失")
        
        print("  ✅ 修复测试完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主修复函数"""
    print("🚀 开始修复图层识别和分组问题")
    print("="*60)
    
    fixes = [
        ("修复EnhancedCADProcessor", fix_enhanced_cad_processor),
        ("修复CADDataProcessor", fix_cad_data_processor),
        ("修复主应用", fix_main_enhanced_with_v2_fill),
        ("测试修复效果", test_fixes)
    ]
    
    success_count = 0
    
    for fix_name, fix_func in fixes:
        print(f"\n{fix_name}...")
        try:
            if fix_func():
                success_count += 1
                print(f"✅ {fix_name}成功")
            else:
                print(f"❌ {fix_name}失败")
        except Exception as e:
            print(f"❌ {fix_name}异常: {e}")
    
    print(f"\n" + "="*60)
    print("📋 修复总结:")
    print("="*60)
    print(f"成功修复: {success_count}/{len(fixes)}")
    
    if success_count == len(fixes):
        print("🎉 所有修复完成！")
        print("\n💡 修复内容:")
        print("  1. 添加了缺失的_update_groups_info方法")
        print("  2. 修复了_process_other_entities方法")
        print("  3. 修复了_optimize_groups方法")
        print("  4. 添加了通用实体分组逻辑")
        print("  5. 修复了处理器创建和回调设置")
        
        print("\n🔄 建议重启应用以使修复生效")
    else:
        print("⚠️ 部分修复失败，请检查错误信息")
    
    return success_count == len(fixes)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
