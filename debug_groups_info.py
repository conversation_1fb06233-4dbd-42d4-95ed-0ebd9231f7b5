#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试组信息更新问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_enhanced import EnhancedCADProcessor

def debug_groups_info_update():
    """调试组信息更新问题"""
    print("🔍 调试组信息更新问题")
    print("="*60)
    
    # 创建处理器
    processor = EnhancedCADProcessor()
    
    # 创建测试数据
    test_entities = [
        # 墙体实体
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': '墙体', 'auto_labeled': True},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'label': '墙体', 'auto_labeled': True},
        # 门窗实体
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(50, -10), (50, 10)], 'label': '门窗', 'auto_labeled': True},
        {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(150, 50), (200, 50)], 'label': '门窗', 'auto_labeled': True},
    ]
    
    # 创建测试组
    test_groups = [
        # 墙体组（列表格式）
        [test_entities[0], test_entities[1]],
        # 门窗组（列表格式）
        [test_entities[2], test_entities[3]]
    ]
    
    print(f"测试数据:")
    print(f"  实体数: {len(test_entities)}")
    print(f"  组数: {len(test_groups)}")
    
    # 设置处理器数据
    processor.entities = test_entities
    processor.all_groups = test_groups
    processor.auto_labeled_entities = test_entities
    
    print(f"\n🔧 测试组信息更新:")
    
    # 检查初始状态
    print(f"1. 初始状态:")
    print(f"   all_groups 长度: {len(processor.all_groups)}")
    print(f"   groups_info 长度: {len(processor.groups_info)}")
    
    # 手动调用更新方法
    print(f"\n2. 手动调用 _update_groups_info():")
    processor._update_groups_info()
    
    print(f"   更新后 groups_info 长度: {len(processor.groups_info)}")
    
    # 检查组信息内容
    print(f"\n3. 组信息详情:")
    for i, info in enumerate(processor.groups_info):
        print(f"   组 {i+1}: {info}")
    
    # 测试 get_groups_info 方法
    print(f"\n4. 测试 get_groups_info() 方法:")
    groups_info = processor.get_groups_info()
    print(f"   返回的组信息数量: {len(groups_info)}")
    
    return processor

def debug_with_dict_format_groups():
    """调试字典格式的组"""
    print(f"\n🔍 调试字典格式的组")
    print("="*60)
    
    # 创建处理器
    processor = EnhancedCADProcessor()
    
    # 创建测试数据
    test_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': '墙体', 'auto_labeled': True},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'label': '墙体', 'auto_labeled': True},
    ]
    
    # 创建字典格式的组
    dict_groups = [
        {
            'entities': test_entities,
            'label': '墙体',
            'group_type': 'wall',
            'layer': 'A-WALL',
            'status': 'auto_labeled',
            'confidence': 0.9
        }
    ]
    
    print(f"测试数据:")
    print(f"  字典格式组数: {len(dict_groups)}")
    print(f"  组内实体数: {len(dict_groups[0]['entities'])}")
    
    # 设置处理器数据
    processor.all_groups = dict_groups
    
    print(f"\n🔧 测试字典格式组信息更新:")
    
    # 调用更新方法
    processor._update_groups_info()
    
    print(f"   更新后 groups_info 长度: {len(processor.groups_info)}")
    
    # 检查组信息内容
    print(f"\n组信息详情:")
    for i, info in enumerate(processor.groups_info):
        print(f"   组 {i+1}: {info}")
    
    return processor

def debug_mixed_format_groups():
    """调试混合格式的组"""
    print(f"\n🔍 调试混合格式的组")
    print("="*60)
    
    # 创建处理器
    processor = EnhancedCADProcessor()
    
    # 创建测试数据
    wall_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': '墙体', 'auto_labeled': True},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'label': '墙体', 'auto_labeled': True},
    ]
    
    door_entities = [
        {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(150, 50), (200, 50)], 'label': '门窗', 'auto_labeled': True},
    ]
    
    # 创建混合格式的组
    mixed_groups = [
        # 字典格式的墙体组
        {
            'entities': wall_entities,
            'label': '墙体',
            'group_type': 'wall',
            'layer': 'A-WALL',
            'status': 'auto_labeled',
            'confidence': 0.9
        },
        # 列表格式的门组
        door_entities
    ]
    
    print(f"测试数据:")
    print(f"  混合格式组数: {len(mixed_groups)}")
    print(f"  组1 (字典): {len(mixed_groups[0]['entities'])} 个实体")
    print(f"  组2 (列表): {len(mixed_groups[1])} 个实体")
    
    # 设置处理器数据
    processor.all_groups = mixed_groups
    
    print(f"\n🔧 测试混合格式组信息更新:")
    
    # 调用更新方法
    processor._update_groups_info()
    
    print(f"   更新后 groups_info 长度: {len(processor.groups_info)}")
    
    # 检查组信息内容
    print(f"\n组信息详情:")
    for i, info in enumerate(processor.groups_info):
        print(f"   组 {i+1}: {info}")
    
    return processor

def test_real_scenario():
    """测试真实场景"""
    print(f"\n🔍 测试真实场景")
    print("="*60)
    
    # 创建处理器
    processor = EnhancedCADProcessor()
    
    # 模拟真实的自动处理流程
    test_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (6000, 0)], 'color': 7},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(6000, 0), (6000, 4000)], 'color': 7},
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(1000, -50), (2000, -50)], 'color': 3},
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(1000, 50), (2000, 50)], 'color': 3},
    ]
    
    processor.entities = test_entities
    processor.all_groups = []
    processor.auto_labeled_entities = []
    
    print(f"模拟自动处理流程:")
    
    # 1. 识别特殊图层
    wall_layers = processor.processor._detect_special_layers(
        test_entities, processor.processor.wall_layer_patterns, debug=False, layer_type="墙体"
    )
    door_window_layers = processor.processor._detect_special_layers(
        test_entities, processor.processor.door_window_layer_patterns, debug=False, layer_type="门窗"
    )
    
    print(f"   墙体图层: {wall_layers}")
    print(f"   门窗图层: {door_window_layers}")
    
    # 2. 分组
    wall_entities = [e for e in test_entities if e['layer'] in wall_layers]
    door_window_entities = [e for e in test_entities if e['layer'] in door_window_layers]
    
    auto_groups = []
    
    if wall_entities:
        wall_groups = processor.processor._group_special_entities_by_layer(
            wall_entities, connection_threshold=10, entity_type="wall"
        )
        
        # 处理墙体组
        for group in wall_groups:
            entities_list = group.get('entities', []) if isinstance(group, dict) else group
            
            # 自动标注
            for entity in entities_list:
                if isinstance(entity, dict):
                    entity['label'] = '墙体'
                    entity['auto_labeled'] = True
            
            auto_groups.append(entities_list)
            processor.auto_labeled_entities.extend(entities_list)
    
    if door_window_entities:
        door_window_groups = processor.processor._group_special_entities_by_layer(
            door_window_entities, connection_threshold=100, entity_type="door_window"
        )
        
        # 处理门窗组
        for group in door_window_groups:
            entities_list = group.get('entities', []) if isinstance(group, dict) else group
            
            # 自动标注
            for entity in entities_list:
                if isinstance(entity, dict):
                    entity['label'] = '门窗'
                    entity['auto_labeled'] = True
            
            auto_groups.append(entities_list)
            processor.auto_labeled_entities.extend(entities_list)
    
    # 3. 设置所有组
    processor.all_groups = auto_groups
    
    print(f"   自动分组完成: {len(auto_groups)} 个组")
    print(f"   自动标注实体: {len(processor.auto_labeled_entities)} 个")
    
    # 4. 更新组信息
    print(f"\n调用 _update_groups_info():")
    processor._update_groups_info()
    
    print(f"   groups_info 长度: {len(processor.groups_info)}")
    
    # 5. 检查结果
    print(f"\n最终结果:")
    groups_info = processor.get_groups_info()
    print(f"   get_groups_info() 返回: {len(groups_info)} 个组")
    
    for i, info in enumerate(groups_info):
        print(f"     组 {i+1}: 状态={info.get('status')}, 实体数={info.get('entity_count')}, 图层={info.get('layer')}")

def main():
    """主测试函数"""
    print("🚀 开始调试组信息更新问题")
    
    # 1. 基本测试
    debug_groups_info_update()
    
    # 2. 字典格式测试
    debug_with_dict_format_groups()
    
    # 3. 混合格式测试
    debug_mixed_format_groups()
    
    # 4. 真实场景测试
    test_real_scenario()
    
    print(f"\n✅ 调试完成")

if __name__ == "__main__":
    main()
