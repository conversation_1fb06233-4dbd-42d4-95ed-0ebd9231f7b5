#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CAD分类标注工具 - 增强版（融合V2墙体填充）
集成改进版墙体填充处理器V2，实现：
1. 使用create_fill_polygons_enhanced方法识别外轮廓
2. 使用完整线段打断算法
3. 处理重叠线条、间隙和缺失端头
4. 仅识别墙体组的外轮廓进行填充，不识别空腔
5. 墙体和门窗自动完成标注
6. 实时状态更新
7. 分层处理策略
8. 灵活保存选项
9. 智能小元素合并
10. 小组自动合并
"""

import os
import pandas as pd
import numpy as np
import json
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, ttk, Frame, Label, Button, StringVar, Checkbutton, BooleanVar, Entry
import threading
import time
import traceback
from collections import defaultdict
import hashlib
import pickle
import math

# 添加matplotlib导入
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.font_manager as fm

# 导入自定义模块
from cad_data_processor import CADDataProcessor
from cad_visualizer import CADVisualizer
# 导入墙体填充处理器（兼容性包装器）
from wall_fill_processor import WallFillProcessor

# 导入增强版墙体填充处理器V2
try:
    from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2
except ImportError as e:
    EnhancedWallFillProcessorV2 = None

# 导入交互式填充窗口
try:
    from interactive_wall_fill_window import InteractiveWallFillWindow
except ImportError as e:
    InteractiveWallFillWindow = None 

class EnhancedCADProcessor:
    """增强版CAD处理器（融合V2墙体填充）"""
    
    def __init__(self, visualizer=None, canvas=None):
        self.processor = CADDataProcessor()
        self.visualizer = visualizer
        self.canvas = canvas
        
        # 状态管理
        self.is_running = False
        self.should_stop = False
        self.current_file = ""
        self.current_file_entities = []
        self.all_groups = []
        
        # 自动标注的实体
        self.auto_labeled_entities = []
        
        # 手动分组状态
        self.manual_grouping_mode = False
        self.pending_manual_groups = []
        self.current_manual_group_index = 0
        
        # 回调函数
        self.status_callback = None
        self.progress_callback = None
        
        # 数据集
        self.dataset = []
        self.labeled_entities = []
        
        # 组状态跟踪
        self.groups_info = []  # 存储所有组的状态信息
        
        # V2墙体填充处理器
        if EnhancedWallFillProcessorV2:
            self.wall_fill_processor_v2 = EnhancedWallFillProcessorV2()
        else:
            self.wall_fill_processor_v2 = None
        
    def set_callbacks(self, status_callback, progress_callback):
        """设置回调函数"""
        self.status_callback = status_callback
        self.progress_callback = progress_callback
    
    def process_folder(self, folder_path):
        """处理文件夹中的所有DXF文件"""
        self.is_running = True
        self.should_stop = False
        
        # 获取所有DXF文件
        dxf_files = []
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                if file.lower().endswith('.dxf'):
                    dxf_files.append(os.path.join(root, file))
        
        if not dxf_files:
            if self.status_callback:
                self.status_callback("error", "未找到DXF文件")
            return
        
        total_files = len(dxf_files)
        if self.status_callback:
            self.status_callback("info", f"找到 {total_files} 个DXF文件")
        
        # 处理每个文件
        for i, file_path in enumerate(dxf_files):
            if self.should_stop:
                break
                
            self.current_file = os.path.basename(file_path)
            
            if self.status_callback:
                self.status_callback("file_start", (i + 1, total_files, self.current_file))
            
            success = self.process_single_file(file_path)
            
            if self.status_callback:
                if success:
                    self.status_callback("file_complete", (i + 1, total_files, self.current_file))
                else:
                    self.status_callback("file_error", (i + 1, total_files, self.current_file))
            
            if self.progress_callback:
                self.progress_callback(i + 1, total_files)
        
        self.is_running = False
        
        if self.status_callback:
            if self.should_stop:
                self.status_callback("stopped", "处理已停止")
            else:
                self.status_callback("completed", "所有文件处理完成")
    
    def process_single_file(self, file_path):
        """处理单个DXF文件"""
        try:
            if self.status_callback:
                self.status_callback("status", f"正在加载 {os.path.basename(file_path)}...")
            
            # 加载DXF文件
            entities = self.processor.load_dxf_file(file_path)
            if not entities:
                if self.status_callback:
                    self.status_callback("error", f"无法加载文件: {file_path}")
                return False
            
            self.current_file_entities = entities
            
            if self.status_callback:
                self.status_callback("status", f"加载完成，共 {len(entities)} 个实体")
            
            # 步骤1: 自动分组和标注墙体、门窗、栏杆
            if self.status_callback:
                self.status_callback("status", "正在自动处理墙体、门窗、栏杆...")
            
            auto_groups = self._auto_process_special_entities(entities)
            
            # 步骤2: 处理其他实体 - 智能分组
            if self.status_callback:
                self.status_callback("status", "正在处理其他实体...")
            
            other_groups = self._process_other_entities(entities, auto_groups)
            
            # 步骤3: 优化小元素和小组
            if self.status_callback:
                self.status_callback("status", "正在优化小元素和小组...")
            
            optimized_groups = self._optimize_groups(other_groups, auto_groups)
            
            # 合并所有组
            self.all_groups = auto_groups + optimized_groups
            
            # 强制合并SPLINE实体
            print(f"调用强制合并SPLINE实体，当前组数: {len(self.all_groups)}")
            self.all_groups = self.processor._force_merge_spline_entities(self.all_groups)
            print(f"强制合并完成，最终组数: {len(self.all_groups)}")
            
            # 步骤4: 进入手动分组模式（如果有其他组需要处理）
            # 使用更准确的方法来确定需要手动处理的组
            self._update_pending_manual_groups()
            
            if self.pending_manual_groups:
                if self.status_callback:
                    self.status_callback("status", f"需要手动标注 {len(self.pending_manual_groups)} 个组")
                
                self.current_manual_group_index = 0
                self.manual_grouping_mode = True
                
                # 更新组状态信息
                self._update_groups_info()
                
                # 显示第一个待标注的组
                self._show_next_manual_group()
            else:
                if self.status_callback:
                    self.status_callback("status", "所有组已自动处理完成")
                self.manual_grouping_mode = False
                
                # 更新组状态信息
                self._update_groups_info()
            
            # 处理完成后检查未处理组
            if self.pending_manual_groups:
                self.manual_grouping_mode = True
                self._show_next_manual_group()  # 显示第一个待处理组
            else:
                # 只有当确实没有待处理组时才显示完成
                self._show_completion_message()
            
            return True
            
        except Exception as e:
            if self.status_callback:
                self.status_callback("error", f"处理文件失败: {str(e)}")
            print(f"错误详情: {traceback.format_exc()}")
            return False 
    
    def _auto_process_special_entities(self, entities):
        """自动处理特殊实体（墙体、门窗、栏杆）"""
        # 识别特殊图层
        wall_layers = self.processor._detect_special_layers(entities, self.processor.wall_layer_patterns, debug=True, layer_type="墙体")
        door_window_layers = self.processor._detect_special_layers(entities, self.processor.door_window_layer_patterns, debug=True, layer_type="门窗")
        railing_layers = self.processor._detect_special_layers(entities, self.processor.railing_layer_patterns, debug=True, layer_type="栏杆")
        
        print(f"特殊图层识别结果:")
        print(f"  墙体图层: {wall_layers}")
        print(f"  门窗图层: {door_window_layers}")
        print(f"  栏杆图层: {railing_layers}")
        
        auto_groups = []
        
        # 处理墙体
        wall_entities = [e for e in entities if e['layer'] in wall_layers]
        if wall_entities:
            wall_groups = self.processor._group_special_entities_by_layer(
                wall_entities, connection_threshold=10, entity_type="wall"
            )
            
            # 墙体分组完成后，合并包含的墙体组
            if self.status_callback:
                self.status_callback("status", "正在合并包含的墙体组...")
            
            wall_groups = self.processor.merge_contained_wall_groups(wall_groups)
            
            # 自动标注墙体
            for group in wall_groups:
                for entity in group:
                    entity['label'] = 'wall'
                    entity['auto_labeled'] = True
                
                # 添加到数据集
                features = self.processor.extract_features(group)
                self.dataset.append({
                    'features': features,
                    'label': 'wall',
                    'source_file': self.current_file,
                    'entity_count': len(group),
                    'auto_labeled': True
                })
            
            auto_groups.extend(wall_groups)
            self.auto_labeled_entities.extend(wall_entities)
            
            if self.status_callback:
                self.status_callback("auto_labeled", ("墙体", len(wall_groups), len(wall_entities)))
        
        # 处理门窗
        door_window_entities = [e for e in entities if e['layer'] in door_window_layers]
        if door_window_entities:
            door_window_groups = self.processor._group_special_entities_by_layer(
                door_window_entities, connection_threshold=50, entity_type="door_window"
            )
            
            # 自动标注门窗
            for group in door_window_groups:
                for entity in group:
                    entity['label'] = 'door_window'
                    entity['auto_labeled'] = True
                
                # 添加到数据集
                features = self.processor.extract_features(group)
                self.dataset.append({
                    'features': features,
                    'label': 'door_window',
                    'source_file': self.current_file,
                    'entity_count': len(group),
                    'auto_labeled': True
                })
            
            auto_groups.extend(door_window_groups)
            self.auto_labeled_entities.extend(door_window_entities)
            
            if self.status_callback:
                self.status_callback("auto_labeled", ("门窗", len(door_window_groups), len(door_window_entities)))
        
        # 处理栏杆
        railing_entities = [e for e in entities if e['layer'] in railing_layers]
        if railing_entities:
            railing_groups = self.processor._group_special_entities_by_layer(
                railing_entities, connection_threshold=20, entity_type="railing"
            )
            
            # 自动标注栏杆
            for group in railing_groups:
                for entity in group:
                    entity['label'] = 'railing'
                    entity['auto_labeled'] = True
                
                # 添加到数据集
                features = self.processor.extract_features(group)
                self.dataset.append({
                    'features': features,
                    'label': 'railing',
                    'source_file': self.current_file,
                    'entity_count': len(group),
                    'auto_labeled': True
                })
            
            auto_groups.extend(railing_groups)
            self.auto_labeled_entities.extend(railing_entities)
            
            if self.status_callback:
                self.status_callback("auto_labeled", ("栏杆", len(railing_groups), len(railing_entities)))
        
        # 在自动标注完成后更新组状态信息
        self._update_groups_info()
        
        # 更新可视化
        if self.visualizer and self.canvas:
            try:
                self.visualizer.visualize_overview(
                    self.current_file_entities,
                    [],  # 没有当前处理组
                    self.auto_labeled_entities,  # 已自动标注的实体
                    processor=self
                )
                self.visualizer.update_canvas(self.canvas)
            except Exception as e:
                print(f"自动标注可视化更新失败: {e}")
        
        return auto_groups
    
    def _process_other_entities(self, entities, auto_groups):
        """处理其他实体"""
        # 获取未被自动处理的实体
        auto_entity_ids = set(id(e) for group in auto_groups for e in group)
        other_entities = [e for e in entities if id(e) not in auto_entity_ids]
        
        if not other_entities:
            return []
        
        # 对其他实体进行连接性分组
        # 设置distance_threshold为40（同图层分组阈值=40，孤立实体合并阈值=100）
        other_groups = self.processor._group_other_entities_by_layer(other_entities, distance_threshold=40)
        
        return other_groups
    
    def _optimize_groups(self, other_groups, auto_groups=None):
        """优化组：处理小元素和小组"""
        if not other_groups:
            return []

        # auto_groups参数保留用于未来扩展，当前版本暂不使用
        _ = auto_groups  # 避免未使用变量警告
        
        # 获取特殊图层信息
        wall_layers = self.processor._detect_special_layers(self.current_file_entities, self.processor.wall_layer_patterns)
        door_window_layers = self.processor._detect_special_layers(self.current_file_entities, self.processor.door_window_layer_patterns)
        railing_layers = self.processor._detect_special_layers(self.current_file_entities, self.processor.railing_layer_patterns)
        
        # 步骤1: 合并小元素到就近组
        groups_after_small_elements = self.processor.merge_small_elements_to_nearest_groups(
            other_groups, wall_layers, door_window_layers, railing_layers, size_threshold=30
        )
        
        # 步骤2: 合并小组到就近组
        groups_after_small_groups = self.processor.merge_small_groups_to_nearest(
            groups_after_small_elements, wall_layers, door_window_layers, railing_layers, size_threshold=60
        )
        
        # 步骤3: 将所有非特殊图层的独立组合并为一个组
        optimized_groups = self.processor.merge_isolated_groups_to_single(
            groups_after_small_groups, wall_layers, door_window_layers, railing_layers
        )
        
        return optimized_groups 
    
    def _update_pending_manual_groups(self):
        """更新待处理手动组列表"""
        self.pending_manual_groups = [
            g for g in self.all_groups 
            if not any(e.get('auto_labeled', False) for e in g)  # 非自动标注
            and not any(e.get('label') for e in g)  # 未标注
        ]

    def _clean_group_data(self, group):
        """清理组数据，确保只包含有效的实体字典"""
        if isinstance(group, dict):
            entities = group.get('entities', [])
        elif isinstance(group, list):
            entities = group
        else:
            return []

        # 过滤并清理实体
        cleaned_entities = []
        for entity in entities:
            if isinstance(entity, dict) and entity.get('type') and entity.get('layer'):
                cleaned_entities.append(entity)
            else:
                print(f"    ⚠️ 跳过无效实体: {type(entity)} - {str(entity)[:100]}")

        return cleaned_entities

    def _update_groups_info(self):
        """更新组状态信息（增强版）"""
        # 调试信息
        print(f"更新组状态: 总组数={len(self.all_groups)}, 自动标注组数={len([g for g in self.all_groups if any(e.get('auto_labeled') for e in g)])}")
        
        self.groups_info = []
        auto_groups_count = len([g for g in self.all_groups if any(e.get('auto_labeled') for e in g)])
        
        for i, group in enumerate(self.all_groups):
            # 调试信息
            print(f"处理组{i+1}: 实体数={len(group)}")
            
            is_auto_labeled = any(e.get('auto_labeled', False) for e in group)
            is_labeled = any(e.get('label') for e in group)
            
            # 新增待处理状态
            is_pending = (not is_auto_labeled and not is_labeled and 
                         i >= auto_groups_count and 
                         (i - auto_groups_count) < len(self.pending_manual_groups))
            if is_auto_labeled:
                status = 'auto_labeled'
            elif is_labeled:
                status = 'labeled'
            elif is_pending:
                status = 'pending'  # 新增待处理状态
            else:
                status = 'unlabeled'
            self.groups_info.append({
                'status': status,
                'type': next((e.get('label') for e in group if e.get('label')), ''),
                'entity_count': len(group)
            })
        # 确保当前处理组标记为"标注中"
        if self.manual_grouping_mode and self.pending_manual_groups:
            current_group = self.pending_manual_groups[self.current_manual_group_index]
            if current_group in self.all_groups:
                group_index = self.all_groups.index(current_group)
                self.groups_info[group_index]['status'] = 'labeling'
    
    def _show_completion_message(self):
        """显示处理完成消息"""
        if self.visualizer and self.canvas:
            try:
                # 清空详细视图
                self.visualizer.ax_detail.clear()
                self.visualizer.ax_detail.text(0.5, 0.5, '所有分组已完成', 
                                               ha='center', va='center', transform=self.visualizer.ax_detail.transAxes,
                                               fontproperties=self.visualizer.chinese_font, fontsize=14)
                self.visualizer.ax_detail.set_title('CAD实体组预览 (已完成)', fontsize=12, fontproperties=self.visualizer.chinese_font)
                
                # 更新全图概览，不显示当前处理组
                self.visualizer.visualize_overview(
                    self.current_file_entities,
                    None,  # 不显示当前处理组
                    self.auto_labeled_entities + self.labeled_entities,  # 已标注的实体
                    processor=self
                )
                self.visualizer.update_canvas(self.canvas)
            except Exception as e:
                print(f"完成时可视化更新失败: {e}")
        
        if self.status_callback:
            self.status_callback("completed", "所有组已自动处理完成")
    
    def _show_next_manual_group(self):
        """显示下一个需要手动标注的组"""
        if not self.pending_manual_groups or self.current_manual_group_index >= len(self.pending_manual_groups):
            # 所有手动组都处理完了，检查是否还有其他未标注的组
            self.manual_grouping_mode = False
            
            # 更新组状态信息，确保状态同步
            self._update_groups_info()
            
            # 检查是否还有其他未标注的组
            if self.has_unlabeled_groups():
                next_group = self.get_next_unlabeled_group()
                if next_group is not None:
                    # 自动跳转到下一个未标注的组
                    self.jump_to_group(next_group)
                    if self.status_callback:
                        self.status_callback("auto_jump", f"自动跳转到组{next_group}")
                    return
            
            # 如果确实没有更多未标注的组，才显示完成信息
            if self.visualizer and self.canvas:
                try:
                    # 清空详细视图
                    self.visualizer.ax_detail.clear()
                    self.visualizer.ax_detail.text(0.5, 0.5, '所有分组已完成', 
                                                   ha='center', va='center', transform=self.visualizer.ax_detail.transAxes,
                                                   fontproperties=self.visualizer.chinese_font, fontsize=14)
                    self.visualizer.ax_detail.set_title('CAD实体组预览 (已完成)', fontsize=12, fontproperties=self.visualizer.chinese_font)
                    
                    # 更新全图概览，不显示当前处理组
                    self.visualizer.visualize_overview(
                        self.current_file_entities,
                        None,  # 不显示当前处理组
                        self.auto_labeled_entities + self.labeled_entities,  # 已标注的实体
                        processor=self
                    )
                    self.visualizer.update_canvas(self.canvas)
                except Exception as e:
                    print(f"完成时可视化更新失败: {e}")
            
            if self.status_callback:
                self.status_callback("manual_complete", "所有手动分组已完成")
            return
        
        current_group = self.pending_manual_groups[self.current_manual_group_index]
        
        print(f"显示手动分组组: {self.current_manual_group_index + 1}/{len(self.pending_manual_groups)}, 实体数量: {len(current_group)}")
        
        # 更新组状态信息，确保当前组标记为"标注中"
        self._update_groups_info()
        
        if self.status_callback:
            self.status_callback("manual_group", {
                'index': self.current_manual_group_index + 1,
                'total': len(self.pending_manual_groups),
                'entity_count': len(current_group)
            })
            # 通知UI更新组列表
            self.status_callback("update_group_list", None)
        
        # 更新可视化，突出显示当前组
        if self.visualizer and self.canvas:
            try:
                print("🔍 更新详细视图...")

                # 清理组数据，确保只包含有效实体
                cleaned_group = self._clean_group_data(current_group)
                print(f"  清理后组数据: {len(cleaned_group)} 个实体")

                self.visualizer.visualize_entity_group(cleaned_group, self.processor.category_mapping)

                print("🌍 更新全图概览...")
                # 获取当前组索引
                current_group_index = None
                if current_group in self.all_groups:
                    current_group_index = self.all_groups.index(current_group) + 1

                self.visualizer.visualize_overview(
                    self.current_file_entities,
                    cleaned_group,  # 使用清理后的当前组
                    self.auto_labeled_entities + self.labeled_entities,  # 已标注的实体
                    current_group_index=current_group_index  # 传递组索引
                )
                self.visualizer.update_canvas(self.canvas)
                print("可视化更新成功")
            except Exception as e:
                print(f"手动分组可视化更新失败: {e}")
                import traceback
                traceback.print_exc()
    
    def label_current_group(self, label):
        """为当前组添加标签"""
        if not self.manual_grouping_mode or self.current_manual_group_index >= len(self.pending_manual_groups):
            return False
        
        current_group = self.pending_manual_groups[self.current_manual_group_index]
        
        # 为组中的每个实体设置标签
        for entity in current_group:
            entity['label'] = label
        
        # 添加到数据集
        features = self.processor.extract_features(current_group)
        self.dataset.append({
            'features': features,
            'label': label,
            'source_file': self.current_file,
            'entity_count': len(current_group),
            'auto_labeled': False
        })
        
        # 添加到已标注实体列表
        self.labeled_entities.extend(current_group)
        
        # 移动到下一组
        self.current_manual_group_index += 1
        
        # 更新组状态信息
        self._update_groups_info()
        
        # 显示下一个组
        self._show_next_manual_group()
        
        # 通知界面更新组列表
        if self.status_callback:
            self.status_callback("update_group_list", None)
            category_name = self.processor.category_mapping.get(label, label)
            self.status_callback("group_labeled", (label, category_name, len(current_group)))
        
        return True
    
    def skip_current_group(self):
        """跳过当前组"""
        if not self.manual_grouping_mode or self.current_manual_group_index >= len(self.pending_manual_groups):
            return False
        
        # 移动到下一组
        self.current_manual_group_index += 1
        
        # 更新组状态信息
        self._update_groups_info()
        
        # 显示下一个组
        self._show_next_manual_group()
        
        if self.status_callback:
            self.status_callback("group_skipped", "已跳过当前组")
        
        return True
    
    def stop_processing(self):
        """停止处理"""
        self.should_stop = True
        self.is_running = False
        self.manual_grouping_mode = False
    
    def auto_fill_walls(self):
        """墙体自动填充功能（改进V2版本 - 处理重叠、间隙和缺失端头）"""
        if not self.processor or not self.processor.current_file_entities:
            messagebox.showwarning("警告", "请先加载CAD文件")
            return
        
        if not self.wall_fill_processor_v2:
            messagebox.showerror("错误", "V2墙体填充处理器不可用")
            return
        
        try:
            # 获取当前文件的所有实体
            entities = self.processor.current_file_entities
            
            # 询问用户选择填充模式
            choice = messagebox.askyesno("选择填充模式", 
                "是否使用交互式填充模式？\n\n"
                "是 - 交互式填充（逐步控制）\n"
                "否 - 自动填充（一键完成）")
            
            if choice:
                # 交互式填充模式
                self._start_interactive_fill(entities)
            else:
                # 自动填充模式
                self._auto_fill_walls_impl(entities)
            
        except Exception as e:
            error_msg = f"墙体填充失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            if self.status_callback:
                self.status_callback("error", error_msg)
    
    def _start_interactive_fill(self, entities):
        """启动交互式填充"""
        try:
            if InteractiveWallFillWindow:
                # 传递self作为parent，这样交互窗口就能访问主程序的方法和属性
                InteractiveWallFillWindow(
                    self, entities, self.wall_fill_processor_v2
                )
            else:
                messagebox.showerror("错误", "交互式填充窗口不可用")
                # 回退到自动填充
                self._auto_fill_walls_impl(entities)
        except Exception as e:
            messagebox.showerror("错误", f"无法启动交互式填充窗口: {e}")
            # 回退到自动填充
            self._auto_fill_walls_impl(entities)
    
    def _auto_fill_walls_impl(self, entities):
        """自动填充实现"""
        try:
            # 使用改进V2处理器处理墙体填充（处理重叠、间隙和缺失端头）
            filled_groups = self.wall_fill_processor_v2.process_wall_filling_enhanced(
                entities, connection_threshold=20
            )
            
            if not filled_groups:
                messagebox.showinfo("提示", "未找到可填充的墙体组")
                return
            
            # 增加调试信息
            print("\n" + "="*50)
            print("改进V2墙体填充调试信息（处理重叠、间隙和缺失端头）")
            print("="*50)
            
            for i, filled_group in enumerate(filled_groups):
                print(f"\n--- 墙体组 {i+1} ---")
                wall_group = filled_group['wall_group']
                
                # 显示墙体组详细信息
                self.wall_fill_processor_v2.debug_wall_group_info(wall_group)
                
                # 显示面积计算调试
                self.wall_fill_processor_v2.debug_area_calculation(wall_group, entities)
                
                # 显示填充结果
                fill_polygons = filled_group.get('fill_polygons', [])
                cavities = filled_group.get('cavities', [])
                print(f"填充区域数量: {len(fill_polygons)}")
                print(f"空腔数量: {len(cavities)}")
                
                for j, fill_poly in enumerate(fill_polygons):
                    try:
                        if hasattr(fill_poly, 'exterior'):
                            point_count = len(list(fill_poly.exterior.coords))
                        else:
                            point_count = len(fill_poly)
                        print(f"  填充区域 {j+1}: {point_count}个点")
                    except Exception as e:
                        print(f"  填充区域 {j+1}: 无法获取点数 - {e}")
                
                for j, cavity in enumerate(cavities):
                    try:
                        if hasattr(cavity, 'exterior'):
                            point_count = len(list(cavity.exterior.coords))
                        else:
                            point_count = len(cavity)
                        print(f"  空腔 {j+1}: {point_count}个点")
                    except Exception as e:
                        print(f"  空腔 {j+1}: 无法获取点数 - {e}")
            
            print("="*50)
            print("调试信息结束")
            print("="*50 + "\n")
            
            # 保存填充结果到实例变量
            self.current_wall_fills = filled_groups
            self.current_wall_fill_processor = self.wall_fill_processor_v2
            
            # 更新可视化
            if self.visualizer:
                self._update_visualization_with_fills_v2(filled_groups)
            
            # 统计信息
            total_fill_polygons = sum(len(fg['fill_polygons']) for fg in filled_groups)
            total_cavities = sum(len(fg['cavities']) for fg in filled_groups)
            
            messagebox.showinfo("成功", 
                f"已完成 {len(filled_groups)} 个墙体组的改进V2填充（处理重叠、间隙和缺失端头）\n"
                f"填充区域: {total_fill_polygons} 个\n"
                f"空腔区域: {total_cavities} 个\n"
                f"点击'保存填充'将其应用到全图概览")
            
        except Exception as e:
            error_msg = f"自动填充失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            if self.status_callback:
                self.status_callback("error", error_msg)
    
    def save_wall_fills(self):
        """保存完整V2墙体填充到全图概览"""
        if not hasattr(self, 'current_wall_fills') or not self.current_wall_fills:
            messagebox.showwarning("警告", "请先执行完整V2墙体自动填充")
            return
        
        try:
            # 更新全图概览显示完整V2填充
            if self.visualizer and self.processor:
                self._update_overview_with_fills()
            
            # 检查是否有未分类的实体组
            if self.processor and self.processor.has_unlabeled_groups():
                next_unlabeled_group = self.processor.get_next_unlabeled_group()
                if next_unlabeled_group:
                    # 跳转到第一个未分类的实体组
                    self.processor.jump_to_group(next_unlabeled_group)
                    messagebox.showinfo("提示", f"完整V2墙体填充已保存到全图概览，已跳转到第 {next_unlabeled_group} 个未分类实体组进行手动分类")
                else:
                    messagebox.showinfo("提示", "完整V2墙体填充已保存到全图概览")
            else:
                messagebox.showinfo("提示", "完整V2墙体填充已保存到全图概览，所有实体组已分类完成")
            
        except Exception as e:
            error_msg = f"保存完整V2填充失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            if self.status_callback:
                self.status_callback("error", error_msg)
    
    def _update_visualization_with_fills_v2(self, filled_groups):
        """更新可视化以显示完整V2填充（仅外轮廓）"""
        if not self.visualizer:
            return
        
        try:
            # 清除当前图形
            self.visualizer.ax_detail.clear()
            
            # 绘制原始实体
            if self.processor and self.processor.current_file_entities:
                for entity in self.processor.current_file_entities:
                    self.visualizer._draw_entity(entity, '#000000', 1, 1.0, self.visualizer.ax_detail)
            
            # 使用完整V2处理器创建patches
            if hasattr(self.wall_fill_processor_v2, 'create_fill_patches'):
                patches_list = self.wall_fill_processor_v2.create_fill_patches(filled_groups)
                
                # 添加所有patches到图形
                for patch in patches_list:
                    self.visualizer.ax_detail.add_patch(patch)
            
            # 更新画布
            self.visualizer.ax_detail.set_title('完整V2墙体填充预览（仅外轮廓）', fontsize=12, fontproperties=self.visualizer.chinese_font)
            
            # 检查canvas是否存在并更新
            if hasattr(self, 'canvas') and self.canvas:
                self.canvas.draw()
            elif hasattr(self.visualizer, 'canvas') and self.visualizer.canvas:
                self.visualizer.canvas.draw()
            else:
                # 如果找不到canvas，尝试通过visualizer更新
                if hasattr(self.visualizer, 'update_canvas'):
                    self.visualizer.update_canvas(None)
            
        except Exception as e:
            print(f"更新完整V2可视化失败: {e}")
    
    def _update_overview_with_fills(self):
        """更新全图概览显示完整V2填充（仅外轮廓）"""
        if not self.visualizer or not self.processor:
            return
        
        try:
            # 设置全局墙体填充数据，确保后续的visualize_overview调用也能显示填充
            if self.current_wall_fills and self.current_wall_fill_processor:
                self.visualizer.set_global_wall_fills(self.current_wall_fills, self.current_wall_fill_processor)
            
            # 更新全图概览，包含完整V2填充效果
            self.visualizer.visualize_overview(
                self.processor.current_file_entities,
                None,  # 不显示当前处理组
                self.processor.auto_labeled_entities + self.processor.labeled_entities,  # 已标注的实体
                wall_fills=self.current_wall_fills,  # 添加完整V2墙体填充
                wall_fill_processor=self.current_wall_fill_processor
            )
            
            # 直接更新canvas（与原始版本保持一致）
            if hasattr(self, 'canvas') and self.canvas:
                self.visualizer.update_canvas(self.canvas)
            else:
                # 如果找不到canvas，尝试通过visualizer更新
                self.visualizer.update_canvas(None)
            
        except Exception as e:
            print(f"更新完整V2全图概览失败: {e}") 
    
    def save_dataset(self, include_images=False, output_dir=None):
        """保存数据集"""
        if not self.dataset:
            return False, "没有可保存的数据"
        
        try:
            if not output_dir:
                output_dir = "output"
            
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存数据集
            dataset_file = os.path.join(output_dir, "cad_dataset.csv")
            df = pd.DataFrame(self.dataset)
            df.to_csv(dataset_file, index=False, encoding='utf-8-sig')
            
            result_msg = f"数据集已保存: {dataset_file} (包含 {len(self.dataset)} 个样本)"
            
            # 保存图片（如果需要）
            if include_images and self.visualizer and self.current_file_entities:
                try:
                    image_file = os.path.join(output_dir, f"{self.current_file}_classification.png")
                    
                    # 创建分类图
                    self.visualizer.visualize_overview(
                        self.current_file_entities,
                        [],  # 没有当前处理组
                        self.auto_labeled_entities + self.labeled_entities,  # 所有已标注实体
                        processor=self
                    )
                    
                    # 保存图片
                    self.visualizer.fig.savefig(image_file, dpi=300, bbox_inches='tight')
                    result_msg += f"\n分类图已保存: {image_file}"
                    
                except Exception as e:
                    print(f"保存图片失败: {e}")
                    result_msg += f"\n图片保存失败: {str(e)}"
            
            return True, result_msg
            
        except Exception as e:
            return False, f"保存失败: {str(e)}"
    
    def get_groups_info(self):
        """获取所有组的状态信息"""
        return self.groups_info
    
    def jump_to_group(self, group_index):
        print(f"跳转到组{group_index}请求")
        print(f"总组数: {len(self.all_groups)}")
        print(f"自动组数: {len([g for g in self.all_groups if any(e.get('auto_labeled', False) for e in g)])}")
        print(f"待处理手动组: {len(self.pending_manual_groups)}")
        
        # 获取实际组对象
        group = self.get_group_by_index(group_index)
        if not group:
            return
        
        # 检查是否自动标注组
        is_auto_labeled = any(e.get('auto_labeled', False) for e in group)
        
        if is_auto_labeled:
            # 自动标注组直接显示
            self._show_group(group)
            self._update_group_status(group_index, 'labeling')
        else:
            # 非自动标注组更新待处理列表
            self._update_pending_manual_groups()
            
            # 找到组在待处理列表中的位置
            try:
                manual_index = self.pending_manual_groups.index(group)
                self.current_manual_group_index = manual_index
                self.manual_grouping_mode = True
                self._show_next_manual_group()
            except ValueError:
                # 组不在待处理列表中
                self._show_group(group)
                self._update_group_status(group_index, 'labeling')
        # 确保更新当前组状态为"标注中"
        self._update_group_status(group_index, 'labeling')
        # 强制更新可视化
        if self.visualizer and self.canvas:
            self._show_group(group)
    
    def _update_groups_info(self):
        """更新组状态信息（增强版）"""
        # 调试信息
        print(f"更新组状态: 总组数={len(self.all_groups)}, 自动标注组数={len([g for g in self.all_groups if any(e.get('auto_labeled') for e in g)])}")
        
        self.groups_info = []
        auto_groups_count = len([g for g in self.all_groups if any(e.get('auto_labeled') for e in g)])
        
        for i, group in enumerate(self.all_groups):
            # 调试信息
            print(f"处理组{i+1}: 实体数={len(group)}")
            
            is_auto_labeled = any(e.get('auto_labeled', False) for e in group)
            is_labeled = any(e.get('label') for e in group)
            
            # 新增待处理状态
            is_pending = (not is_auto_labeled and not is_labeled and 
                         i >= auto_groups_count and 
                         (i - auto_groups_count) < len(self.pending_manual_groups))
            if is_auto_labeled:
                status = 'auto_labeled'
            elif is_labeled:
                status = 'labeled'
            elif is_pending:
                status = 'pending'  # 新增待处理状态
            else:
                status = 'unlabeled'
            self.groups_info.append({
                'status': status,
                'type': next((e.get('label') for e in group if e.get('label')), ''),
                'entity_count': len(group)
            })
        # 确保当前处理组标记为"标注中"
        if self.manual_grouping_mode and self.pending_manual_groups:
            current_group = self.pending_manual_groups[self.current_manual_group_index]
            if current_group in self.all_groups:
                group_index = self.all_groups.index(current_group)
                self.groups_info[group_index]['status'] = 'labeling'
    
    def _update_group_status(self, group_index, status):
        """更新指定组的状态"""
        if 0 < group_index <= len(self.groups_info):
            self.groups_info[group_index-1]['status'] = status
        
        # 强制刷新UI
        if self.status_callback:
            self.status_callback("update_group_list", None)
    
    def can_relabel_group(self, group_index):
        """检查是否可以重新分类指定组"""
        # 调整索引（界面是从1开始，内部是从0开始）
        internal_index = group_index - 1
        
        # 检查自动标注的组
        auto_groups = [g for g in self.all_groups if any(e.get('auto_labeled', False) for e in g)]
        if internal_index < len(auto_groups):
            return True
        
        # 检查手动标注的组
        manual_index = internal_index - len(auto_groups)
        if 0 <= manual_index < len(self.pending_manual_groups):
            return True
        
        return False
    
    def start_relabel_group(self, group_index):
        """开始重新分类指定组"""
        if not self.can_relabel_group(group_index):
            return False
        
        # 获取组并显示在可视化中
        group = self.get_group_by_index(group_index)
        if group and self.visualizer and self.canvas:
            try:
                # 清理组数据，确保只包含有效实体
                cleaned_group = self._clean_group_data(group)
                print(f"  重新分类清理后组数据: {len(cleaned_group)} 个实体")

                self.visualizer.visualize_entity_group(cleaned_group, self.processor.category_mapping)
                self.visualizer.visualize_overview(
                    self.current_file_entities,
                    cleaned_group,  # 使用清理后的当前组
                    self.auto_labeled_entities + self.labeled_entities,
                    processor=self,  # 传递processor
                    current_group_index=group_index  # 传递组索引
                )
                self.visualizer.update_canvas(self.canvas)
            except Exception as e:
                print(f"重新分类可视化更新失败: {e}")
        
        return True
    
    def relabel_group(self, group_index, new_label):
        group = self.get_group_by_index(group_index)

        if not group:
            return False

        try:
            # 更新组中所有实体的标签
            for entity in group:
                entity['label'] = new_label
                # 如果是自动标注的，标记为手动修改
                if entity.get('auto_labeled', False):
                    entity['manually_relabeled'] = True

            # 从原有列表中移除（如果存在）
            if group in self.labeled_entities:
                for entity in group:
                    if entity in self.labeled_entities:
                        self.labeled_entities.remove(entity)

            # 添加到已标注实体列表
            self.labeled_entities.extend(group)

            # 更新数据集中的记录
            self._update_dataset_for_relabeled_group(group, new_label, group_index)

            # 更新组状态信息
            self._update_groups_info()

            # 通知界面更新
            if self.status_callback:
                self.status_callback("update_group_list", None)
                self.status_callback("group_relabeled", (group_index, new_label))

            # 重新分类后，优先跳转到第一个待处理的组
            self._update_pending_manual_groups()  # 重新更新待处理组列表

            if self.pending_manual_groups:
                # 有待处理的手动组，跳转到第一个
                self.current_manual_group_index = 0
                self.manual_grouping_mode = True
                self._show_next_manual_group()
                if self.status_callback:
                    self.status_callback("auto_jump", f"自动跳转到第一个待处理组")
            elif self.has_unlabeled_groups():
                # 没有待处理的手动组，但还有其他未标注组
                next_group = self.get_next_unlabeled_group()
                if next_group:
                    self.jump_to_group(next_group)
                    if self.status_callback:
                        self.status_callback("auto_jump", f"自动跳转到组{next_group}")
                else:
                    self._show_completion_message()
            else:
                # 所有组都已标注完成
                self._show_completion_message()

            return True

        except Exception as e:
            print(f"重新分类失败: {e}")
            return False
    
    def get_group_by_index(self, group_index):
        """根据索引获取组（修正版）"""
        # 总组数检查
        if group_index < 1 or group_index > len(self.all_groups):
            return None
        
        # 直接返回all_groups中的组
        return self.all_groups[group_index-1]
    
    def _validate_group_index(self, group_index):
        """验证组索引有效性"""
        if not isinstance(group_index, int) or group_index < 1:
            return False
        return group_index <= len(self.all_groups)
    
    def _update_pending_manual_groups(self):
        """更新待处理手动组列表"""
        self.pending_manual_groups = [
            g for g in self.all_groups 
            if not any(e.get('auto_labeled', False) for e in g)  # 非自动标注
            and not any(e.get('label') for e in g)  # 未标注
        ]
    
    def _show_group(self, group):
        """显示指定组（统一方法）"""
        # 调试信息
        print(f"显示组: 实体数={len(group) if group else 0}")
        
        if self.visualizer and self.canvas:
            try:
                # 获取组在列表中的位置
                group_index = None
                if group in self.all_groups:
                    group_index = self.all_groups.index(group) + 1

                # 清理组数据，确保只包含有效实体
                cleaned_group = self._clean_group_data(group)
                print(f"  显示组清理后数据: {len(cleaned_group)} 个实体")

                # 更新详细视图
                self.visualizer.visualize_entity_group(cleaned_group, self.processor.category_mapping)

                # 更新全图概览，突出显示当前组，传递正确的组索引
                self.visualizer.visualize_overview(
                    self.current_file_entities,
                    cleaned_group,  # 使用清理后的当前组
                    self.auto_labeled_entities + self.labeled_entities,  # 已标注的实体
                    processor=self,  # 显式传递 processor
                    current_group_index=group_index  # 传递当前组索引
                )
                self.visualizer.update_canvas(self.canvas)
                
                # 调试信息
                if group_index:
                    print(f"组索引: {group_index}/{len(self.all_groups)}")
                else:
                    print("警告: 当前组不在all_groups中")
            except Exception as e:
                print(f"显示组可视化失败: {e}")
                traceback.print_exc()
    
    def _show_completion_message(self):
        """显示真正的完成信息"""
        if self.visualizer and self.canvas:
            self.visualizer.ax_detail.clear()
            self.visualizer.ax_detail.text(0.5, 0.5, '所有处理已完成', 
                                         ha='center', va='center', 
                                         transform=self.visualizer.ax_detail.transAxes,
                                         fontproperties=self.visualizer.chinese_font, 
                                         fontsize=16, color='green')
            self.visualizer.ax_detail.set_title('处理完成', fontsize=14, 
                                              fontproperties=self.visualizer.chinese_font)
            self.visualizer.update_canvas(self.canvas)
        
        if self.status_callback:
            self.status_callback("completed", "所有文件处理完成")
    
    def get_group_info(self, group_index):
        """获取组的详细信息"""
        group = self.get_group_by_index(group_index)
        if not group:
            return {'entity_count': 0, 'status': 'unknown'}
        
        # 获取组的状态
        internal_index = group_index - 1
        if internal_index < len(self.groups_info):
            status_info = self.groups_info[internal_index]
        else:
            status_info = {'status': 'unknown', 'type': ''}
        
        # 获取当前分类
        current_type = None
        for entity in group:
            if entity.get('label'):
                current_type = entity.get('label')
                break
        
        # 获取图层信息
        layers = list(set(entity.get('layer', 'unknown') for entity in group))
        
        # 获取边界框信息
        bbox_info = self._get_group_bbox_info(group)
        
        return {
            'entity_count': len(group),
            'status': status_info['status'],
            'current_type': self.processor.category_mapping.get(current_type, current_type) if current_type else '未分类',
            'layers': layers,
            'bbox_info': bbox_info
        }
    
    def _get_group_bbox_info(self, group):
        """获取组的边界框信息"""
        if not group:
            return "无数据"
        
        try:
            bbox = self.processor._get_group_bbox(group)
            if bbox:
                width = bbox[2] - bbox[0]
                height = bbox[3] - bbox[1]
                return f"宽度: {width:.1f}, 高度: {height:.1f}"
            else:
                return "无法计算"
        except:
            return "计算错误"
    
    def _update_dataset_for_relabeled_group(self, group, new_label, group_index=None):
        """更新数据集中重新分类的组"""
        # 查找并更新数据集中的对应记录
        for i, dataset_item in enumerate(self.dataset):
            # 通过特征匹配找到对应的数据集项
            if self._is_same_group(dataset_item, group):
                # 更新标签
                self.dataset[i]['label'] = new_label
                self.dataset[i]['manually_relabeled'] = True
                self.dataset[i]['relabel_timestamp'] = time.time()
                if group_index is not None:
                    self.dataset[i]['group_index'] = group_index
                break
        else:
            # 如果没找到，创建新的数据集项
            features = self.processor.extract_features(group)
            dataset_item = {
                'features': features,
                'label': new_label,
                'source_file': self.current_file,
                'entity_count': len(group),
                'auto_labeled': False,
                'manually_relabeled': True,
                'relabel_timestamp': time.time()
            }
            if group_index is not None:
                dataset_item['group_index'] = group_index
            self.dataset.append(dataset_item)
    
    def _is_same_group(self, dataset_item, group):
        """检查数据集项是否对应指定的组"""
        # 简单的匹配逻辑：实体数量和源文件匹配
        return (dataset_item.get('entity_count') == len(group) and 
                dataset_item.get('source_file') == self.current_file)
    
    def has_unlabeled_groups(self):
        """检查是否还有未标注的组"""
        # 检查所有组的状态
        for group_info in self.groups_info:
            if group_info['status'] in ['unlabeled', 'labeling']:
                return True
        
        # 如果没有找到未标注的组，检查是否还有未标注的实体
        for group in self.all_groups:
            for entity in group:
                if not entity.get('label'):
                    return True
        
        return False
    
    def get_next_unlabeled_group(self):
        """获取下一个未标注组的索引"""
        # 首先检查groups_info中的未标注组
        for i, group_info in enumerate(self.groups_info):
            if group_info['status'] == 'unlabeled':
                return i + 1  # 返回界面索引（从1开始）
        
        # 如果没有找到，检查所有组中是否有未标注的实体
        for i, group in enumerate(self.all_groups):
            has_unlabeled = False
            for entity in group:
                if not entity.get('label'):
                    has_unlabeled = True
                    break
            
            if has_unlabeled:
                return i + 1  # 返回界面索引（从1开始）
        
        return None 
    
    def export_group_details(self, group_indices=None, output_file="group_details.txt"):
        """导出指定组的实体详细数据，用于分析分组问题"""
        if not self.all_groups:
            return False, "没有可导出的组数据"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("=== CAD分组详细数据导出 ===\n")
                f.write(f"导出时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总组数: {len(self.all_groups)}\n\n")
                
                # 确定要导出的组
                if group_indices is None:
                    # 导出所有组
                    groups_to_export = list(enumerate(self.all_groups, 1))
                else:
                    # 导出指定组
                    groups_to_export = [(i, self.all_groups[i-1]) for i in group_indices if 1 <= i <= len(self.all_groups)]
                
                for group_idx, group in groups_to_export:
                    f.write(f"\n{'='*50}\n")
                    f.write(f"组 {group_idx} 详细信息\n")
                    f.write(f"{'='*50}\n")
                    f.write(f"实体数量: {len(group)}\n")
                    
                    # 统计信息
                    entity_types = {}
                    layers = set()
                    for entity in group:
                        entity_type = entity.get('type', 'unknown')
                        entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
                        layers.add(entity.get('layer', 'unknown'))
                    
                    f.write(f"实体类型分布: {entity_types}\n")
                    f.write(f"图层: {sorted(layers)}\n")
                    
                    # 边界框信息
                    bbox = self.processor._get_group_bbox(group)
                    if bbox:
                        f.write(f"边界框: ({bbox[0]:.2f}, {bbox[1]:.2f}) - ({bbox[2]:.2f}, {bbox[3]:.2f})\n")
                        f.write(f"尺寸: 宽={bbox[2]-bbox[0]:.2f}, 高={bbox[3]-bbox[1]:.2f}\n")
                    
                    # 详细实体信息
                    f.write(f"\n详细实体信息:\n")
                    f.write("-" * 40 + "\n")
                    
                    for i, entity in enumerate(group, 1):
                        f.write(f"\n实体 {i}:\n")
                        f.write(f"  类型: {entity.get('type', 'unknown')}\n")
                        f.write(f"  图层: {entity.get('layer', 'unknown')}\n")
                        
                        # 根据实体类型输出详细信息
                        entity_type = entity.get('type', '')
                        
                        if entity_type == 'LINE':
                            if 'points' in entity and len(entity['points']) >= 2:
                                start, end = entity['points'][0], entity['points'][1]
                                f.write(f"  起点: ({start[0]:.2f}, {start[1]:.2f})\n")
                                f.write(f"  终点: ({end[0]:.2f}, {end[1]:.2f})\n")
                                f.write(f"  长度: {math.sqrt((end[0]-start[0])**2 + (end[1]-start[1])**2):.2f}\n")
                        
                        elif entity_type == 'LWPOLYLINE':
                            if 'points' in entity and entity['points']:
                                points = entity['points']
                                f.write(f"  点数: {len(points)}\n")
                                f.write(f"  是否闭合: {entity.get('closed', False)}\n")
                                f.write(f"  前5个点: {points[:5]}\n")
                                if len(points) > 5:
                                    f.write(f"  后5个点: {points[-5:]}\n")
                        
                        elif entity_type == 'ARC':
                            if 'center' in entity and 'radius' in entity:
                                center = entity['center']
                                radius = entity['radius']
                                start_angle = entity.get('start_angle', 0)
                                end_angle = entity.get('end_angle', 0)
                                f.write(f"  中心: ({center[0]:.2f}, {center[1]:.2f})\n")
                                f.write(f"  半径: {radius:.2f}\n")
                                f.write(f"  起始角度: {start_angle:.2f}°\n")
                                f.write(f"  结束角度: {end_angle:.2f}°\n")
                                f.write(f"  角度范围: {end_angle - start_angle:.2f}°\n")
                        
                        elif entity_type == 'CIRCLE':
                            if 'center' in entity and 'radius' in entity:
                                center = entity['center']
                                radius = entity['radius']
                                f.write(f"  中心: ({center[0]:.2f}, {center[1]:.2f})\n")
                                f.write(f"  半径: {radius:.2f}\n")
                        
                        elif entity_type == 'ELLIPSE':
                            if 'center' in entity and 'major_axis' in entity:
                                center = entity['center']
                                major_axis = entity['major_axis']
                                ratio = entity.get('ratio', 1.0)
                                start_param = entity.get('start_param', 0)
                                end_param = entity.get('end_param', 2 * math.pi)
                                f.write(f"  中心: ({center[0]:.2f}, {center[1]:.2f})\n")
                                f.write(f"  主轴: ({major_axis[0]:.2f}, {major_axis[1]:.2f})\n")
                                f.write(f"  比例: {ratio:.4f}\n")
                                f.write(f"  起始参数: {start_param:.4f}\n")
                                f.write(f"  结束参数: {end_param:.4f}\n")
                                f.write(f"  参数范围: {end_param - start_param:.4f}\n")
                        
                        elif entity_type == 'TEXT':
                            if 'text' in entity and 'position' in entity:
                                text = entity['text']
                                position = entity['position']
                                height = entity.get('height', 0)
                                f.write(f"  文本: '{text}'\n")
                                f.write(f"  位置: ({position[0]:.2f}, {position[1]:.2f})\n")
                                f.write(f"  高度: {height:.2f}\n")
                        
                        # 计算与其他实体的连接性
                        f.write(f"  连接性分析:\n")
                        for j, other_entity in enumerate(group, 1):
                            if i != j:
                                endpoint_dist = self.processor._calculate_endpoint_distance(entity, other_entity)
                                f.write(f"    与实体{j}端点距离: {endpoint_dist:.2f}\n")
                        
                        f.write(f"  {'-'*30}\n")
                
                f.write(f"\n{'='*50}\n")
                f.write("导出完成\n")
                f.write(f"{'='*50}\n")
            
            return True, f"实体详细数据已导出到: {output_file}"
            
        except Exception as e:
            return False, f"导出失败: {str(e)}"
    
    def export_specific_groups(self, group_indices):
        """导出指定组的详细信息"""
        if not group_indices:
            return False, "请指定要导出的组索引"
        
        output_file = f"groups_{'_'.join(map(str, group_indices))}_details.txt"
        return self.export_group_details(group_indices, output_file)

class EnhancedCADApp:
    """增强版CAD标注应用（融合V2墙体填充）"""
    
    def __init__(self, root):
        self.root = root
        self.processor = None
        self.visualizer = None
        self.canvas = None
        
        # GUI变量
        self.folder_var = StringVar()
        self.status_var = StringVar(value="就绪")
        self.progress_var = StringVar(value="等待开始...")
        self.stats_var = StringVar(value="文件: 0/0 | 组: 0/0 | 样本: 0")
        
        # 保存选项
        self.save_images_var = BooleanVar(value=False)
        
        # 状态
        self.current_label = ""
        
        # 墙体填充相关变量
        self.current_wall_fills = None
        self.current_wall_fill_processor = None
        
        self.create_widgets()
        self.setup_shortcuts()
    
    def create_widgets(self):
        """创建UI组件"""
        # 设置窗口
        self.root.title("CAD分类标注工具 - 增强版（融合V2墙体填充）")
        self.root.geometry("1900x1200")
        
        # 主框架
        main_frame = Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = Frame(main_frame, width=400)
        control_frame.pack(side='left', fill='y', padx=(0, 10))
        
        self._create_file_selection(control_frame)
        self._create_process_control(control_frame)
        self._create_status_display(control_frame)
        self._create_category_buttons(control_frame)
        self._create_action_buttons(control_frame)
        self._create_save_options(control_frame)
        self._create_group_list(control_frame)
        
        # 右侧可视化区域
        viz_frame = Frame(main_frame)
        viz_frame.pack(side='right', fill='both', expand=True)
        
        self._create_visualization(viz_frame) 
    
    def _create_file_selection(self, parent):
        """创建文件选择区域"""
        frame = Frame(parent)
        frame.pack(fill='x', pady=(0, 15))
        
        Label(frame, text="数据文件夹:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        folder_entry = tk.Entry(frame, textvariable=self.folder_var, state='readonly')
        folder_entry.pack(fill='x', pady=(5, 0))
        
        Button(frame, text="选择文件夹", command=self.select_folder, 
               bg='#4CAF50', fg='white', font=('Arial', 9)).pack(fill='x', pady=(5, 0))
    
    def _create_process_control(self, parent):
        """创建处理控制区域"""
        frame = Frame(parent)
        frame.pack(fill='x', pady=(0, 15))
        
        Label(frame, text="处理控制:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        button_frame = Frame(frame)
        button_frame.pack(fill='x', pady=(5, 0))
        
        self.start_btn = Button(button_frame, text="开始处理", command=self.start_processing,
                               bg='#2196F3', fg='white', font=('Arial', 9))
        self.start_btn.pack(side='left', fill='x', expand=True, padx=(0, 2))
        
        self.stop_btn = Button(button_frame, text="停止", command=self.stop_processing, state='disabled',
                              bg='#F44336', fg='white', font=('Arial', 9))
        self.stop_btn.pack(side='left', fill='x', expand=True, padx=(2, 0))
    
    def _create_status_display(self, parent):
        """创建状态显示区域"""
        frame = Frame(parent)
        frame.pack(fill='x', pady=(0, 15))
        
        Label(frame, text="处理状态:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        # 进度条
        self.progress_bar = ttk.Progressbar(frame, mode='determinate')
        self.progress_bar.pack(fill='x', pady=(5, 0))
        
        # 状态标签 - 实时更新
        status_label = Label(frame, textvariable=self.status_var, font=('Arial', 9), 
                            fg='blue', wraplength=350, justify='left')
        status_label.pack(anchor='w', fill='x')
        
        # 统计信息
        stats_label = Label(frame, textvariable=self.stats_var, font=('Arial', 9),
                           wraplength=350, justify='left')
        stats_label.pack(anchor='w', fill='x')
    
    def _create_category_buttons(self, parent):
        """创建类别按钮区域"""
        frame = Frame(parent)
        frame.pack(fill='x', pady=(0, 15))
        
        Label(frame, text="选择类别:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        # 创建按钮网格
        button_frame = Frame(frame)
        button_frame.pack(fill='x', pady=(5, 0))
        
        categories = [
            ('wall', '墙体', '#FFA500'),
            ('door_window', '门窗', '#42A5F5'),
            ('railing', '栏杆', '#29B6F6'),
            ('furniture', '家具', '#4DB6AC'),
            ('bed', '床', '#80CBC4'),
            ('sofa', '沙发', '#26A69A'),
            ('cabinet', '柜子', '#00796B'),
            ('dining_table', '餐桌', '#B2DFDB'),
            ('appliance', '家电', '#607D8B'),
            ('stair', '楼梯', '#F9A825'),
            ('elevator', '电梯', '#F57F17'),
            ('dimension', '标注', '#FFCA28'),
            ('room_label', '房间标注', '#EF5350'),
            ('column', '柱子', '#5D4037'),
            ('other', '其他', '#BDBDBD')
        ]
        
        for i, (key, name, color) in enumerate(categories):
            row = i // 3
            col = i % 3
            
            btn = Button(button_frame, text=f"{i+1}. {name}", 
                        command=lambda k=key: self.select_category(k),
                        bg=color, fg='white', font=('Arial', 8))
            btn.grid(row=row, column=col, sticky='ew', padx=1, pady=1)
        
        # 配置列权重
        button_frame.grid_columnconfigure(0, weight=1)
        button_frame.grid_columnconfigure(1, weight=1)
        button_frame.grid_columnconfigure(2, weight=1)
    
    def _create_action_buttons(self, parent):
        """创建操作按钮区域"""
        frame = Frame(parent)
        frame.pack(fill='x', pady=(0, 15))
        
        Label(frame, text="操作控制:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        Button(frame, text="跳过当前组", command=self.skip_group,
               bg='#9E9E9E', fg='white', font=('Arial', 9)).pack(fill='x', pady=(2, 0))
        
        # 墙体填充按钮区域
        wall_fill_frame = Frame(frame)
        wall_fill_frame.pack(fill='x', pady=(2, 0))
        
        # 添加墙体自动填充按钮
        Button(wall_fill_frame, text="墙体自动填充", command=self.auto_fill_walls,
               bg='#2196F3', fg='white', font=('Arial', 9)).pack(side='left', fill='x', expand=True, padx=(0, 2))
        
        # 添加保存填充按钮
        Button(wall_fill_frame, text="保存填充", command=self.save_wall_fills,
               bg='#4CAF50', fg='white', font=('Arial', 9)).pack(side='right', fill='x', expand=True, padx=(2, 0))
    
    def _create_save_options(self, parent):
        """创建保存选项区域"""
        frame = Frame(parent)
        frame.pack(fill='x', pady=(0, 15))
        
        Label(frame, text="保存选项:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        # 保存选项复选框
        Checkbutton(frame, text="同时保存分类图片", variable=self.save_images_var,
                   font=('Arial', 9)).pack(anchor='w', pady=(5, 0))
        
        # 保存按钮
        Button(frame, text="手动保存数据集", command=self.save_dataset,
               bg='#4CAF50', fg='white', font=('Arial', 9)).pack(fill='x', pady=(5, 0))
        
        # 添加导出详细数据按钮
        Button(frame, text="导出组详细数据", command=self.export_group_details,
               bg='#FF9800', fg='white', font=('Arial', 9)).pack(fill='x', pady=(5, 0))
    
    def _create_group_list(self, parent):
        """创建实体组列表区域"""
        frame = Frame(parent)
        frame.pack(fill='both', expand=True, pady=(0, 0))
        
        Label(frame, text="实体组列表:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        # 添加状态说明
        help_text = "双击: 标注/重新分类 | 右键: 更多选项"
        Label(frame, text=help_text, font=('Arial', 8), fg='gray').pack(anchor='w')
        
        # 创建带滚动条的列表框
        list_frame = Frame(frame)
        list_frame.pack(fill='both', expand=True, pady=(5, 0))
        
        # 创建Treeview控件显示组信息
        columns = ('状态', '类型', '实体数')
        self.group_tree = ttk.Treeview(list_frame, columns=columns, show='tree headings', height=10)
        
        # 设置列标题
        self.group_tree.heading('#0', text='组ID', anchor='w')
        self.group_tree.heading('状态', text='状态', anchor='center')
        self.group_tree.heading('类型', text='类型', anchor='center')
        self.group_tree.heading('实体数', text='实体数', anchor='center')
        
        # 设置列宽
        self.group_tree.column('#0', width=80, minwidth=60)
        self.group_tree.column('状态', width=80, minwidth=60)
        self.group_tree.column('类型', width=80, minwidth=60)
        self.group_tree.column('实体数', width=60, minwidth=40)
        
        # 创建滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.group_tree.yview)
        self.group_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.group_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # 绑定双击事件和右键菜单
        self.group_tree.bind('<Double-1>', self.on_group_double_click)
        self.group_tree.bind('<Button-3>', self.on_group_right_click)  # 右键菜单
        
        # 配置状态颜色标签
        self.group_tree.tag_configure('unlabeled', foreground='red')
        self.group_tree.tag_configure('labeling', foreground='red')  # 标注中为红色
        self.group_tree.tag_configure('labeled', foreground='green')
        self.group_tree.tag_configure('auto_labeled', foreground='blue')
        self.group_tree.tag_configure('relabeled', foreground='purple')  # 重新分类的组用紫色
        self.group_tree.tag_configure('pending', foreground='brown')  # 待处理组用棕色
    
    def _create_visualization(self, parent):
        """创建可视化区域"""
        # 标题
        viz_title = Label(parent, text="CAD实体可视化 - 增强版（融合V2墙体填充）", font=('Arial', 12, 'bold'))
        viz_title.pack(pady=(0, 5))
        
        # 创建可视化器和画布
        try:
            self.visualizer = CADVisualizer()
            self.canvas = FigureCanvasTkAgg(self.visualizer.get_figure(), parent)
            self.canvas.get_tk_widget().pack(fill='both', expand=True)
        except Exception as e:
            print(f"可视化初始化失败: {e}")
            Label(parent, text=f"可视化初始化失败: {e}").pack(fill='both', expand=True) 
    
    def select_folder(self):
        """选择文件夹"""
        folder = filedialog.askdirectory(title="选择包含DXF文件的文件夹")
        if folder:
            self.folder_var.set(folder)
            self.status_var.set(f"已选择文件夹: {os.path.basename(folder)}")
    
    def start_processing(self):
        """开始处理"""
        folder = self.folder_var.get()
        if not folder:
            messagebox.showwarning("警告", "请先选择文件夹")
            return
        
        if not os.path.exists(folder):
            messagebox.showerror("错误", "选择的文件夹不存在")
            return
        
        # 创建处理器
        self.processor = EnhancedCADProcessor(self.visualizer, self.canvas)
        self.processor.set_callbacks(self.on_status_update, self.on_progress_update)
        
        # 更新按钮状态
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        
        # 在后台线程中处理
        threading.Thread(target=self.processor.process_folder, args=(folder,), daemon=True).start()
    
    def stop_processing(self):
        """停止处理"""
        if self.processor:
            self.processor.stop_processing()
        
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.status_var.set("处理已停止")
    
    def on_status_update(self, status_type, data):
        """状态更新回调"""
        if status_type == "info":
            self.status_var.set(data)
        
        elif status_type == "error":
            self.status_var.set(f"错误: {data}")
        
        elif status_type == "status":
            self.status_var.set(data)
        
        elif status_type == "file_start":
            file_index, total_files, filename = data
            self.status_var.set(f"正在处理文件 {file_index}/{total_files}: {filename}")
        
        elif status_type == "file_complete":
            file_index, total_files, filename = data
            self.status_var.set(f"文件 {file_index}/{total_files} 处理完成: {filename}")
        
        elif status_type == "file_error":
            file_index, total_files, filename = data
            self.status_var.set(f"文件 {file_index}/{total_files} 处理失败: {filename}")
        
        elif status_type == "auto_labeled":
            category, group_count, entity_count = data
            self.status_var.set(f"自动标注 {category}: {group_count}组, {entity_count}个实体")
        
        elif status_type == "manual_group":
            info = data
            self.status_var.set(f"手动标注 {info['index']}/{info['total']}: {info['entity_count']}个实体")
        
        elif status_type == "group_labeled":
            _, category_name, entity_count = data
            self.status_var.set(f"已标注为 {category_name}: {entity_count}个实体")
        
        elif status_type == "group_skipped":
            self.status_var.set(data)
        
        elif status_type == "manual_complete":
            self.status_var.set(data)
            # 重新启用开始按钮
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')
            
            # 更新可视化，清除当前处理组的高亮显示
            if self.processor and self.processor.visualizer and self.processor.canvas:
                try:
                    # 清空详细视图
                    self.processor.visualizer.ax_detail.clear()
                    self.processor.visualizer.ax_detail.text(0.5, 0.5, '所有分组已完成', 
                                                           ha='center', va='center', transform=self.processor.visualizer.ax_detail.transAxes,
                                                           fontproperties=self.processor.visualizer.chinese_font, fontsize=14)
                    self.processor.visualizer.ax_detail.set_title('CAD实体组预览 (已完成)', fontsize=12, fontproperties=self.processor.visualizer.chinese_font)
                    
                    # 更新全图概览，不显示当前处理组
                    self.processor.visualizer.visualize_overview(
                        self.processor.current_file_entities,
                        None,  # 不显示当前处理组
                        self.processor.auto_labeled_entities + self.processor.labeled_entities  # 已标注的实体
                    )
                    self.processor.visualizer.update_canvas(self.processor.canvas)
                except Exception as e:
                    print(f"完成时可视化更新失败: {e}")
        
        elif status_type == "completed":
            # 仅更新状态，不强制显示完成信息
            self.status_var.set(data)
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')
            
            # 让processor决定是否显示完成信息
            if self.processor:
                # 检查是否还有待处理组
                if not self.processor.pending_manual_groups:
                    self.processor._show_completion_message()
            
            # 更新组列表显示
            self.update_group_list()
            
            # 注意：移除自动跳转逻辑，因为完成状态不应该跳转到其他组
            # 只有在手动分组完成时才检查是否需要跳转到其他未标注组
        
        elif status_type == "stopped":
            self.status_var.set(data)
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')
            
            # 更新可视化，清除当前处理组的高亮显示
            if self.processor and self.processor.visualizer and self.processor.canvas:
                try:
                    # 清空详细视图
                    self.processor.visualizer.ax_detail.clear()
                    self.processor.visualizer.ax_detail.text(0.5, 0.5, '处理已停止', 
                                                           ha='center', va='center', transform=self.processor.visualizer.ax_detail.transAxes,
                                                           fontproperties=self.processor.visualizer.chinese_font, fontsize=14)
                    self.processor.visualizer.ax_detail.set_title('CAD实体组预览 (已停止)', fontsize=12, fontproperties=self.processor.visualizer.chinese_font)
                    
                    # 更新全图概览，不显示当前处理组
                    self.processor.visualizer.visualize_overview(
                        self.processor.current_file_entities,
                        None,  # 不显示当前处理组
                        self.processor.auto_labeled_entities + self.processor.labeled_entities  # 已标注的实体
                    )
                    self.processor.visualizer.update_canvas(self.processor.canvas)
                except Exception as e:
                    print(f"停止时可视化更新失败: {e}")
            
            # 更新组列表显示
            self.update_group_list()
        
        elif status_type == "update_group_list":
            # 更新组列表显示
            self.update_group_list()
        
        elif status_type == "force_update_group_list":
            # 强制更新组列表显示
            self.update_group_list()
        
        elif status_type == "group_relabeled":
            group_index, new_label = data
            category_name = self.processor.processor.category_mapping.get(new_label, new_label)
            self.status_var.set(f"组{group_index} 已重新分类为: {category_name}")
        
        elif status_type == "auto_jump":
            self.status_var.set(f"自动跳转: {data}")
        
        # 在相关状态更新时也更新组列表
        if status_type in ["manual_group", "group_labeled", "group_skipped", "manual_complete", "auto_labeled", "group_relabeled"]:
            self.update_group_list()
    
    def on_progress_update(self, current, total):
        """进度更新回调"""
        if total > 0:
            progress = (current / total) * 100
            self.progress_bar['value'] = progress
            
            # 更新统计信息
            dataset_count = len(self.processor.dataset) if self.processor else 0
            self.stats_var.set(f"文件: {current}/{total} | 样本: {dataset_count}")
    
    def select_category(self, category):
        """选择类别"""
        if self.processor and self.processor.manual_grouping_mode:
            success = self.processor.label_current_group(category)
            if success:
                self.current_label = category
            else:
                messagebox.showwarning("警告", "当前没有可标注的组")
        else:
            messagebox.showinfo("提示", "请先开始处理文件")
    
    def skip_group(self):
        """跳过当前组"""
        if self.processor and self.processor.manual_grouping_mode:
            success = self.processor.skip_current_group()
            if not success:
                messagebox.showwarning("警告", "当前没有可跳过的组")
        else:
            messagebox.showinfo("提示", "请先开始处理文件")
    
    def auto_fill_walls(self):
        """墙体自动填充功能（融合V2版本）"""
        if not self.processor or not self.processor.current_file_entities:
            messagebox.showwarning("警告", "请先加载CAD文件")
            return
        
        # 调用处理器的V2墙体填充功能
        self.processor.auto_fill_walls()
    
    def save_wall_fills(self):
        """保存墙体填充到全图概览（融合V2版本）"""
        if not self.processor:
            messagebox.showwarning("警告", "请先开始处理文件")
            return
        
        # 调用处理器的保存填充功能
        self.processor.save_wall_fills() 
    
    def save_dataset(self):
        """保存数据集"""
        if not self.processor or not self.processor.dataset:
            messagebox.showwarning("警告", "没有可保存的数据")
            return
        
        # 选择保存目录
        output_dir = filedialog.askdirectory(title="选择保存目录")
        if not output_dir:
            return
        
        include_images = self.save_images_var.get()
        
        try:
            success, message = self.processor.save_dataset(include_images, output_dir)
            if success:
                messagebox.showinfo("成功", message)
                self.status_var.set("数据集保存成功")
            else:
                messagebox.showerror("错误", message)
                self.status_var.set(f"保存失败: {message}")
        except Exception as e:
            error_msg = f"保存失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)
    
    def setup_shortcuts(self):
        """设置快捷键"""
        self.root.bind('<Control-s>', lambda e: self.save_dataset())
        self.root.bind('<Escape>', lambda e: self.stop_processing())
        
        # 数字键选择类别
        categories = ['wall', 'door_window', 'railing', 'furniture', 'bed', 'sofa', 'cabinet', 'dining_table', 'appliance', 'stair', 'elevator', 'dimension', 'room_label', 'column', 'other']
        for i, category in enumerate(categories):
            self.root.bind(str(i+1), lambda e, cat=category: self.select_category(cat))
        
        # 空格键跳过
        self.root.bind('<space>', lambda e: self.skip_group())
    
    def on_group_double_click(self, event):
        """处理组列表双击事件"""
        selection = self.group_tree.selection()
        if selection:
            item = selection[0]
            group_id = self.group_tree.item(item, 'text')
            values = self.group_tree.item(item, 'values')
            status = values[0] if values else ""
            
            group_index = int(group_id.replace('组', ''))
            
            if status in ['已标注', '自动标注', '重新标注']:
                # 已标注的组，进入重新分类模式
                self.start_relabel_mode(group_index)
            elif status == '未标注':
                # 未标注的组，正常跳转
                if self.processor and hasattr(self.processor, 'jump_to_group'):
                    self.processor.jump_to_group(group_index)
            elif status == '标注中':
                # 当前正在标注的组，也可以重新分类
                self.start_relabel_mode(group_index)
    
    def on_group_right_click(self, event):
        """处理组列表右键菜单"""
        # 选择右键点击的项目
        item = self.group_tree.identify_row(event.y)
        if item:
            self.group_tree.selection_set(item)
            
            # 获取组信息
            group_id = self.group_tree.item(item, 'text')
            values = self.group_tree.item(item, 'values')
            status = values[0] if values else ""
            
            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)
            
            group_index = int(group_id.replace('组', ''))
            
            if status in ['已标注', '自动标注', '标注中', '重新标注']:
                context_menu.add_command(label="重新分类", 
                                       command=lambda: self.start_relabel_mode(group_index))
            
            if status == '未标注':
                context_menu.add_command(label="开始标注", 
                                       command=lambda: self.processor.jump_to_group(group_index) if self.processor else None)
            
            context_menu.add_separator()
            context_menu.add_command(label="查看详情", 
                                   command=lambda: self.show_group_details(group_index))
            
            # 显示菜单
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
    
    def update_group_list(self):
        """更新实体组列表显示"""
        if not self.processor:
            return
        
        # 清空现有列表
        for item in self.group_tree.get_children():
            self.group_tree.delete(item)
        
        # 获取所有组的信息
        groups_info = self.processor.get_groups_info()
        
        for i, group_info in enumerate(groups_info):
            group_id = f"组{i+1}"
            status = group_info['status']
            group_type = group_info.get('type', '')
            entity_count = group_info['entity_count']
            
            # 根据状态选择标签和显示文本
            if status == 'auto_labeled':
                status_text = '自动标注'
                tag = 'auto_labeled'
                type_text = self.processor.processor.category_mapping.get(group_type, group_type)
            elif status == 'labeled':
                status_text = '已标注'
                tag = 'labeled'
                type_text = self.processor.processor.category_mapping.get(group_type, group_type)
            elif status == 'relabeled':
                status_text = '重新标注'
                tag = 'relabeled'
                type_text = self.processor.processor.category_mapping.get(group_type, group_type)
            elif status == 'pending':  # 新增待处理状态
                status_text = '待处理'
                tag = 'pending'
                type_text = '待标注'
            elif status == 'labeling': # Existing logic, but now correctly fed by _update_groups_info
                status_text = '标注中'
                tag = 'labeling'
                type_text = '待标注'
            else:
                status_text = '未标注'
                tag = 'unlabeled'
                type_text = '待标注'
            
            # 插入到列表中
            self.group_tree.insert('', 'end', text=group_id, 
                                                                  values=(status_text, type_text, entity_count),
                                 tags=(tag,))
    
    def start_relabel_mode(self, group_index):
        """开始重新分类模式"""
        if not self.processor:
            messagebox.showwarning("警告", "请先开始处理文件")
            return
        
        # 检查是否有可重新分类的组
        if not self.processor.can_relabel_group(group_index):
            messagebox.showwarning("警告", "该组无法重新分类")
            return
        
        # 进入重新分类模式
        success = self.processor.start_relabel_group(group_index)
        if success:
            # 更新状态显示
            self.status_var.set(f"正在重新分类第 {group_index} 组")
            
            # 创建分类选择对话框
            self.show_relabel_dialog(group_index)
        else:
            messagebox.showerror("错误", "无法进入重新分类模式")
    
    def show_relabel_dialog(self, group_index):
        """显示重新分类对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"重新分类 - 组{group_index}")
        dialog.geometry("400x500")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        # 获取组信息
        group_info = self.processor.get_group_info(group_index)
        
        # 标题
        title_label = Label(dialog, text=f"重新分类 - 组{group_index}", 
                           font=('Arial', 12, 'bold'))
        title_label.pack(pady=10)
        
        # 组信息
        info_frame = Frame(dialog)
        info_frame.pack(fill='x', padx=20, pady=5)
        
        Label(info_frame, text=f"实体数量: {group_info['entity_count']}", 
              font=('Arial', 10)).pack(anchor='w')
        Label(info_frame, text=f"当前分类: {group_info.get('current_type', '未知')}", 
              font=('Arial', 10)).pack(anchor='w')
        
        # 分类选择区域
        category_frame = Frame(dialog)
        category_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        Label(category_frame, text="选择新的分类:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        # 分类按钮
        categories = [
            ('wall', '墙体', '#FFA500'),
            ('door_window', '门窗', '#42A5F5'),
            ('railing', '栏杆', '#29B6F6'),
            ('furniture', '家具', '#4DB6AC'),
            ('bed', '床', '#80CBC4'),
            ('sofa', '沙发', '#26A69A'),
            ('cabinet', '柜子', '#00796B'),
            ('dining_table', '餐桌', '#B2DFDB'),
            ('appliance', '家电', '#607D8B'),
            ('stair', '楼梯', '#F9A825'),
            ('elevator', '电梯', '#F57F17'),
            ('dimension', '标注', '#FFCA28'),
            ('room_label', '房间标注', '#EF5350'),
            ('column', '柱子', '#5D4037'),
            ('other', '其他', '#BDBDBD')
        ]
        
        button_frame = Frame(category_frame)
        button_frame.pack(fill='x', pady=10)
        
        for i, (key, name, color) in enumerate(categories):
            row = i // 3
            col = i % 3
            
            btn = Button(button_frame, text=name, 
                        command=lambda k=key: self.apply_relabel(group_index, k, dialog),
                        bg=color, fg='white', font=('Arial', 9), width=10)
            btn.grid(row=row, column=col, sticky='ew', padx=2, pady=2)
        
        # 配置列权重
        button_frame.grid_columnconfigure(0, weight=1)
        button_frame.grid_columnconfigure(1, weight=1)
        button_frame.grid_columnconfigure(2, weight=1)
        
        # 按钮区域
        btn_frame = Frame(dialog)
        btn_frame.pack(fill='x', padx=20, pady=10)
        
        Button(btn_frame, text="取消", command=dialog.destroy,
               bg='#9E9E9E', fg='white', font=('Arial', 9)).pack(side='right', padx=(5, 0))
    
    def apply_relabel(self, group_index, new_label, dialog):
        """应用重新分类"""
        if not self.processor:
            return
        
        # 执行重新分类
        success = self.processor.relabel_group(group_index, new_label)
        
        if success:
            category_name = self.processor.processor.category_mapping.get(new_label, new_label)
            self.status_var.set(f"组{group_index} 已重新分类为: {category_name}")
            
            # 关闭对话框
            dialog.destroy()
            
            # 检查是否还有未完成的分类
            self.check_and_continue_labeling()
            
            # 更新组列表
            self.update_group_list()
            
            # 更新可视化显示
            if self.processor and self.processor.visualizer and self.processor.canvas:
                try:
                    self.processor.visualizer.visualize_overview(
                        self.processor.current_file_entities,
                        [],  # 没有当前处理组
                        self.processor.auto_labeled_entities + self.processor.labeled_entities
                    )
                    self.processor.visualizer.update_canvas(self.processor.canvas)
                except Exception as e:
                    print(f"重新分类后可视化更新失败: {e}")
        else:
            messagebox.showerror("错误", "重新分类失败")
    
    def check_and_continue_labeling(self):
        """检查并继续标注未完成的组"""
        if not self.processor:
            return
        
        # 如果还有未完成的标注，自动跳转到下一个
        if self.processor.has_unlabeled_groups():
            next_group = self.processor.get_next_unlabeled_group()
            if next_group is not None:
                self.processor.jump_to_group(next_group)
                self.status_var.set(f"自动跳转到下一个未标注组: 组{next_group}")
        else:
            self.status_var.set("所有组已标注完成")
    
    def show_group_details(self, group_index):
        """显示组详情"""
        if not self.processor:
            return
        
        group_info = self.processor.get_group_info(group_index)
        
        details = f"""组{group_index} 详细信息:
        
实体数量: {group_info['entity_count']}
当前状态: {group_info['status']}
分类类型: {group_info.get('current_type', '未知')}
图层信息: {', '.join(group_info.get('layers', []))}
边界框: {group_info.get('bbox_info', '未知')}
        """
        
        messagebox.showinfo(f"组{group_index}详情", details)
    
    def export_group_details(self):
        """导出组详细数据"""
        if not self.processor or not self.processor.all_groups:
            messagebox.showwarning("警告", "没有可导出的组数据")
            return
        
        # 创建输入对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("导出组详细数据")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        # 标题
        title_label = Label(dialog, text="选择要导出的组", font=('Arial', 12, 'bold'))
        title_label.pack(pady=10)
        
        # 说明
        info_label = Label(dialog, text="输入组索引（如：1,3,4 或留空导出所有组）", font=('Arial', 10))
        info_label.pack(pady=5)
        
        # 输入框
        entry_var = StringVar()
        entry = Entry(dialog, textvariable=entry_var, font=('Arial', 10), width=30)
        entry.pack(pady=10)
        
        # 按钮框架
        btn_frame = Frame(dialog)
        btn_frame.pack(pady=10)
        
        def do_export():
            input_text = entry_var.get().strip()
            
            if not input_text:
                # 导出所有组
                success, message = self.processor.export_group_details()
            else:
                try:
                    # 解析输入的组索引
                    group_indices = [int(x.strip()) for x in input_text.split(',')]
                    success, message = self.processor.export_specific_groups(group_indices)
                except ValueError:
                    messagebox.showerror("错误", "请输入有效的组索引，如：1,3,4")
                    return
            
            if success:
                messagebox.showinfo("成功", message)
                self.status_var.set("组详细数据导出成功")
            else:
                messagebox.showerror("错误", message)
                self.status_var.set(f"导出失败: {message}")
            
            dialog.destroy()
        
        Button(btn_frame, text="导出", command=do_export,
               bg='#4CAF50', fg='white', font=('Arial', 9)).pack(side='left', padx=(0, 5))
        
        Button(btn_frame, text="取消", command=dialog.destroy,
               bg='#9E9E9E', fg='white', font=('Arial', 9)).pack(side='left')

def main():
    """主程序入口"""
    try:
        print("正在启动CAD分类标注工具 - 增强版（融合V2墙体填充）...")
        
        # 设置matplotlib后端
        import matplotlib
        matplotlib.use('TkAgg')
        
        root = tk.Tk()
        app = EnhancedCADApp(root)
        
        print("程序启动成功，开始主循环...")
        root.mainloop()
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
