
# 处理器监控代码 - 插入到类的初始化方法中
def _init_processor_monitor(self):
    """初始化处理器监控"""
    self._processor_creation_count = 0
    self._processor_reset_log = []
    self._last_processor_check = time.time()

def _log_processor_event(self, event_type, location, details=""):
    """记录处理器事件"""
    import time
    timestamp = time.strftime("%H:%M:%S", time.localtime())
    
    if event_type == "reset":
        self._processor_creation_count += 1
    
    event = {
        'timestamp': timestamp,
        'type': event_type,
        'location': location,
        'details': details,
        'count': self._processor_creation_count
    }
    
    self._processor_reset_log.append(event)
    
    # 打印详细日志
    print(f"🔍 [{timestamp}] 处理器事件: {event_type}")
    print(f"   位置: {location}")
    print(f"   详情: {details}")
    print(f"   重置次数: {self._processor_creation_count}")
    
    # 如果重置次数过多，打印调用栈
    if self._processor_creation_count > 3:
        print(f"   ⚠️ 处理器重置次数过多，打印调用栈:")
        import traceback
        traceback.print_stack()

def _check_processor_stability(self):
    """检查处理器稳定性"""
    current_time = time.time()
    
    if hasattr(self, '_last_processor_check'):
        time_diff = current_time - self._last_processor_check
        
        if not self.processor:
            self._log_processor_event("missing", "stability_check", 
                                    f"处理器在{time_diff:.2f}秒后丢失")
        elif time_diff > 1.0:  # 超过1秒检查一次
            self._log_processor_event("check", "stability_check", 
                                    f"处理器状态正常，间隔{time_diff:.2f}秒")
    
    self._last_processor_check = current_time

# 修改后的处理器检查代码 - 替换原有的检查
def _safe_processor_check(self, location):
    """安全的处理器检查"""
    if not hasattr(self, '_processor_creation_count'):
        self._init_processor_monitor()
    
    self._check_processor_stability()
    
    if not self.processor:
        self._log_processor_event("reset", location, "处理器不存在，需要创建")
        
        # 分析可能的原因
        possible_causes = []
        
        if hasattr(self, 'current_file') and not self.current_file:
            possible_causes.append("当前文件为空")
        
        if hasattr(self, 'file_cache') and not self.file_cache:
            possible_causes.append("文件缓存为空")
        
        if len(self._processor_reset_log) > 1:
            last_event = self._processor_reset_log[-2]
            time_since_last = time.time() - time.mktime(time.strptime(last_event['timestamp'], "%H:%M:%S"))
            if time_since_last < 5:
                possible_causes.append(f"距离上次重置仅{time_since_last:.1f}秒")
        
        if possible_causes:
            print(f"   🔍 可能原因: {', '.join(possible_causes)}")
        
        return True  # 需要创建处理器
    
    return False  # 处理器存在
