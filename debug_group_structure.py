#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试组数据结构问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_enhanced import EnhancedCADProcessor

def debug_group_structure():
    """调试组数据结构"""
    print("🔍 调试组数据结构问题")
    print("="*60)
    
    # 创建处理器
    processor = EnhancedCADProcessor()
    
    # 模拟从实际文件加载的数据结构
    # 这些数据结构可能来自缓存或文件处理
    
    # 1. 正确的组结构（列表格式）
    correct_group = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': '墙体', 'auto_labeled': True},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'label': '墙体', 'auto_labeled': True}
    ]
    
    # 2. 错误的组结构（包含字符串）
    incorrect_group = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': '墙体', 'auto_labeled': True},
        "string_entity",  # 这会导致错误
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'label': '墙体', 'auto_labeled': True}
    ]
    
    # 3. 字典格式的组
    dict_group = {
        'entities': [
            {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(50, -10), (50, 10)], 'label': '门窗', 'auto_labeled': True},
            {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(50, 10), (50, 30)], 'label': '门窗', 'auto_labeled': True}
        ],
        'label': '门窗',
        'group_type': 'door_window',
        'layer': 'A-WINDOW',
        'status': 'auto_labeled',
        'confidence': 0.8
    }
    
    test_groups = [correct_group, incorrect_group, dict_group]
    
    print(f"测试组数据:")
    for i, group in enumerate(test_groups):
        print(f"  组 {i+1}: {type(group)}")
        if isinstance(group, list):
            print(f"    列表长度: {len(group)}")
            for j, item in enumerate(group):
                print(f"      项目 {j+1}: {type(item)}")
        elif isinstance(group, dict):
            print(f"    字典键: {list(group.keys())}")
            if 'entities' in group:
                print(f"    实体数: {len(group['entities'])}")
    
    # 测试组信息更新
    print(f"\n🔧 测试组信息更新:")
    processor.all_groups = test_groups
    
    try:
        processor._update_groups_info()
        print(f"  ✅ 组信息更新成功: {len(processor.groups_info)} 个组")
        
        for i, info in enumerate(processor.groups_info):
            print(f"    组 {i+1}: 状态={info.get('status')}, 标签={info.get('label')}, 类型={info.get('group_type')}")
            
    except Exception as e:
        print(f"  ❌ 组信息更新失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试可视化器
    print(f"\n🔧 测试可视化器:")
    try:
        from cad_visualizer import CADVisualizer
        visualizer = CADVisualizer()
        
        # 测试正确的组
        print(f"  测试正确的组:")
        visualizer.visualize_entity_group(correct_group, {})
        print(f"    ✅ 正确组可视化成功")
        
        # 测试错误的组
        print(f"  测试错误的组:")
        visualizer.visualize_entity_group(incorrect_group, {})
        print(f"    ✅ 错误组可视化成功（应该跳过字符串）")
        
        # 测试字典格式的组
        print(f"  测试字典格式的组:")
        entities = dict_group['entities']
        visualizer.visualize_entity_group(entities, {})
        print(f"    ✅ 字典组可视化成功")
        
    except Exception as e:
        print(f"  ❌ 可视化器测试失败: {e}")
        import traceback
        traceback.print_exc()

def debug_real_data_structure():
    """调试真实数据结构"""
    print(f"\n🔍 调试真实数据结构")
    print("="*60)
    
    # 模拟从缓存加载的真实数据结构
    # 这些可能是导致问题的实际数据格式
    
    # 从日志中可以看到，可能存在这样的数据结构问题：
    # 1. 组中包含字符串而不是实体字典
    # 2. 实体数据类型不一致
    # 3. 组格式混合（有些是列表，有些是字典）
    
    print("模拟可能的问题数据结构:")
    
    # 问题1: 组中包含字符串
    problematic_group1 = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
        "entity_id_string",  # 这可能是实体ID字符串
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)]}
    ]
    
    # 问题2: 实体属性缺失
    problematic_group2 = [
        {'type': 'LINE', 'layer': 'A-WALL'},  # 缺少points
        {'layer': 'A-WALL', 'points': [(100, 0), (100, 100)]},  # 缺少type
        {}  # 空字典
    ]
    
    # 问题3: 混合数据类型
    problematic_group3 = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
        123,  # 数字
        None,  # None值
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)]}
    ]
    
    test_groups = [problematic_group1, problematic_group2, problematic_group3]
    
    print(f"测试问题数据结构:")
    for i, group in enumerate(test_groups):
        print(f"  问题组 {i+1}:")
        for j, item in enumerate(group):
            print(f"    项目 {j+1}: {type(item)} - {item}")
    
    # 测试修复后的可视化器
    print(f"\n🔧 测试修复后的可视化器:")
    try:
        from cad_visualizer import CADVisualizer
        visualizer = CADVisualizer()
        
        for i, group in enumerate(test_groups):
            print(f"  测试问题组 {i+1}:")
            try:
                visualizer.visualize_entity_group(group, {})
                print(f"    ✅ 问题组 {i+1} 可视化成功")
            except Exception as e:
                print(f"    ❌ 问题组 {i+1} 可视化失败: {e}")
        
    except Exception as e:
        print(f"  ❌ 可视化器初始化失败: {e}")

def suggest_fixes():
    """建议修复方案"""
    print(f"\n💡 建议修复方案")
    print("="*60)
    
    print("1. 数据清理方案:")
    print("   - 在组处理前过滤非字典实体")
    print("   - 验证实体必需属性（type, layer等）")
    print("   - 统一组数据格式")
    
    print("\n2. 可视化器增强:")
    print("   - 添加实体类型检查")
    print("   - 使用安全的属性访问方法")
    print("   - 跳过无效实体并记录日志")
    
    print("\n3. 组信息更新优化:")
    print("   - 确保组类型正确识别")
    print("   - 修复自动标注状态检测")
    print("   - 改进组状态统计")
    
    # 提供具体的修复代码示例
    print("\n4. 具体修复代码:")
    print("""
    # 清理组数据的函数
    def clean_group_data(group):
        if isinstance(group, dict):
            entities = group.get('entities', [])
        elif isinstance(group, list):
            entities = group
        else:
            return []
        
        # 过滤并清理实体
        cleaned_entities = []
        for entity in entities:
            if isinstance(entity, dict) and 'type' in entity and 'layer' in entity:
                cleaned_entities.append(entity)
            else:
                print(f"跳过无效实体: {type(entity)} - {entity}")
        
        return cleaned_entities
    """)

def main():
    """主测试函数"""
    print("🚀 开始调试组数据结构问题")
    
    try:
        # 1. 基本数据结构测试
        debug_group_structure()
        
        # 2. 真实问题数据测试
        debug_real_data_structure()
        
        # 3. 修复建议
        suggest_fixes()
        
        print(f"\n✅ 调试完成")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
