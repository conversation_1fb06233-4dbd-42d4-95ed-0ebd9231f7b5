#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动测试 - 验证主程序能否正常启动
"""

import sys
import os
import tkinter as tk
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_main_startup():
    """测试主程序启动"""
    print("🚀 测试主程序启动")
    print("="*50)
    
    try:
        # 创建根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        print("1. 尝试导入主程序...")
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        print("  ✅ 主程序导入成功")
        
        print("2. 尝试创建应用实例...")
        app = EnhancedCADAppV2(root)
        print("  ✅ 应用实例创建成功")
        
        print("3. 检查追踪功能初始化...")
        if hasattr(app, '_processor_reset_count'):
            print(f"  ✅ 追踪计数器: {app._processor_reset_count}")
        else:
            print("  ❌ 追踪计数器未初始化")
        
        if hasattr(app, '_track_processor_reset'):
            print("  ✅ 追踪方法存在")
        else:
            print("  ❌ 追踪方法不存在")
        
        print("4. 测试追踪方法调用...")
        if hasattr(app, '_track_processor_reset'):
            app._track_processor_reset("startup_test")
            print("  ✅ 追踪方法调用成功")
        else:
            print("  ❌ 追踪方法调用失败")
        
        # 清理
        root.destroy()
        
        print("\n🎉 启动测试完成 - 所有功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_main_startup()
