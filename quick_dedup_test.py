#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速去重测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    print("🧪 快速去重测试...")
    
    try:
        from cad_data_processor import CADDataProcessor
        processor = CADDataProcessor()
        
        # 测试实体
        entities = [
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 342727.5,
                'start_y': 25051.0,
                'end_x': 342727.5,
                'end_y': 15031.0
            },
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 342727.5,
                'start_y': 15031.0,
                'end_x': 339439.5,
                'end_y': 15031.0
            },
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 342727.5,
                'start_y': 25051.0,
                'end_x': 342727.5,
                'end_y': 15031.0  # 重复
            }
        ]
        
        print(f"输入: {len(entities)} 个实体")
        
        # 测试哈希
        for i, entity in enumerate(entities):
            hash_val = processor._get_entity_hash(entity)
            print(f"实体{i+1}: {hash_val}")
        
        # 测试去重
        unique = processor._remove_duplicate_entities(entities)
        print(f"输出: {len(unique)} 个实体")
        
        if len(unique) == 2:
            print("✅ 修复成功！")
        else:
            print("❌ 修复失败")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_test()
