#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全图概览数据缓存系统
为全图概览显示提供专门的数据缓存，支持多文件、数据变化检测、独立存储
"""

import copy
import hashlib
import json
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field
from enum import Enum
import threading
import time

class DataType(Enum):
    """数据类型枚举"""
    DXF_ENTITIES = "dxf_entities"
    GROUP_DATA = "group_data"
    WALL_FILL = "wall_fill"
    ROOM_FILL = "room_fill"
    FURNITURE_FILL = "furniture_fill"
    LAYER_SETTINGS = "layer_settings"
    COLOR_SCHEME = "color_scheme"
    VISIBILITY_SETTINGS = "visibility_settings"

@dataclass
class CacheEntry:
    """缓存条目"""
    data: Any
    timestamp: float
    data_hash: str
    version: int = 1
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_expired(self, max_age: float = 3600) -> bool:
        """检查缓存是否过期"""
        return time.time() - self.timestamp > max_age

@dataclass
class FileDataCache:
    """单个文件的数据缓存"""
    file_path: str
    entries: Dict[DataType, CacheEntry] = field(default_factory=dict)
    last_modified: float = field(default_factory=time.time)
    
    def get_data_hash(self) -> str:
        """获取整个文件数据的哈希值"""
        combined_data = {}
        for data_type, entry in self.entries.items():
            combined_data[data_type.value] = entry.data_hash
        return hashlib.md5(json.dumps(combined_data, sort_keys=True).encode()).hexdigest()

class OverviewDataCache:
    """全图概览数据缓存系统"""
    
    def __init__(self):
        self.file_caches: Dict[str, FileDataCache] = {}
        self.current_file: Optional[str] = None
        self.change_listeners: Dict[DataType, List[callable]] = {}
        self.lock = threading.RLock()
        self.debug_mode = True
        
        # 数据变化追踪
        self.pending_changes: Set[str] = set()  # 待处理的变化文件
        self.change_batch_timer = None
        self.batch_delay = 0.1  # 100ms批处理延迟
        
        if self.debug_mode:
            print("🗄️ 全图概览数据缓存系统初始化完成")
    
    def set_current_file(self, file_path: str):
        """设置当前文件"""
        with self.lock:
            self.current_file = file_path
            if file_path not in self.file_caches:
                self.file_caches[file_path] = FileDataCache(file_path)
                if self.debug_mode:
                    print(f"📁 为文件创建新缓存: {file_path}")
    
    def store_data(self, data_type: DataType, data: Any, file_path: str = None, 
                   metadata: Dict[str, Any] = None) -> bool:
        """存储数据到缓存"""
        if file_path is None:
            file_path = self.current_file
        
        if not file_path:
            if self.debug_mode:
                print("⚠️ 无法存储数据：未指定文件路径")
            return False
        
        with self.lock:
            # 确保文件缓存存在
            if file_path not in self.file_caches:
                self.file_caches[file_path] = FileDataCache(file_path)
            
            file_cache = self.file_caches[file_path]
            
            # 计算数据哈希
            data_str = self._serialize_data(data)
            data_hash = hashlib.md5(data_str.encode()).hexdigest()
            
            # 检查数据是否发生变化
            has_changed = True
            if data_type in file_cache.entries:
                old_entry = file_cache.entries[data_type]
                has_changed = old_entry.data_hash != data_hash
            
            if has_changed:
                # 创建新的缓存条目
                new_version = 1
                if data_type in file_cache.entries:
                    new_version = file_cache.entries[data_type].version + 1
                
                entry = CacheEntry(
                    data=copy.deepcopy(data),
                    timestamp=time.time(),
                    data_hash=data_hash,
                    version=new_version,
                    metadata=metadata or {}
                )
                
                file_cache.entries[data_type] = entry
                file_cache.last_modified = time.time()
                
                # 标记文件有变化
                self.pending_changes.add(file_path)
                
                # 启动批处理定时器
                self._schedule_change_notification()
                
                if self.debug_mode:
                    print(f"💾 数据已更新: {data_type.value} (文件: {file_path}, 版本: {new_version})")
                
                return True
            else:
                if self.debug_mode:
                    print(f"📋 数据无变化: {data_type.value} (文件: {file_path})")
                return False
    
    def get_data(self, data_type: DataType, file_path: str = None) -> Optional[Any]:
        """从缓存获取数据"""
        if file_path is None:
            file_path = self.current_file
        
        if not file_path or file_path not in self.file_caches:
            return None
        
        with self.lock:
            file_cache = self.file_caches[file_path]
            if data_type in file_cache.entries:
                entry = file_cache.entries[data_type]
                if not entry.is_expired():
                    return copy.deepcopy(entry.data)
                else:
                    if self.debug_mode:
                        print(f"⏰ 缓存已过期: {data_type.value}")
                    # 清除过期缓存
                    del file_cache.entries[data_type]
        
        return None
    
    def has_data_changed(self, data_type: DataType, new_data: Any, file_path: str = None) -> bool:
        """检查数据是否发生变化"""
        if file_path is None:
            file_path = self.current_file
        
        if not file_path or file_path not in self.file_caches:
            return True  # 没有缓存，认为是变化
        
        with self.lock:
            file_cache = self.file_caches[file_path]
            if data_type not in file_cache.entries:
                return True  # 没有该类型数据，认为是变化
            
            # 计算新数据的哈希
            new_data_str = self._serialize_data(new_data)
            new_hash = hashlib.md5(new_data_str.encode()).hexdigest()
            
            old_entry = file_cache.entries[data_type]
            return old_entry.data_hash != new_hash
    
    def get_file_data_summary(self, file_path: str = None) -> Dict[str, Any]:
        """获取文件数据摘要"""
        if file_path is None:
            file_path = self.current_file
        
        if not file_path or file_path not in self.file_caches:
            return {}
        
        with self.lock:
            file_cache = self.file_caches[file_path]
            summary = {
                'file_path': file_path,
                'last_modified': file_cache.last_modified,
                'data_hash': file_cache.get_data_hash(),
                'data_types': {}
            }
            
            for data_type, entry in file_cache.entries.items():
                summary['data_types'][data_type.value] = {
                    'version': entry.version,
                    'timestamp': entry.timestamp,
                    'data_hash': entry.data_hash,
                    'metadata': entry.metadata
                }
            
            return summary
    
    def clear_file_cache(self, file_path: str):
        """清除指定文件的缓存"""
        with self.lock:
            if file_path in self.file_caches:
                del self.file_caches[file_path]
                self.pending_changes.discard(file_path)
                if self.debug_mode:
                    print(f"🗑️ 已清除文件缓存: {file_path}")
    
    def clear_all_cache(self):
        """清除所有缓存"""
        with self.lock:
            self.file_caches.clear()
            self.pending_changes.clear()
            if self.debug_mode:
                print("🗑️ 已清除所有缓存")
    
    def register_change_listener(self, data_type: DataType, callback: callable):
        """注册数据变化监听器"""
        if data_type not in self.change_listeners:
            self.change_listeners[data_type] = []
        self.change_listeners[data_type].append(callback)
    
    def _serialize_data(self, data: Any) -> str:
        """序列化数据用于哈希计算"""
        try:
            if hasattr(data, '__dict__'):
                # 对象类型
                return json.dumps(data.__dict__, sort_keys=True, default=str)
            elif isinstance(data, (list, tuple)):
                # 列表或元组
                return json.dumps([self._serialize_data(item) for item in data], sort_keys=True)
            elif isinstance(data, dict):
                # 字典
                return json.dumps(data, sort_keys=True, default=str)
            else:
                # 基本类型
                return json.dumps(data, sort_keys=True, default=str)
        except Exception as e:
            # 如果序列化失败，使用字符串表示
            return str(data)
    
    def _schedule_change_notification(self):
        """安排变化通知"""
        if self.change_batch_timer:
            self.change_batch_timer.cancel()
        
        self.change_batch_timer = threading.Timer(
            self.batch_delay, 
            self._notify_changes
        )
        self.change_batch_timer.start()
    
    def _notify_changes(self):
        """通知数据变化"""
        with self.lock:
            if not self.pending_changes:
                return
            
            changed_files = list(self.pending_changes)
            self.pending_changes.clear()
            
            if self.debug_mode:
                print(f"📢 通知数据变化: {len(changed_files)} 个文件")
            
            # 这里可以触发显示更新等操作
            for file_path in changed_files:
                self._trigger_file_change_callbacks(file_path)
    
    def _trigger_file_change_callbacks(self, file_path: str):
        """触发文件变化回调"""
        if file_path not in self.file_caches:
            return
        
        file_cache = self.file_caches[file_path]
        
        # 为每种数据类型触发相应的监听器
        for data_type, entry in file_cache.entries.items():
            if data_type in self.change_listeners:
                for callback in self.change_listeners[data_type]:
                    try:
                        callback(file_path, data_type, entry.data)
                    except Exception as e:
                        if self.debug_mode:
                            print(f"❌ 变化回调执行失败: {e}")

# 全局缓存实例
overview_cache = OverviewDataCache()
