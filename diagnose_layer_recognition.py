#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断图层识别问题
检查为什么只识别到门窗图层，其他图层（墙体、家具、房间等）没有被正确识别和分组
"""

import os
import sys
from collections import defaultdict

def diagnose_layer_recognition(file_path):
    """诊断图层识别问题"""
    print(f"🔍 诊断文件: {os.path.basename(file_path)}")
    print("="*60)
    
    try:
        # 1. 导入必要的模块
        from cad_data_processor import CADDataProcessor
        from main_enhanced import EnhancedCADProcessor
        
        print("✅ 模块导入成功")
        
        # 2. 创建CAD数据处理器
        cad_processor = CADDataProcessor()
        print("✅ CAD数据处理器创建成功")
        
        # 3. 加载DXF文件
        print(f"\n📂 加载DXF文件...")
        entities = cad_processor.load_dxf_file(file_path)
        
        if not entities:
            print("❌ 无法加载文件或文件为空")
            return False
        
        print(f"✅ 成功加载 {len(entities)} 个实体")
        
        # 4. 分析图层分布
        print(f"\n📊 图层分布分析:")
        layer_stats = defaultdict(int)
        layer_types = defaultdict(list)
        
        for entity in entities:
            layer = entity.get('layer', 'UNKNOWN')
            entity_type = entity.get('type', 'UNKNOWN')
            layer_stats[layer] += 1
            if entity_type not in layer_types[layer]:
                layer_types[layer].append(entity_type)
        
        # 按实体数量排序显示
        sorted_layers = sorted(layer_stats.items(), key=lambda x: x[1], reverse=True)
        
        print(f"  总图层数: {len(sorted_layers)}")
        print(f"  图层详情:")
        for layer, count in sorted_layers:
            types_str = ', '.join(layer_types[layer][:5])  # 只显示前5种类型
            if len(layer_types[layer]) > 5:
                types_str += f" (+{len(layer_types[layer])-5}种)"
            print(f"    {layer}: {count}个实体 ({types_str})")
        
        # 5. 测试特殊图层识别
        print(f"\n🔍 特殊图层识别测试:")
        
        # 墙体图层识别
        wall_layers = cad_processor._detect_special_layers(
            entities, cad_processor.wall_layer_patterns, debug=False, layer_type="墙体"
        )
        print(f"  墙体图层识别结果: {wall_layers}")
        if wall_layers:
            wall_entities = [e for e in entities if e['layer'] in wall_layers]
            print(f"    墙体实体数量: {len(wall_entities)}")
        
        # 门窗图层识别
        door_window_layers = cad_processor._detect_special_layers(
            entities, cad_processor.door_window_layer_patterns, debug=False, layer_type="门窗"
        )
        print(f"  门窗图层识别结果: {door_window_layers}")
        if door_window_layers:
            door_window_entities = [e for e in entities if e['layer'] in door_window_layers]
            print(f"    门窗实体数量: {len(door_window_entities)}")
        
        # 栏杆图层识别
        railing_layers = cad_processor._detect_special_layers(
            entities, cad_processor.railing_layer_patterns, debug=False, layer_type="栏杆"
        )
        print(f"  栏杆图层识别结果: {railing_layers}")
        if railing_layers:
            railing_entities = [e for e in entities if e['layer'] in railing_layers]
            print(f"    栏杆实体数量: {len(railing_entities)}")
        
        # 6. 测试完整的处理流程
        print(f"\n🔄 完整处理流程测试:")
        
        enhanced_processor = EnhancedCADProcessor(None, None)
        enhanced_processor.status_callback = None  # 禁用状态回调
        enhanced_processor.progress_callback = None  # 禁用进度回调
        
        print(f"  开始处理文件...")
        success = enhanced_processor.process_single_file(file_path)
        
        if success:
            print(f"  ✅ 文件处理成功")
            
            # 分析处理结果
            entities_count = len(enhanced_processor.current_file_entities) if enhanced_processor.current_file_entities else 0
            groups_count = len(enhanced_processor.all_groups) if enhanced_processor.all_groups else 0
            
            print(f"  📊 处理结果:")
            print(f"    实体数量: {entities_count}")
            print(f"    分组数量: {groups_count}")
            
            # 分析分组结果
            if enhanced_processor.all_groups:
                print(f"  📋 分组详情:")
                group_types = defaultdict(int)
                
                for i, group in enumerate(enhanced_processor.all_groups):
                    group_label = group.get('label', 'unlabeled')
                    group_size = len(group.get('entities', []))
                    group_types[group_label] += 1
                    
                    if i < 10:  # 只显示前10个组的详情
                        print(f"    组 {i+1}: {group_label} ({group_size}个实体)")
                
                print(f"  📊 分组类型统计:")
                for label, count in group_types.items():
                    print(f"    {label}: {count}个组")
            
            # 检查组信息
            if hasattr(enhanced_processor, 'groups_info') and enhanced_processor.groups_info:
                print(f"  📋 组信息数量: {len(enhanced_processor.groups_info)}")
            else:
                print(f"  ⚠️ 组信息为空，尝试更新...")
                enhanced_processor._update_groups_info()
                if hasattr(enhanced_processor, 'groups_info') and enhanced_processor.groups_info:
                    print(f"  ✅ 组信息更新成功: {len(enhanced_processor.groups_info)}")
                else:
                    print(f"  ❌ 组信息更新失败")
        else:
            print(f"  ❌ 文件处理失败")
        
        # 7. 检查图层模式匹配
        print(f"\n🔍 图层模式匹配详细分析:")
        
        print(f"  墙体图层模式:")
        wall_patterns = cad_processor.wall_layer_patterns[:10]  # 显示前10个模式
        for pattern in wall_patterns:
            matching_layers = [layer for layer in layer_stats.keys() if pattern.lower() in layer.lower()]
            if matching_layers:
                print(f"    '{pattern}' -> {matching_layers}")
        
        print(f"  门窗图层模式:")
        door_window_patterns = cad_processor.door_window_layer_patterns[:10]
        for pattern in door_window_patterns:
            matching_layers = [layer for layer in layer_stats.keys() if pattern.lower() in layer.lower()]
            if matching_layers:
                print(f"    '{pattern}' -> {matching_layers}")
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def diagnose_processor_creation():
    """诊断处理器创建问题"""
    print(f"\n🔧 诊断处理器创建问题:")
    print("="*40)
    
    try:
        # 测试CAD数据处理器创建
        print("1. 测试CAD数据处理器创建...")
        from cad_data_processor import CADDataProcessor
        cad_processor = CADDataProcessor()
        print("   ✅ CAD数据处理器创建成功")
        
        # 检查处理器属性
        print("   📋 处理器属性检查:")
        print(f"     专业DXF读取器: {'✅' if hasattr(cad_processor, 'professional_reader') and cad_processor.professional_reader else '❌'}")
        print(f"     线段合并器: {'✅' if hasattr(cad_processor, 'line_merger') and cad_processor.line_merger else '❌'}")
        print(f"     重叠线条合并器: {'✅' if hasattr(cad_processor, 'overlapping_merger') and cad_processor.overlapping_merger else '❌'}")
        
        # 测试增强CAD处理器创建
        print("\n2. 测试增强CAD处理器创建...")
        from main_enhanced import EnhancedCADProcessor
        enhanced_processor = EnhancedCADProcessor(None, None)
        print("   ✅ 增强CAD处理器创建成功")
        
        # 检查增强处理器属性
        print("   📋 增强处理器属性检查:")
        print(f"     基础处理器: {'✅' if hasattr(enhanced_processor, 'processor') and enhanced_processor.processor else '❌'}")
        print(f"     process_single_file方法: {'✅' if hasattr(enhanced_processor, 'process_single_file') else '❌'}")
        print(f"     _auto_process_special_entities方法: {'✅' if hasattr(enhanced_processor, '_auto_process_special_entities') else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理器创建诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主诊断函数"""
    print("🚀 开始图层识别问题诊断")
    print("="*60)
    
    # 1. 诊断处理器创建
    processor_ok = diagnose_processor_creation()
    
    if not processor_ok:
        print("❌ 处理器创建有问题，无法继续诊断")
        return False
    
    # 2. 查找测试文件
    test_file = None
    possible_files = [
        "wall00.dxf",
        "test.dxf",
        "sample.dxf"
    ]
    
    for filename in possible_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("⚠️ 未找到测试DXF文件，请提供文件路径")
        print("可用的测试文件名: wall00.dxf, test.dxf, sample.dxf")
        
        # 尝试从命令行参数获取文件路径
        if len(sys.argv) > 1:
            test_file = sys.argv[1]
            if not os.path.exists(test_file):
                print(f"❌ 指定的文件不存在: {test_file}")
                return False
        else:
            return False
    
    # 3. 诊断文件处理
    print(f"\n📁 使用测试文件: {test_file}")
    file_ok = diagnose_layer_recognition(test_file)
    
    # 4. 输出诊断总结
    print(f"\n" + "="*60)
    print("📋 诊断总结:")
    print("="*60)
    
    if processor_ok and file_ok:
        print("✅ 基础功能正常，可能的问题:")
        print("  1. 图层命名不匹配预定义模式")
        print("  2. 特殊图层识别逻辑需要调整")
        print("  3. 分组算法参数需要优化")
        print("  4. 显示文件与处理文件不一致")
        
        print("\n💡 建议解决方案:")
        print("  1. 检查DXF文件中的实际图层名称")
        print("  2. 扩展图层识别模式")
        print("  3. 调整分组阈值参数")
        print("  4. 确保处理器状态正确更新")
    else:
        print("❌ 发现基础功能问题，需要修复:")
        if not processor_ok:
            print("  - 处理器创建或配置问题")
        if not file_ok:
            print("  - 文件处理流程问题")
    
    return processor_ok and file_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
