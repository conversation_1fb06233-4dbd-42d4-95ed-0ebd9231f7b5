#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的处理器重置修复
只添加最基本的追踪功能，避免复杂的代码修改
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def add_simple_tracking():
    """添加简化的处理器追踪"""
    print("🔧 添加简化的处理器追踪")
    print("="*60)
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. 在初始化中添加简单的计数器
        print("1. 添加处理器重置计数器...")
        
        init_tracking = '''        
        # 🔍 简化处理器重置追踪
        self._processor_reset_count = 0
        print("🔍 处理器重置追踪已启动")
'''
        
        # 查找合适的插入位置
        insert_pos = content.find('        # 调用父类初始化')
        if insert_pos != -1:
            content = content[:insert_pos] + init_tracking + content[insert_pos:]
            print("  ✅ 重置计数器已添加")
        
        # 2. 修改处理器重置检测，添加简单日志
        print("2. 修改处理器重置检测...")
        
        old_reset_code = '''            if not self.processor:
                print("  ⚠️ 处理器不存在，创建新的处理器")'''
        
        new_reset_code = '''            if not self.processor:
                # 🔍 简化追踪处理器重置
                if hasattr(self, '_processor_reset_count'):
                    self._processor_reset_count += 1
                    import time
                    timestamp = time.strftime("%H:%M:%S", time.localtime())
                    print(f"🔍 [{timestamp}] 处理器重置 #{self._processor_reset_count} - _load_from_cache")
                    print(f"   📁 当前文件: {getattr(self, 'current_file', '未知')}")
                    print(f"   💾 缓存大小: {len(getattr(self, 'file_cache', {}))}")
                    
                    # 简单的调用栈信息
                    import traceback
                    stack = traceback.extract_stack()
                    if len(stack) >= 2:
                        caller = stack[-2]
                        print(f"   📞 调用者: {os.path.basename(caller.filename)}:{caller.lineno} in {caller.name}")
                else:
                    print("🔍 [追踪未初始化] 处理器重置 - _load_from_cache")
                
                print("  ⚠️ 处理器不存在，创建新的处理器")'''
        
        if old_reset_code in content:
            content = content.replace(old_reset_code, new_reset_code)
            print("  ✅ 处理器重置检测已修改")
        
        # 3. 在其他可能的重置位置添加类似的追踪
        print("3. 添加其他位置的追踪...")
        
        # 查找其他处理器检查位置
        other_checks = [
            ('if not self.processor:', '组列表更新'),
        ]
        
        for check_pattern, description in other_checks:
            # 查找所有匹配的位置
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if check_pattern in line and '⚠️ 没有处理器' in lines[i+1] if i+1 < len(lines) else False:
                    # 在这个位置添加追踪
                    lines[i+1] = lines[i+1].replace(
                        'print("⚠️ 没有处理器，',
                        '''if hasattr(self, '_processor_reset_count'):
                self._processor_reset_count += 1
                import time
                timestamp = time.strftime("%H:%M:%S", time.localtime())
                print(f"🔍 [{timestamp}] 处理器重置 #{self._processor_reset_count} - {description}")
            print("⚠️ 没有处理器，'''
                    )
                    print(f"  ✅ 已添加 {description} 的追踪")
                    break
            
            content = '\n'.join(lines)
        
        # 保存修改后的文件
        with open('main_enhanced_with_v2_fill.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 简化处理器追踪添加完成")
        return True
        
    except Exception as e:
        print(f"❌ 添加追踪失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_fix():
    """测试简化修复"""
    print(f"\n🧪 测试简化修复")
    print("="*60)
    
    try:
        # 检查修改是否成功
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('_processor_reset_count = 0', '重置计数器'),
            ('处理器重置追踪已启动', '追踪初始化'),
            ('处理器重置 #', '重置日志'),
            ('📁 当前文件:', '文件信息'),
            ('💾 缓存大小:', '缓存信息'),
            ('📞 调用者:', '调用信息')
        ]
        
        print("检查修改结果:")
        for check, description in checks:
            if check in content:
                print(f"  ✅ {description}: 已添加")
            else:
                print(f"  ❌ {description}: 未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_test_runner():
    """创建测试运行器"""
    print(f"\n🚀 创建测试运行器")
    print("="*60)
    
    test_runner = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试简化处理器追踪
"""

import sys
import os
import tkinter as tk
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_tracking():
    """测试简化追踪功能"""
    print("🧪 测试简化处理器追踪")
    print("="*50)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        print("1. 导入主程序...")
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        print("  ✅ 导入成功")
        
        print("2. 创建应用实例...")
        app = EnhancedCADAppV2(root)
        print("  ✅ 创建成功")
        
        print("3. 检查追踪功能...")
        if hasattr(app, '_processor_reset_count'):
            print(f"  ✅ 重置计数器: {app._processor_reset_count}")
        else:
            print("  ❌ 重置计数器未初始化")
        
        print("4. 模拟处理器重置...")
        # 临时清空处理器来触发重置
        original_processor = app.processor
        app.processor = None
        
        # 尝试触发重置检测
        try:
            # 这会触发处理器检查
            app._load_from_cache("test.dxf")
        except:
            pass  # 忽略错误，我们只关心追踪
        
        # 恢复处理器
        app.processor = original_processor
        
        print("5. 检查追踪结果...")
        if hasattr(app, '_processor_reset_count') and app._processor_reset_count > 0:
            print(f"  ✅ 成功追踪到 {app._processor_reset_count} 次重置")
        else:
            print("  ⚠️ 未检测到重置事件")
        
        # 清理
        root.destroy()
        
        print("\\n🎉 简化追踪测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_simple_tracking()
'''
    
    with open('test_simple_tracking.py', 'w', encoding='utf-8') as f:
        f.write(test_runner)
    
    print("✅ 测试运行器已创建: test_simple_tracking.py")

def main():
    """主函数"""
    print("🚀 开始简化处理器重置修复")
    
    try:
        # 1. 添加简化追踪
        tracking_success = add_simple_tracking()
        
        # 2. 测试修复
        test_success = test_simple_fix()
        
        # 3. 创建测试运行器
        create_test_runner()
        
        print(f"\n" + "="*60)
        print(f"📊 简化修复结果:")
        print(f"  简化追踪添加: {'✅ 成功' if tracking_success else '❌ 失败'}")
        print(f"  修复测试: {'✅ 通过' if test_success else '❌ 失败'}")
        print(f"  测试运行器: ✅ 已创建")
        
        if tracking_success and test_success:
            print(f"\n🎯 下一步操作:")
            print(f"   1. 运行测试: python test_simple_tracking.py")
            print(f"   2. 如果测试通过，运行主程序: python main_enhanced_with_v2_fill.py")
            print(f"   3. 观察控制台的简化追踪信息")
            
            print(f"\n🔍 追踪信息格式:")
            print(f"   🔍 [时间戳] 处理器重置 #次数 - 触发位置")
            print(f"   📁 当前文件: 文件路径")
            print(f"   💾 缓存大小: 缓存中文件数量")
            print(f"   📞 调用者: 文件:行号 in 方法名")
            
            print(f"\n💡 这个简化版本:")
            print(f"   - 避免了复杂的方法添加")
            print(f"   - 只添加基本的计数和日志")
            print(f"   - 不会导致AttributeError")
            print(f"   - 能够追踪重置的基本信息")
        else:
            print(f"\n❌ 简化修复失败，请检查错误信息")
        
    except Exception as e:
        print(f"❌ 简化修复过程失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
