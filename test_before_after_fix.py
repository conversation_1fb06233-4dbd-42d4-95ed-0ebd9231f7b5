#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复前后的差异
对比原始代码和修复后代码的行为差异
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def simulate_original_behavior():
    """模拟原始行为（修复前）"""
    print("🔄 模拟原始行为（修复前）...")
    
    try:
        from cad_visualizer import CADVisualizer
        
        visualizer = CADVisualizer()
        
        # 模拟原始数据（可能包含字符串）
        all_entities = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
            {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(50, 0), (50, 50)]},
            {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(0, 50), (50, 50)]}
        ]
        
        # 模拟包含字符串的当前组（原始问题）
        current_group_with_strings = [
            {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(50, 0), (50, 50)]},
            'index',  # 原始问题：字符串数据
            'total',
            'entity_count'
        ]
        
        labeled_entities = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]}
        ]
        
        print(f"📋 原始数据:")
        print(f"  所有实体: {len(all_entities)} 个")
        print(f"  当前组（含字符串）: {len(current_group_with_strings)} 个")
        print(f"  已标注实体: {len(labeled_entities)} 个")
        
        # 直接传递给可视化器（原始行为）
        print("\n🎨 原始行为：直接传递数据...")
        visualizer.visualize_overview(
            all_entities,
            current_group_with_strings,  # 包含字符串的数据
            labeled_entities
        )
        
        # 检查结果
        if hasattr(visualizer, 'ax_overview') and visualizer.ax_overview:
            children = visualizer.ax_overview.get_children()
            line_objects = [child for child in children if hasattr(child, 'get_data')]
            print(f"  结果：{len(line_objects)} 个线条对象")
        
        return True
        
    except Exception as e:
        print(f"❌ 原始行为模拟失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_fixed_behavior():
    """模拟修复后行为"""
    print("\n🔧 模拟修复后行为...")
    
    try:
        from cad_visualizer import CADVisualizer
        from main_enhanced import EnhancedCADProcessor
        
        visualizer = CADVisualizer()
        processor = EnhancedCADProcessor(None, None)
        
        # 相同的原始数据
        all_entities = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
            {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(50, 0), (50, 50)]},
            {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(0, 50), (50, 50)]}
        ]
        
        current_group_with_strings = [
            {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(50, 0), (50, 50)]},
            'index',
            'total', 
            'entity_count'
        ]
        
        labeled_entities = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]}
        ]
        
        print(f"📋 修复后处理:")
        print(f"  原始当前组: {len(current_group_with_strings)} 个项目")
        
        # 使用数据清理（修复后行为）
        cleaned_current_group = processor._clean_group_data(current_group_with_strings)
        print(f"  清理后当前组: {len(cleaned_current_group)} 个实体")
        
        # 传递清理后的数据
        print("\n🎨 修复后行为：传递清理后的数据...")
        visualizer.visualize_overview(
            all_entities,
            cleaned_current_group,  # 清理后的数据
            labeled_entities
        )
        
        # 检查结果
        if hasattr(visualizer, 'ax_overview') and visualizer.ax_overview:
            children = visualizer.ax_overview.get_children()
            line_objects = [child for child in children if hasattr(child, 'get_data')]
            print(f"  结果：{len(line_objects)} 个线条对象")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复后行为模拟失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况...")
    
    try:
        from cad_visualizer import CADVisualizer
        from main_enhanced import EnhancedCADProcessor
        
        visualizer = CADVisualizer()
        processor = EnhancedCADProcessor(None, None)
        
        # 测试用例
        test_cases = [
            {
                'name': 'None当前组',
                'current_group': None
            },
            {
                'name': '空列表当前组',
                'current_group': []
            },
            {
                'name': '只有字符串的当前组',
                'current_group': ['index', 'total', 'entity_count']
            },
            {
                'name': '混合数据当前组',
                'current_group': [
                    {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(50, 0), (50, 50)]},
                    'index'
                ]
            }
        ]
        
        all_entities = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
            {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(50, 0), (50, 50)]}
        ]
        
        for test_case in test_cases:
            name = test_case['name']
            current_group = test_case['current_group']
            
            print(f"\n  测试: {name}")
            print(f"    原始数据: {current_group}")
            
            # 模拟修复后的处理
            if current_group:
                cleaned_group = processor._clean_group_data(current_group)
            else:
                cleaned_group = []
            
            print(f"    清理后: {cleaned_group}")
            
            # 测试可视化
            try:
                visualizer.visualize_overview(all_entities, cleaned_group, [])
                print(f"    结果: ✅ 可视化成功")
            except Exception as e:
                print(f"    结果: ❌ 可视化失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_potential_issues():
    """分析潜在问题"""
    print("\n🔍 分析潜在问题...")
    
    print("📋 可能的问题原因:")
    print("  1. 数据清理改变了实体引用 - 可视化器依赖对象引用进行匹配")
    print("  2. 清理后的空组影响高亮逻辑 - 当前组为空时高亮失效")
    print("  3. 实体匹配算法问题 - 清理后的实体无法正确匹配")
    print("  4. 坐标计算问题 - 清理影响了坐标范围计算")
    print("  5. 颜色分配逻辑问题 - 清理后的数据导致颜色分配错误")
    
    print("\n🔧 建议的解决方案:")
    print("  1. 保持实体对象引用不变 - 只过滤，不创建新对象")
    print("  2. 改进空组处理 - 区分None和空数组的处理")
    print("  3. 增强实体匹配 - 使用多种匹配策略")
    print("  4. 验证坐标完整性 - 确保清理不影响坐标数据")
    print("  5. 调试颜色分配 - 检查清理后的颜色逻辑")
    
    return True

def test_object_reference_preservation():
    """测试对象引用保持"""
    print("\n🔗 测试对象引用保持...")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        processor = EnhancedCADProcessor(None, None)
        
        # 创建原始实体
        original_entity = {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]}
        
        # 创建包含该实体的组
        original_group = [
            original_entity,
            'invalid_string',
            {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(50, 0), (50, 50)]}
        ]
        
        print(f"📋 原始实体ID: {id(original_entity)}")
        
        # 清理数据
        cleaned_group = processor._clean_group_data(original_group)
        
        print(f"📊 清理结果:")
        print(f"  原始组: {len(original_group)} 个项目")
        print(f"  清理后: {len(cleaned_group)} 个实体")
        
        # 检查对象引用是否保持
        reference_preserved = False
        for cleaned_entity in cleaned_group:
            if id(cleaned_entity) == id(original_entity):
                reference_preserved = True
                print(f"  ✅ 对象引用保持: {id(cleaned_entity)}")
                break
        
        if not reference_preserved:
            print(f"  ❌ 对象引用未保持")
            print(f"  清理后实体IDs: {[id(e) for e in cleaned_group]}")
        
        return reference_preserved
        
    except Exception as e:
        print(f"❌ 对象引用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CAD分类标注工具 - 修复前后差异测试")
    print("=" * 60)
    
    tests = [
        ("原始行为模拟", simulate_original_behavior),
        ("修复后行为模拟", simulate_fixed_behavior),
        ("边界情况测试", test_edge_cases),
        ("潜在问题分析", analyze_potential_issues),
        ("对象引用保持测试", test_object_reference_preservation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    failed_tests = [name for name, success in results if not success]
    
    if failed_tests:
        print(f"\n⚠️ 发现问题: {len(failed_tests)} 个")
        print("需要进一步调查修复前后的行为差异")
    else:
        print("\n✅ 所有测试通过")
        print("修复前后的行为基本一致")
    
    print("=" * 60)
