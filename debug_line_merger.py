#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试线条合并算法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from line_merger import SimpleLineMerger

def debug_line_merger():
    """调试线条合并算法"""
    
    # 创建非常简单的测试数据 - 两条连接的水平线
    test_lines = [
        [(0.0, 0.0), (100.0, 0.0)],    # 第一条线
        [(100.0, 0.0), (200.0, 0.0)]   # 第二条线，精确连接
    ]
    
    print("调试线条合并算法...")
    print(f"测试数据: {len(test_lines)} 条线段")
    for i, line in enumerate(test_lines):
        print(f"  线段 {i+1}: {line[0]} -> {line[1]}")
    
    # 创建合并器，使用较大的阈值确保能够合并
    merger = SimpleLineMerger(
        distance_threshold=10,  # 10mm连接阈值
        angle_threshold=5,      # 5度角度阈值
        enable_iterative=True,
        max_iterations=3
    )
    
    # 执行合并
    merged_lines = merger.merge_lines(test_lines)
    
    print(f"\n合并结果: {len(merged_lines)} 条线段")
    for i, line in enumerate(merged_lines):
        if hasattr(line, 'coords'):
            coords = list(line.coords)
            print(f"  合并线段 {i+1}: {coords[0]} -> {coords[-1]}")
        else:
            print(f"  合并线段 {i+1}: {line[0]} -> {line[-1]}")
    
    # 打印详细统计
    merger.print_stats()

if __name__ == "__main__":
    debug_line_merger()
