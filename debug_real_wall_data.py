#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试实际墙体数据的线条合并问题
"""

import sys
import os
import math
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cad_data_processor import CADDataProcessor
from line_merger import DXFLineMerger

def analyze_real_wall_data():
    """分析实际墙体数据"""
    print("🔍 分析实际墙体数据")
    print("=" * 60)
    
    # 加载实际DXF文件
    processor = CADDataProcessor()
    file_path = "C:/A-BCXM/CAD分类标注工具C01/test-dxf-wall/wall00.dxf"
    
    print(f"加载文件: {file_path}")
    entities = processor.load_dxf_file(file_path)
    
    if not entities:
        print("❌ 无法加载文件")
        return
    
    print(f"✅ 加载成功，共 {len(entities)} 个实体")
    
    # 识别墙体图层
    wall_layers = processor._detect_special_layers(
        entities, processor.wall_layer_patterns, debug=False, layer_type="墙体"
    )
    print(f"识别到墙体图层: {wall_layers}")
    
    # 过滤墙体线条
    wall_entities = [e for e in entities if e['layer'] in wall_layers]
    wall_lines = [e for e in wall_entities if e.get('type') in ['LINE', 'LWPOLYLINE', 'POLYLINE']]
    
    print(f"墙体实体总数: {len(wall_entities)}")
    print(f"墙体线条数: {len(wall_lines)}")
    
    if len(wall_lines) == 0:
        print("❌ 未找到墙体线条")
        return
    
    # 分析前10条线条的详细信息
    print(f"\n📊 前10条墙体线条分析:")
    for i, line in enumerate(wall_lines[:10]):
        print(f"  线条 {i+1}:")
        print(f"    类型: {line.get('type')}")
        print(f"    图层: {line.get('layer')}")
        
        if line.get('type') == 'LINE':
            start_x = line.get('start_x', 0)
            start_y = line.get('start_y', 0)
            end_x = line.get('end_x', 0)
            end_y = line.get('end_y', 0)
            length = math.sqrt((end_x - start_x)**2 + (end_y - start_y)**2)
            print(f"    坐标: ({start_x:.2f}, {start_y:.2f}) -> ({end_x:.2f}, {end_y:.2f})")
            print(f"    长度: {length:.2f}")
        elif line.get('type') in ['LWPOLYLINE', 'POLYLINE']:
            points = line.get('points', [])
            print(f"    点数: {len(points)}")
            if len(points) >= 2:
                print(f"    起点: {points[0]}")
                print(f"    终点: {points[-1]}")
    
    # 测试线条合并
    print(f"\n🔧 测试线条合并:")
    
    # 创建合并器，使用更宽松的阈值
    merger = DXFLineMerger(
        distance_threshold=50,   # 增加到50mm
        angle_threshold=10,      # 增加到10度
        enable_iterative=True,
        max_iterations=3
    )
    
    print(f"合并参数:")
    print(f"  距离阈值: {merger.merger.dist_thresh}mm")
    print(f"  角度阈值: {merger.merger.angle_thresh}度")
    
    # 执行合并
    original_count = len(wall_lines)
    merged_entities = merger.process_entities(wall_lines.copy())
    merged_lines = [e for e in merged_entities if e.get('type') in ['LINE', 'LWPOLYLINE', 'POLYLINE']]
    merged_count = len(merged_lines)
    
    print(f"\n📈 合并结果:")
    print(f"  原始线条数: {original_count}")
    print(f"  合并后线条数: {merged_count}")
    print(f"  减少线条数: {original_count - merged_count}")
    print(f"  合并率: {(original_count - merged_count) / original_count * 100:.1f}%")
    
    # 如果仍然没有合并，分析原因
    if merged_count == original_count:
        print(f"\n⚠️ 没有线条被合并，分析可能原因:")
        analyze_merge_failure(wall_lines[:5], merger)  # 分析前5条线条
    
    return wall_lines, merged_lines

def analyze_merge_failure(lines, merger):
    """分析合并失败的原因"""
    print(f"🔍 分析前 {len(lines)} 条线条的连接可能性:")
    
    for i in range(len(lines)):
        for j in range(i + 1, len(lines)):
            line1 = lines[i]
            line2 = lines[j]
            
            print(f"\n  线条 {i+1} vs 线条 {j+1}:")
            
            # 转换为坐标格式
            coords1 = get_line_coords(line1)
            coords2 = get_line_coords(line2)
            
            if not coords1 or not coords2:
                print(f"    ❌ 无法获取坐标")
                continue
            
            print(f"    线条1: {coords1[0]} -> {coords1[-1]}")
            print(f"    线条2: {coords2[0]} -> {coords2[-1]}")
            
            # 计算端点间最小距离
            min_dist = calculate_min_endpoint_distance(coords1, coords2)
            print(f"    最小端点距离: {min_dist:.2f}mm (阈值: {merger.merger.dist_thresh}mm)")
            
            # 检查平行性
            is_parallel = check_parallel(coords1, coords2, merger.merger.angle_thresh)
            print(f"    平行检测: {'通过' if is_parallel else '失败'}")
            
            # 综合判断
            can_merge = min_dist <= merger.merger.dist_thresh and is_parallel
            print(f"    可以合并: {'是' if can_merge else '否'}")

def get_line_coords(line):
    """获取线条坐标"""
    if line.get('type') == 'LINE':
        start = (line.get('start_x', 0), line.get('start_y', 0))
        end = (line.get('end_x', 0), line.get('end_y', 0))
        return [start, end]
    elif line.get('type') in ['LWPOLYLINE', 'POLYLINE']:
        points = line.get('points', [])
        return points if len(points) >= 2 else None
    return None

def calculate_min_endpoint_distance(coords1, coords2):
    """计算两条线段端点间的最小距离"""
    start1, end1 = coords1[0], coords1[-1]
    start2, end2 = coords2[0], coords2[-1]
    
    distances = [
        math.sqrt((start1[0] - start2[0])**2 + (start1[1] - start2[1])**2),
        math.sqrt((start1[0] - end2[0])**2 + (start1[1] - end2[1])**2),
        math.sqrt((end1[0] - start2[0])**2 + (end1[1] - start2[1])**2),
        math.sqrt((end1[0] - end2[0])**2 + (end1[1] - end2[1])**2)
    ]
    
    return min(distances)

def check_parallel(coords1, coords2, angle_threshold):
    """检查两条线段是否平行"""
    start1, end1 = coords1[0], coords1[-1]
    start2, end2 = coords2[0], coords2[-1]
    
    # 计算方向向量
    dx1, dy1 = end1[0] - start1[0], end1[1] - start1[1]
    dx2, dy2 = end2[0] - start2[0], end2[1] - start2[1]
    
    # 计算向量长度
    length1 = math.sqrt(dx1**2 + dy1**2)
    length2 = math.sqrt(dx2**2 + dy2**2)
    
    if length1 < 1e-5 or length2 < 1e-5:
        return False
    
    # 计算夹角
    dot_product = (dx1 * dx2 + dy1 * dy2) / (length1 * length2)
    dot_product = max(-1, min(1, abs(dot_product)))
    angle_rad = math.acos(dot_product)
    angle_deg = math.degrees(angle_rad)
    
    return angle_deg < angle_threshold

def main():
    """主函数"""
    try:
        wall_lines, merged_lines = analyze_real_wall_data()
        print(f"\n✅ 分析完成")
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
