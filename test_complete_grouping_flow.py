#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试完整的分组流程
验证从线条处理到分组识别的完整流程是否正确工作
"""

import os
import sys

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_complete_grouping_flow():
    """测试完整的分组流程"""
    print("🧪 测试完整的分组流程...")
    
    try:
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        # 创建测试处理器
        from cad_data_processor import CADDataProcessor
        app.processor = type('MockProcessor', (), {})()
        app.processor.processor = CADDataProcessor()
        app.processor.auto_labeled_entities = []
        app.processor.labeled_entities = []
        
        # 创建更复杂的测试数据，模拟真实CAD文件
        test_entities = [
            # 墙体实体 - 形成一个房间
            {
                'type': 'LINE',
                'layer': 'WALL',
                'points': [[0, 0], [1000, 0]],
                'start': [0, 0],
                'end': [1000, 0]
            },
            {
                'type': 'LINE',
                'layer': 'WALL',
                'points': [[1000, 0], [1000, 800]],
                'start': [1000, 0],
                'end': [1000, 800]
            },
            {
                'type': 'LINE',
                'layer': 'WALL',
                'points': [[1000, 800], [0, 800]],
                'start': [1000, 800],
                'end': [0, 800]
            },
            {
                'type': 'LINE',
                'layer': 'WALL',
                'points': [[0, 800], [0, 0]],
                'start': [0, 800],
                'end': [0, 0]
            },
            # 门窗实体
            {
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'points': [[200, 0], [300, 0]],
                'start': [200, 0],
                'end': [300, 0]
            },
            {
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'points': [[200, -10], [300, -10]],
                'start': [200, -10],
                'end': [300, -10]
            },
            {
                'type': 'LINE',
                'layer': 'DOOR',
                'points': [[500, 0], [600, 0]],
                'start': [500, 0],
                'end': [600, 0]
            },
            # 家具实体
            {
                'type': 'CIRCLE',
                'layer': 'FURNITURE',
                'center': [300, 400],
                'radius': 50
            },
            {
                'type': 'LWPOLYLINE',
                'layer': 'FURNITURE',
                'points': [[100, 100], [200, 100], [200, 200], [100, 200]],
                'closed': True
            },
            # 文字实体
            {
                'type': 'TEXT',
                'layer': 'TEXT',
                'text': '客厅',
                'position': [500, 400]
            },
            # 标注实体
            {
                'type': 'DIMENSION',
                'layer': 'DIM',
                'points': [[0, -50], [1000, -50]]
            }
        ]
        
        print(f"✅ 创建了 {len(test_entities)} 个测试实体")
        
        # 步骤1：模拟基础数据加载
        print("\n📁 步骤1：基础数据加载...")
        app.processor.raw_entities = test_entities
        print(f"  原始实体数: {len(test_entities)}")
        
        # 步骤2：模拟线条处理
        print("\n🔧 步骤2：线条处理...")
        
        # 去重处理
        deduped_entities = app.processor.processor.process_line_deduplication(test_entities)
        print(f"  去重后实体数: {len(deduped_entities)}")
        
        # 合并处理
        merged_entities = app.processor.processor.merge_lines(deduped_entities)
        app.processor.merged_entities = merged_entities
        print(f"  合并后实体数: {len(merged_entities)}")
        
        # 步骤3：分组处理（关键测试）
        print("\n🎯 步骤3：分组处理...")
        success = app._process_grouping()
        
        if success:
            print("✅ 分组处理成功")
            
            # 检查分组结果
            total_groups = len(app.processor.all_groups) if hasattr(app.processor, 'all_groups') else 0
            auto_labeled_count = len(app.processor.auto_labeled_entities) if hasattr(app.processor, 'auto_labeled_entities') else 0
            
            print(f"  总组数: {total_groups}")
            print(f"  自动标注实体数: {auto_labeled_count}")
            
            # 检查组状态信息
            if hasattr(app.processor, 'groups_info'):
                auto_labeled_groups = sum(1 for info in app.processor.groups_info if info['status'] == 'auto_labeled')
                unlabeled_groups = sum(1 for info in app.processor.groups_info if info['status'] == 'unlabeled')
                
                print(f"  自动标注组数: {auto_labeled_groups}")
                print(f"  未标注组数: {unlabeled_groups}")
                
                # 显示各类型的分组情况
                category_stats = {}
                for info in app.processor.groups_info:
                    category = info.get('category', 'unknown')
                    if category not in category_stats:
                        category_stats[category] = 0
                    category_stats[category] += 1
                
                print(f"  分类统计: {category_stats}")
                
                # 验证是否正确识别了特殊图层
                expected_categories = {'wall', 'door_window', 'text', 'dimension'}
                found_categories = set(info.get('category') for info in app.processor.groups_info if info.get('category'))
                
                print(f"  期望类别: {expected_categories}")
                print(f"  实际类别: {found_categories}")
                
                success_rate = len(found_categories & expected_categories) / len(expected_categories) * 100
                print(f"  识别成功率: {success_rate:.1f}%")
                
                if success_rate >= 75:  # 至少75%的类别被正确识别
                    print("✅ 特殊图层识别成功")
                    result = True
                else:
                    print("❌ 特殊图层识别不完整")
                    result = False
            else:
                print("❌ 组状态信息缺失")
                result = False
        else:
            print("❌ 分组处理失败")
            result = False
        
        # 步骤4：测试可视化更新
        print("\n🎨 步骤4：可视化更新...")
        try:
            app._update_visualization_group_v2()
            print("✅ 可视化更新成功")
        except Exception as e:
            print(f"❌ 可视化更新失败: {e}")
        
        # 清理
        root.destroy()
        
        return result
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始完整分组流程测试...")
    
    success = test_complete_grouping_flow()
    
    if success:
        print("\n🎉 完整分组流程测试通过！")
        print("✅ 特殊图层识别功能正常")
        print("✅ 自动标记功能正常")
        print("✅ 分组处理功能正常")
    else:
        print("\n❌ 完整分组流程测试失败，需要进一步检查")
