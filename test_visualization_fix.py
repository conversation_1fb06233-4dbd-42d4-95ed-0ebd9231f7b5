#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试可视化修复
验证组数据清理是否解决了字符串实体问题
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_group_data_cleaning():
    """测试组数据清理功能"""
    print("🧪 测试组数据清理功能...")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        # 创建处理器
        processor = EnhancedCADProcessor(None, None)
        
        # 创建包含问题的测试组（模拟原始错误场景）
        problematic_group = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
            'index',  # 字符串实体（原始错误）
            'total',  # 字符串实体（原始错误）
            'entity_count',  # 字符串实体（原始错误）
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)]},
            123,  # 数字
            None,  # None值
            {'layer': 'A-WALL'},  # 缺少type
            {}  # 空字典
        ]
        
        print(f"📋 原始组数据: {len(problematic_group)} 个项目")
        for i, item in enumerate(problematic_group):
            print(f"  项目 {i+1}: {type(item)} - {item}")
        
        # 测试数据清理
        cleaned_group = processor._clean_group_data(problematic_group)
        
        print(f"\n✨ 清理后组数据: {len(cleaned_group)} 个项目")
        for i, item in enumerate(cleaned_group):
            print(f"  项目 {i+1}: {type(item)} - {item.get('type')} - {item.get('layer')}")
        
        # 验证结果
        if len(cleaned_group) == 2:
            print("✅ 数据清理正确，保留了2个有效实体")
            return True
        else:
            print(f"❌ 数据清理错误，应该保留2个有效实体，实际保留了{len(cleaned_group)}个")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_visualizer_with_cleaned_data():
    """测试可视化器使用清理后的数据"""
    print("\n🎨 测试可视化器使用清理后的数据...")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        from cad_visualizer import CADVisualizer
        
        # 创建处理器和可视化器
        processor = EnhancedCADProcessor(None, None)
        visualizer = CADVisualizer()
        
        # 创建包含问题的测试组
        problematic_group = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
            'index',  # 这些字符串会被过滤掉
            'total',
            'entity_count',
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)]},
        ]
        
        print(f"📋 测试组数据: {len(problematic_group)} 个项目")
        
        # 清理数据
        cleaned_group = processor._clean_group_data(problematic_group)
        print(f"✨ 清理后数据: {len(cleaned_group)} 个有效实体")
        
        # 测试可视化器（应该不再出现字符串实体警告）
        print("🎨 测试可视化器...")
        visualizer.visualize_entity_group(cleaned_group, {'wall': '墙体'})
        
        print("✅ 可视化器测试成功，没有字符串实体警告")
        return True
        
    except Exception as e:
        print(f"❌ 可视化器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_show_group_method():
    """测试修复后的_show_group方法"""
    print("\n🔧 测试修复后的_show_group方法...")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        # 创建处理器
        processor = EnhancedCADProcessor(None, None)
        
        # 模拟设置必要的属性
        processor.current_file_entities = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)]},
        ]
        processor.auto_labeled_entities = []
        processor.labeled_entities = []
        processor.all_groups = []
        
        # 创建包含问题的测试组
        problematic_group = [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
            'index',  # 这些会被清理掉
            'total',
            'entity_count',
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)]},
        ]
        
        print(f"📋 测试_show_group方法，组数据: {len(problematic_group)} 个项目")
        
        # 测试_show_group方法（不会实际显示，因为没有可视化器）
        processor._show_group(problematic_group, 1)
        
        print("✅ _show_group方法测试成功")
        return True
        
    except Exception as e:
        print(f"❌ _show_group方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_status_callback_data():
    """测试状态回调数据不会被误传给可视化器"""
    print("\n📞 测试状态回调数据处理...")
    
    try:
        # 模拟状态回调数据（这些不应该传给可视化器）
        status_data = {
            'index': 1,
            'total': 5,
            'entity_count': 3
        }
        
        print(f"📋 状态回调数据: {status_data}")
        print(f"  数据类型: {type(status_data)}")
        print(f"  包含的键: {list(status_data.keys())}")
        
        # 验证这种数据不会被当作实体组处理
        from main_enhanced import EnhancedCADProcessor
        processor = EnhancedCADProcessor(None, None)
        
        # 尝试清理状态数据（应该返回空列表）
        cleaned_data = processor._clean_group_data(status_data)
        
        if len(cleaned_data) == 0:
            print("✅ 状态回调数据正确被过滤，不会传给可视化器")
            return True
        else:
            print(f"❌ 状态回调数据未被正确过滤: {cleaned_data}")
            return False
        
    except Exception as e:
        print(f"❌ 状态回调数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CAD分类标注工具 - 可视化修复测试")
    print("=" * 60)
    
    tests = [
        test_group_data_cleaning,
        test_visualizer_with_cleaned_data,
        test_show_group_method,
        test_status_callback_data
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {test.__name__}: {status}")
    
    if all(results):
        print("\n🎉 所有测试通过！可视化修复成功。")
        print("\n修复总结:")
        print("1. ✅ 添加了组数据清理功能")
        print("2. ✅ 修复了_show_group方法，使用清理后的数据")
        print("3. ✅ 防止字符串数据传递给可视化器")
        print("4. ✅ 改进了错误处理和日志输出")
        print("\n原始错误 '⚠️ 跳过非字典实体: <class 'str'> - index' 已解决。")
    else:
        print("\n❌ 部分测试失败，可能还有问题需要解决。")
    
    print("=" * 60)
