#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分组逻辑测试程序
测试各个分组条件和门窗墙体的自动标注功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import os
from cad_data_processor import CADDataProcessor
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
import math

class GroupingLogicTest:
    """分组逻辑测试类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("CAD分组逻辑测试程序")
        self.root.geometry("1400x900")
        
        # 测试状态
        self.processor = None
        self.test_entities = []
        self.test_results = []
        
        self.create_interface()
        self.create_test_data()
        
    def create_interface(self):
        """创建测试界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="分组逻辑测试", width=350)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 测试按钮组
        ttk.Label(control_frame, text="分组条件测试", font=("Arial", 12, "bold")).pack(pady=10)
        
        ttk.Button(control_frame, text="1. 测试图层识别", 
                  command=self.test_layer_detection).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Button(control_frame, text="2. 测试墙体分组", 
                  command=self.test_wall_grouping).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Button(control_frame, text="3. 测试门窗分组", 
                  command=self.test_door_window_grouping).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Button(control_frame, text="4. 测试连接性分组", 
                  command=self.test_connectivity_grouping).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Button(control_frame, text="5. 测试小实体处理", 
                  command=self.test_small_entity_handling).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Button(control_frame, text="6. 测试组合并逻辑", 
                  command=self.test_group_merging).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Separator(control_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        ttk.Label(control_frame, text="自动标注测试", font=("Arial", 12, "bold")).pack(pady=10)
        
        ttk.Button(control_frame, text="7. 测试墙体自动标注", 
                  command=self.test_wall_auto_labeling).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Button(control_frame, text="8. 测试门窗自动标注", 
                  command=self.test_door_window_auto_labeling).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Button(control_frame, text="9. 测试类别推荐", 
                  command=self.test_category_suggestion).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Separator(control_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        ttk.Button(control_frame, text="运行所有测试", 
                  command=self.run_all_tests,
                  style="Accent.TButton").pack(fill=tk.X, pady=5, padx=10)
        
        ttk.Button(control_frame, text="加载真实DXF文件", 
                  command=self.load_real_dxf).pack(fill=tk.X, pady=2, padx=10)
        
        ttk.Button(control_frame, text="清除测试结果", 
                  command=self.clear_results).pack(fill=tk.X, pady=2, padx=10)
        
        # 状态显示
        ttk.Label(control_frame, text="测试状态", font=("Arial", 10, "bold")).pack(pady=(20, 5))
        
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(control_frame, textvariable=self.status_var, 
                                     foreground="blue")
        self.status_label.pack(pady=2)
        
        self.progress = ttk.Progressbar(control_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=5, padx=10)
        
        # 右侧显示区域
        display_frame = ttk.Frame(main_frame)
        display_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建notebook用于多个视图
        self.notebook = ttk.Notebook(display_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 分组结果视图
        self.grouping_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.grouping_frame, text="分组结果")
        
        # 测试结果视图
        self.results_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.results_frame, text="测试结果")
        
        # 创建测试结果显示
        self.create_results_display()
        
    def create_results_display(self):
        """创建测试结果显示"""
        # 结果文本框
        result_scroll_frame = ttk.Frame(self.results_frame)
        result_scroll_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.result_text = tk.Text(result_scroll_frame, wrap=tk.WORD, font=("Consolas", 10))
        result_scrollbar = ttk.Scrollbar(result_scroll_frame, orient=tk.VERTICAL, 
                                        command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=result_scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_test_data(self):
        """创建测试数据"""
        self.test_entities = [
            # 墙体实体 - 形成一个房间
            {'type': 'LINE', 'layer': 'WALL', 'points': [(0, 0), (1000, 0)], 'id': 'wall_1'},
            {'type': 'LINE', 'layer': 'WALL', 'points': [(1000, 0), (1000, 800)], 'id': 'wall_2'},
            {'type': 'LINE', 'layer': 'WALL', 'points': [(1000, 800), (0, 800)], 'id': 'wall_3'},
            {'type': 'LINE', 'layer': 'WALL', 'points': [(0, 800), (0, 0)], 'id': 'wall_4'},
            
            # 门实体
            {'type': 'LINE', 'layer': 'DOOR', 'points': [(400, 0), (400, 80)], 'id': 'door_line'},
            {'type': 'ARC', 'layer': 'DOOR', 'center': (400, 0), 'radius': 80, 
             'start_angle': 0, 'end_angle': 90, 'id': 'door_arc'},
            
            # 窗户实体
            {'type': 'LINE', 'layer': 'WINDOW', 'points': [(200, 800), (300, 800)], 'id': 'window_1'},
            {'type': 'LINE', 'layer': 'WINDOW', 'points': [(700, 800), (800, 800)], 'id': 'window_2'},
            
            # 家具实体
            {'type': 'CIRCLE', 'layer': 'FURNITURE', 'center': (500, 400), 'radius': 50, 'id': 'table'},
            {'type': 'LWPOLYLINE', 'layer': 'FURNITURE', 
             'points': [(200, 200), (300, 200), (300, 300), (200, 300), (200, 200)], 'id': 'sofa'},
            
            # 文字标注
            {'type': 'TEXT', 'layer': 'ANNOTATION', 'text': '客厅', 'position': (500, 400), 
             'height': 20, 'id': 'room_label'},
            {'type': 'TEXT', 'layer': 'ANNOTATION', 'text': '门', 'position': (400, 40), 
             'height': 15, 'id': 'door_label'},
            
            # 小实体
            {'type': 'CIRCLE', 'layer': 'DETAIL', 'center': (100, 100), 'radius': 5, 'id': 'small_1'},
            {'type': 'LINE', 'layer': 'DETAIL', 'points': [(150, 150), (160, 160)], 'id': 'small_2'},
            
            # 栏杆实体
            {'type': 'LINE', 'layer': 'RAILING', 'points': [(500, 600), (600, 600)], 'id': 'railing_1'},
            {'type': 'LINE', 'layer': 'RAILING', 'points': [(500, 610), (600, 610)], 'id': 'railing_2'},
            
            # 不同图层的墙体（测试图层识别）
            {'type': 'LINE', 'layer': '墙体', 'points': [(1200, 0), (1400, 0)], 'id': 'wall_cn'},
            {'type': 'LINE', 'layer': 'WALLS', 'points': [(1200, 200), (1400, 200)], 'id': 'wall_en'},
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(1200, 400), (1400, 400)], 'id': 'wall_code'},
        ]
        
    def log_result(self, test_name, result, details=""):
        """记录测试结果"""
        timestamp = time.strftime("%H:%M:%S")
        status = "✅ 通过" if result else "❌ 失败"
        
        log_entry = f"[{timestamp}] {test_name}: {status}\n"
        if details:
            log_entry += f"  详情: {details}\n"
        log_entry += "-" * 50 + "\n"
        
        self.result_text.insert(tk.END, log_entry)
        self.result_text.see(tk.END)
        self.root.update()
        
        self.test_results.append({
            'test': test_name,
            'result': result,
            'details': details,
            'timestamp': timestamp
        })
    
    def test_layer_detection(self):
        """测试图层识别功能"""
        self.status_var.set("测试图层识别...")
        self.progress.start()
        
        try:
            # 创建处理器
            if not self.processor:
                self.processor = CADDataProcessor()
            
            # 测试墙体图层识别
            wall_layers = self.processor._detect_special_layers(
                self.test_entities, 
                self.processor.wall_layer_patterns, 
                debug=False, 
                layer_type="墙体"
            )
            
            # 测试门窗图层识别
            door_window_layers = self.processor._detect_special_layers(
                self.test_entities, 
                self.processor.door_window_layer_patterns, 
                debug=False, 
                layer_type="门窗"
            )
            
            # 测试栏杆图层识别
            railing_layers = self.processor._detect_special_layers(
                self.test_entities, 
                self.processor.railing_layer_patterns, 
                debug=False, 
                layer_type="栏杆"
            )
            
            # 验证结果
            expected_wall_layers = {'WALL', '墙体', 'WALLS', 'A-WALL'}
            expected_door_window_layers = {'DOOR', 'WINDOW'}
            expected_railing_layers = {'RAILING'}
            
            wall_success = wall_layers == expected_wall_layers
            door_window_success = door_window_layers == expected_door_window_layers
            railing_success = railing_layers == expected_railing_layers
            
            overall_success = wall_success and door_window_success and railing_success
            
            details = f"""
墙体图层识别: {'✅' if wall_success else '❌'}
  期望: {expected_wall_layers}
  实际: {wall_layers}

门窗图层识别: {'✅' if door_window_success else '❌'}
  期望: {expected_door_window_layers}
  实际: {door_window_layers}

栏杆图层识别: {'✅' if railing_success else '❌'}
  期望: {expected_railing_layers}
  实际: {railing_layers}
"""
            
            self.log_result("图层识别测试", overall_success, details)
            
        except Exception as e:
            self.log_result("图层识别测试", False, f"错误: {str(e)}")
        
        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_wall_grouping(self):
        """测试墙体分组功能"""
        self.status_var.set("测试墙体分组...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 筛选墙体实体
            wall_entities = [e for e in self.test_entities if e['layer'] in ['WALL', '墙体', 'WALLS', 'A-WALL']]

            # 执行墙体分组
            wall_groups = self.processor._group_special_entities_by_layer(
                wall_entities,
                connection_threshold=10,
                entity_type="wall"
            )

            # 验证结果
            success = len(wall_groups) > 0

            # 检查分组逻辑
            group_details = []
            for i, group in enumerate(wall_groups):
                if isinstance(group, dict):
                    entities = group.get('entities', [])
                    label = group.get('label', '未标注')
                    layer = group.get('layer', '未知')
                else:
                    entities = group
                    label = '未标注'
                    layer = entities[0]['layer'] if entities else '未知'

                group_details.append(f"组{i+1}: {len(entities)}个实体, 图层:{layer}, 标签:{label}")

            details = f"""
墙体实体数: {len(wall_entities)}
生成组数: {len(wall_groups)}
分组详情:
""" + "\n".join(group_details)

            self.log_result("墙体分组测试", success, details)

            # 显示分组结果
            self.display_grouping_result(wall_groups, "墙体分组结果")

        except Exception as e:
            self.log_result("墙体分组测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_door_window_grouping(self):
        """测试门窗分组功能"""
        self.status_var.set("测试门窗分组...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 筛选门窗实体
            door_window_entities = [e for e in self.test_entities if e['layer'] in ['DOOR', 'WINDOW']]

            # 执行门窗分组
            door_window_groups = self.processor._group_special_entities_by_layer(
                door_window_entities,
                connection_threshold=50,
                entity_type="door_window"
            )

            # 验证结果
            success = len(door_window_groups) > 0

            # 检查分组逻辑
            group_details = []
            for i, group in enumerate(door_window_groups):
                if isinstance(group, dict):
                    entities = group.get('entities', [])
                    label = group.get('label', '未标注')
                    layer = group.get('layer', '未知')
                else:
                    entities = group
                    label = '未标注'
                    layer = entities[0]['layer'] if entities else '未知'

                group_details.append(f"组{i+1}: {len(entities)}个实体, 图层:{layer}, 标签:{label}")

            details = f"""
门窗实体数: {len(door_window_entities)}
生成组数: {len(door_window_groups)}
分组详情:
""" + "\n".join(group_details)

            self.log_result("门窗分组测试", success, details)

            # 显示分组结果
            self.display_grouping_result(door_window_groups, "门窗分组结果")

        except Exception as e:
            self.log_result("门窗分组测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_connectivity_grouping(self):
        """测试连接性分组功能"""
        self.status_var.set("测试连接性分组...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 筛选非特殊图层实体
            other_entities = [e for e in self.test_entities
                            if e['layer'] not in ['WALL', '墙体', 'WALLS', 'A-WALL', 'DOOR', 'WINDOW', 'RAILING']]

            # 执行连接性分组
            connected_groups = self.processor._group_other_entities_by_layer(
                other_entities,
                distance_threshold=20
            )

            # 验证结果
            success = len(connected_groups) >= 0  # 允许为空

            # 检查分组逻辑
            group_details = []
            for i, group in enumerate(connected_groups):
                if isinstance(group, dict):
                    entities = group.get('entities', [])
                    layer = group.get('layer', '未知')
                else:
                    entities = group
                    layer = entities[0]['layer'] if entities else '未知'

                group_details.append(f"组{i+1}: {len(entities)}个实体, 图层:{layer}")

            details = f"""
其他实体数: {len(other_entities)}
生成组数: {len(connected_groups)}
分组详情:
""" + "\n".join(group_details)

            self.log_result("连接性分组测试", success, details)

        except Exception as e:
            self.log_result("连接性分组测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_small_entity_handling(self):
        """测试小实体处理功能"""
        self.status_var.set("测试小实体处理...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 创建一些测试组
            test_groups = [
                [e for e in self.test_entities if e['layer'] == 'WALL'],
                [e for e in self.test_entities if e['layer'] == 'FURNITURE']
            ]

            # 查找孤立的小实体
            isolated_small = self.processor._find_isolated_small_entities(
                self.test_entities,
                test_groups
            )

            # 对小实体进行分组
            small_groups = self.processor._group_isolated_small_entities(isolated_small)

            # 验证结果
            success = True  # 小实体处理不应该出错

            details = f"""
孤立小实体数: {len(isolated_small)}
小实体组数: {len(small_groups)}
小实体详情: {[e['id'] for e in isolated_small]}
"""

            self.log_result("小实体处理测试", success, details)

        except Exception as e:
            self.log_result("小实体处理测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_group_merging(self):
        """测试组合并逻辑"""
        self.status_var.set("测试组合并逻辑...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 创建一些测试组
            test_groups = [
                [{'type': 'LINE', 'layer': 'TEST', 'points': [(0, 0), (100, 0)], 'id': 'line1'}],
                [{'type': 'LINE', 'layer': 'TEST', 'points': [(100, 0), (200, 0)], 'id': 'line2'}],
                [{'type': 'CIRCLE', 'layer': 'OTHER', 'center': (500, 500), 'radius': 10, 'id': 'circle1'}]
            ]

            # 测试相邻组合并
            merged_groups = self.processor.merge_adjacent_groups(
                test_groups,
                distance_threshold=50,
                debug=True
            )

            # 验证结果
            success = len(merged_groups) <= len(test_groups)

            details = f"""
原始组数: {len(test_groups)}
合并后组数: {len(merged_groups)}
合并成功: {'是' if len(merged_groups) < len(test_groups) else '否'}
"""

            self.log_result("组合并逻辑测试", success, details)

        except Exception as e:
            self.log_result("组合并逻辑测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_wall_auto_labeling(self):
        """测试墙体自动标注功能"""
        self.status_var.set("测试墙体自动标注...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 创建墙体组
            wall_entities = [e for e in self.test_entities if e['layer'] in ['WALL', '墙体', 'WALLS', 'A-WALL']]
            wall_group = wall_entities

            # 提取特征
            features = self.processor.extract_features(wall_group)

            # 测试类别推荐
            suggestions = self.processor.suggest_category(features, wall_group)

            # 测试图层推断
            inferred_category = self.processor._infer_category_from_layer(wall_group)

            # 验证结果
            wall_suggested = any('wall' in str(s).lower() or '墙' in str(s) for s in suggestions)
            wall_inferred = 'wall' in inferred_category.lower() or '墙' in inferred_category

            success = wall_suggested or wall_inferred

            details = f"""
特征提取: {'✅' if features else '❌'}
  长度: {features.get('length', 0):.2f}
  面积: {features.get('area', 0):.2f}
  封闭性: {features.get('closed', 0)}

类别推荐: {suggestions}
图层推断: {inferred_category}
墙体识别: {'✅' if success else '❌'}
"""

            self.log_result("墙体自动标注测试", success, details)

        except Exception as e:
            self.log_result("墙体自动标注测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_door_window_auto_labeling(self):
        """测试门窗自动标注功能"""
        self.status_var.set("测试门窗自动标注...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 测试门的自动标注
            door_entities = [e for e in self.test_entities if e['layer'] == 'DOOR']
            door_features = self.processor.extract_features(door_entities)
            door_suggestions = self.processor.suggest_category(door_features, door_entities)
            door_inferred = self.processor._infer_category_from_layer(door_entities)

            # 测试窗的自动标注
            window_entities = [e for e in self.test_entities if e['layer'] == 'WINDOW']
            window_features = self.processor.extract_features(window_entities)
            window_suggestions = self.processor.suggest_category(window_features, window_entities)
            window_inferred = self.processor._infer_category_from_layer(window_entities)

            # 验证结果
            door_success = any('door' in str(s).lower() or '门' in str(s) for s in door_suggestions) or \
                          'door' in door_inferred.lower() or '门' in door_inferred

            window_success = any('window' in str(s).lower() or '窗' in str(s) for s in window_suggestions) or \
                            'window' in window_inferred.lower() or '窗' in window_inferred

            success = door_success and window_success

            details = f"""
门的识别:
  特征: {door_features.get('length', 0):.2f}长度, {door_features.get('area', 0):.2f}面积
  推荐: {door_suggestions}
  推断: {door_inferred}
  成功: {'✅' if door_success else '❌'}

窗的识别:
  特征: {window_features.get('length', 0):.2f}长度, {window_features.get('area', 0):.2f}面积
  推荐: {window_suggestions}
  推断: {window_inferred}
  成功: {'✅' if window_success else '❌'}
"""

            self.log_result("门窗自动标注测试", success, details)

        except Exception as e:
            self.log_result("门窗自动标注测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def test_category_suggestion(self):
        """测试类别推荐功能"""
        self.status_var.set("测试类别推荐...")
        self.progress.start()

        try:
            if not self.processor:
                self.processor = CADDataProcessor()

            # 测试不同类型实体的类别推荐
            test_cases = [
                ('家具', [e for e in self.test_entities if e['layer'] == 'FURNITURE']),
                ('标注', [e for e in self.test_entities if e['layer'] == 'ANNOTATION']),
                ('栏杆', [e for e in self.test_entities if e['layer'] == 'RAILING']),
                ('细节', [e for e in self.test_entities if e['layer'] == 'DETAIL'])
            ]

            results = []
            for category_name, entities in test_cases:
                if entities:
                    features = self.processor.extract_features(entities)
                    suggestions = self.processor.suggest_category(features, entities)
                    inferred = self.processor._infer_category_from_layer(entities)

                    results.append(f"{category_name}: 推荐={suggestions}, 推断={inferred}")
                else:
                    results.append(f"{category_name}: 无实体")

            success = len(results) > 0

            details = "\n".join(results)

            self.log_result("类别推荐测试", success, details)

        except Exception as e:
            self.log_result("类别推荐测试", False, f"错误: {str(e)}")

        finally:
            self.progress.stop()
            self.status_var.set("就绪")

    def display_grouping_result(self, groups, title="分组结果"):
        """显示分组结果"""
        try:
            # 清除之前的显示
            for widget in self.grouping_frame.winfo_children():
                widget.destroy()

            if not groups:
                ttk.Label(self.grouping_frame, text="无分组数据").pack(expand=True)
                return

            # 创建matplotlib图形
            fig, ax = plt.subplots(figsize=(10, 8))

            # 为每个组分配不同颜色
            colors = plt.cm.Set3(np.linspace(0, 1, len(groups)))

            for group_idx, group in enumerate(groups):
                color = colors[group_idx]

                # 处理不同格式的组
                if isinstance(group, dict):
                    entities = group.get('entities', [])
                    label = group.get('label', f'组{group_idx+1}')
                elif isinstance(group, list):
                    entities = group
                    label = f'组{group_idx+1}'
                else:
                    continue

                # 绘制组中的实体
                for entity in entities:
                    try:
                        if entity.get('type') == 'LINE' and 'points' in entity:
                            points = entity['points']
                            if len(points) >= 2:
                                ax.plot([points[0][0], points[1][0]],
                                       [points[0][1], points[1][1]],
                                       color=color, linewidth=2, alpha=0.8)

                        elif entity.get('type') == 'ARC' and 'center' in entity:
                            center = entity['center']
                            radius = entity.get('radius', 10)
                            start_angle = math.radians(entity.get('start_angle', 0))
                            end_angle = math.radians(entity.get('end_angle', 360))

                            angles = np.linspace(start_angle, end_angle, 50)
                            x = center[0] + radius * np.cos(angles)
                            y = center[1] + radius * np.sin(angles)
                            ax.plot(x, y, color=color, linewidth=2, alpha=0.8)

                        elif entity.get('type') == 'CIRCLE' and 'center' in entity:
                            center = entity['center']
                            radius = entity.get('radius', 10)
                            circle = plt.Circle(center, radius, fill=False, color=color, linewidth=2, alpha=0.8)
                            ax.add_patch(circle)

                        elif entity.get('type') in ('LWPOLYLINE', 'POLYLINE') and 'points' in entity:
                            points = entity['points']
                            if len(points) >= 2:
                                x_coords = [p[0] for p in points]
                                y_coords = [p[1] for p in points]
                                ax.plot(x_coords, y_coords, color=color, linewidth=2, alpha=0.8)

                    except Exception as e:
                        continue

                # 添加组标签
                if entities:
                    # 计算组的中心点
                    centers = []
                    for entity in entities:
                        if entity.get('type') == 'LINE' and 'points' in entity:
                            points = entity['points']
                            if len(points) >= 2:
                                center_x = (points[0][0] + points[1][0]) / 2
                                center_y = (points[0][1] + points[1][1]) / 2
                                centers.append((center_x, center_y))
                        elif 'center' in entity:
                            centers.append(entity['center'])

                    if centers:
                        avg_x = sum(c[0] for c in centers) / len(centers)
                        avg_y = sum(c[1] for c in centers) / len(centers)
                        ax.text(avg_x, avg_y, label, fontsize=10, ha='center', va='center',
                               bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.7))

            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            ax.set_title(f"{title} ({len(groups)} 个组)", fontsize=14, fontweight='bold')

            # 嵌入到tkinter
            canvas = FigureCanvasTkAgg(fig, self.grouping_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            # 切换到分组结果标签页
            self.notebook.select(self.grouping_frame)

        except Exception as e:
            print(f"显示分组结果失败: {e}")

    def run_all_tests(self):
        """运行所有测试"""
        self.status_var.set("运行所有测试...")

        # 清除之前的结果
        self.clear_results()

        def run_tests():
            try:
                tests = [
                    ("图层识别", self.test_layer_detection),
                    ("墙体分组", self.test_wall_grouping),
                    ("门窗分组", self.test_door_window_grouping),
                    ("连接性分组", self.test_connectivity_grouping),
                    ("小实体处理", self.test_small_entity_handling),
                    ("组合并逻辑", self.test_group_merging),
                    ("墙体自动标注", self.test_wall_auto_labeling),
                    ("门窗自动标注", self.test_door_window_auto_labeling),
                    ("类别推荐", self.test_category_suggestion)
                ]

                for test_name, test_func in tests:
                    self.status_var.set(f"正在运行: {test_name}")
                    test_func()
                    time.sleep(0.5)  # 测试间隔

                # 生成测试报告
                self.generate_test_report()

            except Exception as e:
                self.log_result("测试套件", False, f"测试套件执行失败: {str(e)}")

            finally:
                self.status_var.set("所有测试完成")

        threading.Thread(target=run_tests, daemon=True).start()

    def load_real_dxf(self):
        """加载真实DXF文件进行测试"""
        file_path = filedialog.askopenfilename(
            title="选择DXF文件",
            filetypes=[("DXF files", "*.dxf"), ("All files", "*.*")]
        )

        if file_path:
            self.status_var.set("加载DXF文件...")
            self.progress.start()

            try:
                # 创建处理器
                self.processor = CADDataProcessor()

                # 这里需要实际的DXF加载逻辑
                # 由于CADDataProcessor可能没有直接的load_dxf方法，我们模拟加载
                messagebox.showinfo("提示", "DXF文件加载功能需要与主程序集成\n当前使用模拟数据进行测试")

                self.log_result("DXF文件加载", True, f"文件路径: {file_path}")

            except Exception as e:
                self.log_result("DXF文件加载", False, f"错误: {str(e)}")

            finally:
                self.progress.stop()
                self.status_var.set("就绪")

    def clear_results(self):
        """清除测试结果"""
        self.result_text.delete(1.0, tk.END)
        self.test_results.clear()
        self.status_var.set("测试结果已清除")

    def generate_test_report(self):
        """生成测试报告"""
        if not self.test_results:
            return

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['result'])
        failed_tests = total_tests - passed_tests

        report = f"""
{'='*60}
分组逻辑测试报告
{'='*60}
测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
总测试数: {total_tests}
通过测试: {passed_tests}
失败测试: {failed_tests}
成功率: {(passed_tests/total_tests*100):.1f}%

详细结果:
"""

        for result in self.test_results:
            status = "✅" if result['result'] else "❌"
            report += f"{status} {result['test']}\n"

        # 分析结果
        report += f"\n分析总结:\n"

        # 检查关键功能
        key_tests = {
            '图层识别': '图层识别测试',
            '墙体分组': '墙体分组测试',
            '门窗分组': '门窗分组测试',
            '墙体自动标注': '墙体自动标注测试',
            '门窗自动标注': '门窗自动标注测试'
        }

        for key_name, test_name in key_tests.items():
            test_result = next((r for r in self.test_results if r['test'] == test_name), None)
            if test_result:
                status = "✅ 正常" if test_result['result'] else "❌ 异常"
                report += f"- {key_name}: {status}\n"

        report += f"\n{'='*60}\n"

        self.result_text.insert(tk.END, report)
        self.result_text.see(tk.END)

    def run(self):
        """运行测试应用"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = GroupingLogicTest()
        app.run()
    except Exception as e:
        print(f"应用启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
