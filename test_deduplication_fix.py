#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试去重修复
验证修复后的去重逻辑是否正确工作
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_fixed_hash_logic():
    """测试修复后的哈希逻辑"""
    print("🧪 测试修复后的哈希逻辑...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 测试真实的实体数据（基于调试输出）
        test_entities = [
            # 实体1：垂直线段
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 342727.5,
                'start_y': 25051.0,
                'end_x': 342727.5,
                'end_y': 15031.0,
                'color': 256,
                'linetype': 'Continuous',
                'merged': True
            },
            # 实体2：水平线段
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 342727.5,
                'start_y': 15031.0,
                'end_x': 339439.5,
                'end_y': 15031.0,
                'color': 256,
                'linetype': 'Continuous',
                'merged': True
            },
            # 实体3：另一个垂直线段
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 339439.5,
                'start_y': 15031.0,
                'end_x': 339439.5,
                'end_y': 25051.0,
                'color': 256,
                'linetype': 'Continuous',
                'merged': True
            },
            # 实体4：真正的重复（与实体1相同）
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 342727.5,
                'start_y': 25051.0,
                'end_x': 342727.5,
                'end_y': 15031.0,
                'color': 256,
                'linetype': 'Continuous',
                'merged': True
            },
            # 实体5：方向相反但相同的线段（与实体1相同，但方向相反）
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 342727.5,
                'start_y': 15031.0,  # 起点和终点交换
                'end_x': 342727.5,
                'end_y': 25051.0,
                'color': 256,
                'linetype': 'Continuous',
                'merged': True
            }
        ]
        
        print(f"📋 测试实体: {len(test_entities)} 个")
        
        # 分析每个实体的哈希值
        hashes = []
        for i, entity in enumerate(test_entities):
            entity_hash = processor._get_entity_hash(entity)
            hashes.append(entity_hash)
            print(f"  实体{i+1}: {entity_hash}")
            print(f"    坐标: ({entity.get('start_x')}, {entity.get('start_y')}) -> ({entity.get('end_x')}, {entity.get('end_y')})")
        
        # 检查哈希分布
        unique_hashes = set(hashes)
        print(f"\n📊 哈希分析:")
        print(f"  总哈希数: {len(hashes)}")
        print(f"  唯一哈希数: {len(unique_hashes)}")
        
        # 统计每个哈希的出现次数
        hash_count = {}
        for h in hashes:
            hash_count[h] = hash_count.get(h, 0) + 1
        
        print(f"  哈希分布:")
        for h, count in hash_count.items():
            print(f"    {h}: {count} 次")
        
        # 测试去重
        print(f"\n🔧 测试去重功能:")
        unique_entities = processor._remove_duplicate_entities(test_entities)
        print(f"  期望结果: 保留3个不同的实体（实体1、2、3）")
        print(f"  实际结果: {len(unique_entities)} 个实体")
        
        # 验证结果
        expected_unique = 3  # 应该保留3个不同的实体
        success = len(unique_entities) == expected_unique
        
        if success:
            print(f"  ✅ 去重修复成功！")
        else:
            print(f"  ❌ 去重修复失败")
            print(f"    期望: {expected_unique} 个实体")
            print(f"    实际: {len(unique_entities)} 个实体")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_coordinate_formats():
    """测试多种坐标格式的一致性"""
    print("\n🔧 测试多种坐标格式的一致性...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 相同线段的不同格式表示
        same_line_entities = [
            # 格式1：start_x/start_y/end_x/end_y
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 0.0,
                'start_y': 0.0,
                'end_x': 100.0,
                'end_y': 0.0
            },
            # 格式2：points数组
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'points': [(0.0, 0.0), (100.0, 0.0)]
            },
            # 格式3：start/end数组
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start': (0.0, 0.0),
                'end': (100.0, 0.0)
            },
            # 格式4：方向相反的相同线段
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 100.0,
                'start_y': 0.0,
                'end_x': 0.0,
                'end_y': 0.0
            }
        ]
        
        print(f"📋 相同线段的不同格式: {len(same_line_entities)} 个")
        
        # 生成哈希
        hashes = []
        for i, entity in enumerate(same_line_entities):
            entity_hash = processor._get_entity_hash(entity)
            hashes.append(entity_hash)
            print(f"  格式{i+1}: {entity_hash}")
        
        # 检查一致性
        unique_hashes = set(hashes)
        print(f"\n📊 一致性检查:")
        print(f"  唯一哈希数: {len(unique_hashes)}")
        
        if len(unique_hashes) == 1:
            print(f"  ✅ 所有格式产生相同哈希（正确）")
            success = True
        else:
            print(f"  ❌ 不同格式产生不同哈希（问题）")
            print(f"  哈希值: {list(unique_hashes)}")
            success = False
        
        # 测试去重
        print(f"\n🔧 测试去重:")
        unique_entities = processor._remove_duplicate_entities(same_line_entities)
        print(f"  期望: 1 个实体（所有格式应该被认为是同一个实体）")
        print(f"  实际: {len(unique_entities)} 个实体")
        
        return success and len(unique_entities) == 1
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况...")
    
    try:
        from cad_data_processor import CADDataProcessor
        
        processor = CADDataProcessor()
        
        # 边界情况测试
        edge_case_entities = [
            # 正常实体
            {
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_x': 0.0,
                'start_y': 0.0,
                'end_x': 100.0,
                'end_y': 0.0
            },
            # 缺少坐标信息的实体
            {
                'type': 'LINE',
                'layer': 'A-WALL'
            },
            # 无效的实体
            {
                'type': 'LINE'
            },
            # 非字典实体
            'invalid_entity',
            # None实体
            None,
            # 其他类型实体
            {
                'type': 'CIRCLE',
                'layer': 'A-WALL',
                'center': (50, 50),
                'radius': 25
            }
        ]
        
        print(f"📋 边界情况测试: {len(edge_case_entities)} 个")
        
        # 测试哈希生成
        valid_entities = []
        for i, entity in enumerate(edge_case_entities):
            try:
                if entity is not None and isinstance(entity, dict):
                    entity_hash = processor._get_entity_hash(entity)
                    valid_entities.append(entity)
                    print(f"  实体{i+1}: {entity_hash}")
                else:
                    print(f"  实体{i+1}: 跳过无效实体")
            except Exception as e:
                print(f"  实体{i+1}: 哈希生成失败 - {e}")
        
        # 测试去重
        print(f"\n🔧 测试边界情况去重:")
        try:
            unique_entities = processor._remove_duplicate_entities(valid_entities)
            print(f"  输入: {len(valid_entities)} 个有效实体")
            print(f"  输出: {len(unique_entities)} 个实体")
            print(f"  ✅ 边界情况处理成功")
            return True
        except Exception as e:
            print(f"  ❌ 边界情况处理失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CAD分类标注工具 - 去重修复测试")
    print("=" * 60)
    
    tests = [
        ("修复后哈希逻辑测试", test_fixed_hash_logic),
        ("多种坐标格式一致性测试", test_multiple_coordinate_formats),
        ("边界情况测试", test_edge_cases)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    failed_tests = [name for name, success in results if not success]
    
    if failed_tests:
        print(f"\n⚠️ 失败的测试: {len(failed_tests)} 个")
        print("需要进一步调查这些问题")
    else:
        print("\n🎉 所有测试通过！")
        print("去重修复成功，现在应该能正确处理不同格式的实体了")
    
    print("=" * 60)
