#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试显示修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_enhanced import EnhancedCADProcessor
from cad_visualizer import CADVisualizer

def test_group_data_cleaning():
    """测试组数据清理功能"""
    print("🧪 测试组数据清理功能")
    print("="*60)
    
    # 创建处理器
    processor = EnhancedCADProcessor()
    
    # 创建包含问题的测试组
    problematic_group = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': '墙体'},
        "string_entity",  # 字符串实体
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'label': '墙体'},
        123,  # 数字
        None,  # None值
        {'layer': 'A-WALL'},  # 缺少type
        {}  # 空字典
    ]
    
    print(f"原始组数据: {len(problematic_group)} 个项目")
    for i, item in enumerate(problematic_group):
        print(f"  项目 {i+1}: {type(item)} - {str(item)[:50]}")
    
    # 测试数据清理
    cleaned_group = processor._clean_group_data(problematic_group)
    
    print(f"\n清理后组数据: {len(cleaned_group)} 个项目")
    for i, item in enumerate(cleaned_group):
        print(f"  项目 {i+1}: {type(item)} - {item.get('type')} - {item.get('layer')}")
    
    return len(cleaned_group) == 2  # 应该只剩下2个有效实体

def test_group_info_update():
    """测试组信息更新"""
    print(f"\n🧪 测试组信息更新")
    print("="*60)
    
    # 创建处理器
    processor = EnhancedCADProcessor()
    
    # 创建测试组数据
    test_groups = [
        # 墙体组（列表格式，自动标注）
        [
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': '墙体', 'auto_labeled': True},
            {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'label': '墙体', 'auto_labeled': True}
        ],
        # 门窗组（字典格式，自动标注）
        {
            'entities': [
                {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(50, -10), (50, 10)], 'label': '门窗', 'auto_labeled': True}
            ],
            'label': '门窗',
            'group_type': 'door_window',
            'layer': 'A-WINDOW',
            'status': 'auto_labeled',
            'confidence': 0.8
        },
        # 待处理组（列表格式，未标注）
        [
            {'type': 'LINE', 'layer': 'A-TEXT', 'points': [(200, 200), (300, 200)]}
        ]
    ]
    
    processor.all_groups = test_groups
    
    # 测试组信息更新
    processor._update_groups_info()
    
    print(f"组信息更新结果: {len(processor.groups_info)} 个组")
    
    for i, info in enumerate(processor.groups_info):
        print(f"  组 {i+1}:")
        print(f"    状态: {info.get('status')}")
        print(f"    标签: {info.get('label')}")
        print(f"    类型: {info.get('group_type')}")
        print(f"    图层: {info.get('layer')}")
        print(f"    实体数: {info.get('entity_count')}")
    
    # 验证结果
    expected_statuses = ['auto_labeled', 'auto_labeled', 'pending']
    expected_types = ['wall', 'door_window', 'other']
    
    success = True
    for i, info in enumerate(processor.groups_info):
        if info.get('status') != expected_statuses[i]:
            print(f"  ❌ 组 {i+1} 状态错误: 期望 {expected_statuses[i]}, 实际 {info.get('status')}")
            success = False
        if info.get('group_type') != expected_types[i]:
            print(f"  ❌ 组 {i+1} 类型错误: 期望 {expected_types[i]}, 实际 {info.get('group_type')}")
            success = False
    
    if success:
        print(f"  ✅ 组信息更新测试通过")
    
    return success

def test_visualizer_fixes():
    """测试可视化器修复"""
    print(f"\n🧪 测试可视化器修复")
    print("="*60)
    
    # 创建可视化器
    visualizer = CADVisualizer()
    
    # 创建包含问题的测试组
    problematic_group = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]},
        "string_entity",  # 这会导致错误
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)]},
        {'layer': 'A-WALL'},  # 缺少type
        {}  # 空字典
    ]
    
    print(f"测试问题组可视化: {len(problematic_group)} 个项目")
    
    try:
        visualizer.visualize_entity_group(problematic_group, {})
        print(f"  ✅ 问题组可视化成功（应该跳过无效实体）")
        return True
    except Exception as e:
        print(f"  ❌ 问题组可视化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_category_mapping():
    """测试类别映射"""
    print(f"\n🧪 测试类别映射")
    print("="*60)
    
    # 创建处理器
    processor = EnhancedCADProcessor()
    
    # 设置类别映射
    processor.category_mapping = {
        'wall': '墙体',
        'door_window': '门窗',
        'other': '其他'
    }
    
    # 测试类别映射获取
    test_types = ['wall', 'door_window', 'other', 'unknown']
    expected_results = ['墙体', '门窗', '其他', 'unknown']
    
    print(f"测试类别映射:")
    success = True
    
    for i, test_type in enumerate(test_types):
        if hasattr(processor, 'category_mapping') and processor.category_mapping:
            result = processor.category_mapping.get(test_type, test_type)
        else:
            result = test_type
        
        expected = expected_results[i]
        print(f"  {test_type} -> {result} (期望: {expected})")
        
        if result != expected:
            print(f"    ❌ 映射错误")
            success = False
        else:
            print(f"    ✅ 映射正确")
    
    return success

def main():
    """主测试函数"""
    print("🚀 开始测试显示修复效果")
    
    try:
        # 测试1: 组数据清理
        test1_success = test_group_data_cleaning()
        
        # 测试2: 组信息更新
        test2_success = test_group_info_update()
        
        # 测试3: 可视化器修复
        test3_success = test_visualizer_fixes()
        
        # 测试4: 类别映射
        test4_success = test_category_mapping()
        
        print(f"\n" + "="*60)
        print(f"📊 测试结果总结:")
        print(f"  组数据清理: {'✅ 通过' if test1_success else '❌ 失败'}")
        print(f"  组信息更新: {'✅ 通过' if test2_success else '❌ 失败'}")
        print(f"  可视化器修复: {'✅ 通过' if test3_success else '❌ 失败'}")
        print(f"  类别映射: {'✅ 通过' if test4_success else '❌ 失败'}")
        
        if all([test1_success, test2_success, test3_success, test4_success]):
            print(f"\n🎉 所有测试通过！显示问题已修复。")
            print(f"💡 现在程序应该能够正确显示:")
            print(f"   - 组列表中的墙体和门窗类型")
            print(f"   - CAD实体组预览")
            print(f"   - CAD实体全图概览中的所有组")
        else:
            print(f"\n⚠️ 部分测试失败，可能还需要进一步修复。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
