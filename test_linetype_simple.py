#!/usr/bin/env python3

"""
Simple linetype test
"""

def test_case_sensitivity():
    """Test case sensitivity fix"""
    
    print("Testing linetype case handling")
    print("=" * 40)
    
    # Old logic (before fix)
    def old_check(linetype_name):
        return linetype_name != 'CONTINUOUS'
    
    # New logic (after fix)
    def new_check(linetype_name):
        return linetype_name.upper() not in ['CONTINUOUS', 'BYLAYER', 'BYBLOCK']
    
    # Test cases
    test_cases = [
        'CONTINUOUS',
        'Continuous', 
        'continuous',
        'BYLAYER',
        'Bylayer',
        'BYBLOCK',
        'Byblock',
        'DASHED',
        'HIDDEN'
    ]
    
    print("Linetype test results:")
    print("Linetype      | Before | After  | Status")
    print("-" * 45)
    
    for linetype in test_cases:
        old_result = old_check(linetype)
        new_result = new_check(linetype)
        
        if linetype.upper() in ['CONTINUOUS', 'BYLAYER', 'BYBLOCK']:
            expected = False
            status = "OK" if new_result == expected else "FAIL"
        else:
            expected = True
            status = "OK" if new_result == expected else "FAIL"
        
        print(f"{linetype:12} | {str(old_result):6} | {str(new_result):6} | {status}")
    
    print("\nFix explanation:")
    print("- Before: Only checked 'CONTINUOUS' (uppercase)")
    print("- After: Checks 'CONTINUOUS', 'BYLAYER', 'BYBLOCK' (case insensitive)")
    print("- This correctly identifies 'Continuous' variants, avoiding false warnings")

if __name__ == "__main__":
    test_case_sensitivity()
